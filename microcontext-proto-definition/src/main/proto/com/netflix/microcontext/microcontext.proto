syntax = "proto3";

package com.netflix.microcontext;

import "com/netflix/proto/options/docs/api-docs.proto";
import "com/netflix/proto/options/server.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/wrappers.proto";
import "google/protobuf/struct.proto";
import "netflix/context/common/common.proto";
import "netflix/context/context.proto";
import "netflix/context/video/video.proto";
import "netflix/context/experimentation/experimentation.proto";

option java_multiple_files = true;
option java_package = "com.netflix.microcontext.protogen";
option java_outer_classname = "MicrocontextProto";

option (com.netflix.proto.options.server).name = "Microcontext";

service MicrocontextService {
  option (com.netflix.proto.options.docs.service) = {
    description: "Microcontext server"
  };

  rpc GetContext (GetContextRequest) returns (GetContextResponse) {
    option (com.netflix.proto.options.docs.operation) = {
      description: "Resolves the Microcontext"
    };
  }

  rpc EchoContext (EchoContextRequest) returns (EchoContextResponse) {
    option (com.netflix.proto.options.docs.operation) = {
      description: "Displays the current Microcontext"
    };
  }

  rpc DeserializeContext (DeserializeContextRequest) returns (GetContextResponse) {
    option (com.netflix.proto.options.docs.operation) = {
      description: "Deserializes the provided context. Use for DEBUG PURPOSES ONLY"
    };
  }

  rpc SerializeContext (SerializeContextRequest) returns (SerializeContextResponse) {
    option (com.netflix.proto.options.docs.operation) = {
      description: "Serializes the provided context. Use for DEBUG PURPOSES ONLY"
    };
  }

  rpc GetExperimentation (GetExperimentationRequest) returns (GetExperimentationResponse) {
    option (com.netflix.proto.options.docs.operation) = {
      description: "Resolves Experimentation. Use for DEBUG PURPOSES ONLY"
    };
  }
}

message GetContextRequest {
  reserved 5;
  google.protobuf.StringValue passport = 1  [(com.netflix.proto.options.docs.field) = {description: "The current passport"}];
  google.protobuf.StringValue ip_address = 2 [(com.netflix.proto.options.docs.field) = {description: "The ip address of the request"}];
  map<string, .netflix.context.common.StringList> headers = 3 [(com.netflix.proto.options.docs.field) = {description: "Request headers"}];
  map<string, .netflix.context.common.StringList> params = 4 [(com.netflix.proto.options.docs.field) = {description: "Request parameters"}];
  google.protobuf.FieldMask mask = 6 [(com.netflix.proto.options.docs.field) = {description: "Provide a mask if you don't want all the contexts. A mask MUST be supplied to receive any microconfig values"}];
}

message GetContextResponse {
  .netflix.context.Context context = 1;
  google.protobuf.FieldMask mask = 2 [(com.netflix.proto.options.docs.field) = {description: "Provide a mask if you don't want all the contexts"}];
}

message EchoContextRequest {
  .netflix.context.Context override = 1;
}

message DeserializeContextRequest {
  string serialized_context = 1;
}

message EchoContextResponse {
  .netflix.context.Context context = 1;
  google.protobuf.FieldMask mask = 2 [(com.netflix.proto.options.docs.field) = {description: "Provide a mask if you don't want all the contexts"}];
}

message SerializeContextRequest {
  .netflix.context.Context context = 1;
}

message SerializeContextResponse {
  string serialized_context = 1;
}

message GetExperimentationRequest {
  oneof microcontext {
    string serialized_context = 1;
    .netflix.context.Context context = 2;
  }
}

message GetExperimentationResponse {
  .netflix.context.experimentation.ExperimentationContext experimentation = 1;
}
