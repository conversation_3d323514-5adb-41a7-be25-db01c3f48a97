{"definitions": [{"protopath": "src:/:main:/:proto:/:com:/:netflix:/:microcontext:/:microcontext.proto", "def": {"messages": [{"name": "DeserializeContextRequest", "fields": [{"id": 1, "name": "serialized_context", "type": "string"}]}, {"name": "EchoContextRequest", "fields": [{"id": 1, "name": "override", "type": ".netflix.context.Context"}]}, {"name": "EchoContextResponse", "fields": [{"id": 1, "name": "context", "type": ".netflix.context.Context"}, {"id": 2, "name": "mask", "type": "google.protobuf.FieldMask", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "Provide a mask if you don't want all the contexts"}]}]}]}, {"name": "GetContextRequest", "fields": [{"id": 1, "name": "passport", "type": "google.protobuf.StringValue", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "The current passport"}]}]}, {"id": 2, "name": "ip_address", "type": "google.protobuf.StringValue", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "The ip address of the request"}]}]}, {"id": 6, "name": "mask", "type": "google.protobuf.FieldMask", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "Provide a mask if you don't want all the contexts. A mask MUST be supplied to receive any microconfig values"}]}]}], "maps": [{"key_type": "string", "field": {"id": 3, "name": "headers", "type": ".netflix.context.common.StringList", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "Request headers"}]}]}}, {"key_type": "string", "field": {"id": 4, "name": "params", "type": ".netflix.context.common.StringList", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "Request parameters"}]}]}}], "reserved_ids": [5]}, {"name": "GetContextResponse", "fields": [{"id": 1, "name": "context", "type": ".netflix.context.Context"}, {"id": 2, "name": "mask", "type": "google.protobuf.FieldMask", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "Provide a mask if you don't want all the contexts"}]}]}]}, {"name": "GetExperimentationRequest", "fields": [{"id": 1, "name": "serialized_context", "type": "string"}, {"id": 2, "name": "context", "type": ".netflix.context.Context"}]}, {"name": "GetExperimentationResponse", "fields": [{"id": 1, "name": "experimentation", "type": ".netflix.context.experimentation.ExperimentationContext"}]}, {"name": "SerializeContextRequest", "fields": [{"id": 1, "name": "context", "type": ".netflix.context.Context"}]}, {"name": "SerializeContextResponse", "fields": [{"id": 1, "name": "serialized_context", "type": "string"}]}], "services": [{"name": "MicrocontextService", "rpcs": [{"name": "DeserializeContext", "in_type": "DeserializeContextRequest", "out_type": "GetContextResponse", "options": [{"name": "(com.netflix.proto.options.docs.operation)", "aggregated": [{"name": "description", "value": "Deserializes the provided context. Use for DEBUG PURPOSES ONLY"}]}]}, {"name": "EchoContext", "in_type": "EchoContextRequest", "out_type": "EchoContextResponse", "options": [{"name": "(com.netflix.proto.options.docs.operation)", "aggregated": [{"name": "description", "value": "Displays the current Microcontext"}]}]}, {"name": "GetContext", "in_type": "GetContextRequest", "out_type": "GetContextResponse", "options": [{"name": "(com.netflix.proto.options.docs.operation)", "aggregated": [{"name": "description", "value": "Resolves the Microcontext"}]}]}, {"name": "GetExperimentation", "in_type": "GetExperimentationRequest", "out_type": "GetExperimentationResponse", "options": [{"name": "(com.netflix.proto.options.docs.operation)", "aggregated": [{"name": "description", "value": "Resolves Experimentation. Use for DEBUG PURPOSES ONLY"}]}]}, {"name": "SerializeContext", "in_type": "SerializeContextRequest", "out_type": "SerializeContextResponse", "options": [{"name": "(com.netflix.proto.options.docs.operation)", "aggregated": [{"name": "description", "value": "Serializes the provided context. Use for DEBUG PURPOSES ONLY"}]}]}]}], "imports": [{"path": "com/netflix/proto/options/docs/api-docs.proto"}, {"path": "com/netflix/proto/options/server.proto"}, {"path": "google/protobuf/field_mask.proto"}, {"path": "google/protobuf/wrappers.proto"}, {"path": "google/protobuf/struct.proto"}, {"path": "netflix/context/common/common.proto"}, {"path": "netflix/context/context.proto"}, {"path": "netflix/context/video/video.proto"}, {"path": "netflix/context/experimentation/experimentation.proto"}], "package": {"name": "com.netflix.microcontext"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_package", "value": "com.netflix.microcontext.protogen"}, {"name": "java_outer_classname", "value": "MicrocontextProto"}, {"name": "(com.netflix.proto.options.server).name", "value": "Microcontext"}]}}]}