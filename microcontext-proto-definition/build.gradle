apply plugin: 'com.netflix.grpc.proto-definition'
apply plugin: 'netflix.jvm-library'

dependencies {
    api project(':microcontext-model')
    api 'com.google.protobuf:protobuf-java'
    api 'io.grpc:grpc-protobuf'
    api 'io.grpc:grpc-stub'
    api 'netflix.grpc:netflix-grpc-options-proto-definition'
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(8)
    }
}


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations:latest.release'
}
