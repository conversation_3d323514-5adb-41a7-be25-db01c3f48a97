#!/bin/bash
set -efux

# Adds a build status in the Stash PR UI's Build tab that links to the doc
# preview for this PR. Only works on Rocket PR builds.
function add_preview_build_status() {
  newt pr set-build \
    --commit "${ROCKET_SHA}" \
    --build-key "${JOB_NAME}" \
    --build-name "microcontext Manuals docs preview #${BUILD_NUMBER}" \
    --build-state SUCCESSFUL \
    --build-url "https://go.netflix.com/man-preview/microcontext/pull-${ROCKET_PR_NUMBER}"
}

# Adds a code insight to the Stash PR UI that links to the doc preview for this
# PR. Only works on Rocket PR builds.
function add_preview_code_insight() {
  mkdir -p build
  newt expand-template \
    --input ./.netflix/manuals_preview_insight.tmpl \
    --param=DocName=microcontext \
    --param=DefaultBranch=main \
    --param="PreviewLinkText=go/man-preview/microcontext/pull-${ROCKET_PR_NUMBER}" \
    --param="PreviewLink=https://go.netflix.com/man-preview/microcontext/pull-${ROCKET_PR_NUMBER}" \
    --param="BuildLinkText=${JOB_NAME} #${BUILD_NUMBER}" \
    --param="BuildLink=${BUILD_URL}" \
    --left="<<" \
    --right=">>" \
    > ./build/manuals_preview_insight.json
  newt post-stash-insight \
    --commit "${ROCKET_SHA}" \
    --input-file ./build/manuals_preview_insight.json \
    --project dna \
    --repo microcontext \
    --report-key net.netflix.manuals.preview
}

# Returns a 0 status if there is already a Newt Manuals version published for
# this PR. Returns a 1 status otherwise.
function previously_published() {
  # Note this is a bit fragile, each version output has a space before the
  # newline.
  if newt version list | grep "mkdocs/pull-${ROCKET_PR_NUMBER}\s"; then
    return 0
  fi
  return 1
}

newt build

if [[ "${ROCKET_EVENT_TYPE}" == "PR" ]]; then
  # Only add the doc preview comment once per PR.
  if ! previously_published; then
    comment=1
  fi

  newt publish

  if [[ -v comment ]]; then
    readonly preview="[go/man-preview/microcontext/pull-${ROCKET_PR_NUMBER}](https://go.netflix.com/man-preview/microcontext/pull-${ROCKET_PR_NUMBER})"
    newt commentpr \
      --pr-id="${ROCKET_PR_NUMBER}" \
      --pr-comment="Docs preview published at: ${preview}. This will be automatically updated for every push to this PR. See the \`Builds\` tab for publishing status."
  fi
  add_preview_build_status
  add_preview_code_insight

elif [[ "${ROCKET_BRANCH}" == "main" ]]; then
  newt publish

  # PR preview versions aren't removed automatically, so attempt to clean them
  # up after the PR is merged.
  #
  # TODO(ROCK-1611): Rocket currently does not expose the source PR for a merge
  # as an env var. But it does print out where the commit to the main branch is
  # coming from in its logs. So we parse that as a hack for now even though it's
  # fragile. It's also okay if this fails, we just leak some doc versions that
  # don't get cleaned up.
  readonly pr="$(metatron curl -a jenkins "${JENKINS_URL_METATRON}"job/${JOB_NAME}/${BUILD_NUMBER}/consoleText | head -n 1 | sed -rn "s/.+\/pull-requests\/([[:digit:]]+)\/.+/\1/p")"
  if [[ -n "${pr}" ]]; then
    echo "Deleting docs preview version ${pr} as it's been merged..."
    newt versions delete --param=DOCS_SITE_VERSION=pull-"${pr}" || true
  fi
fi
