# Concepts
Microcontext will be initialized for every request that comes in via [Zuul](http://go/zuul).  

For requests that do not pass through Zuul we will need to configure a strategy to resolve Microcontext (see [manual](../guides/manual.md) and [testing](../guides/testing/java.md)) 

## Initialization
For initialization in Zuul we utilize a mix of [Headers and Parameters](../ref/headersandparameters.md), Query Parameters, Geo, Device and Passport information to resolve base data for Microcontext.  This utilizes information that is already available at the Zuul layer and does not make any datastore or service calls.

!!! Tip
    To distinguish what will be resolved by default we've marked Resolved fields as deprecated.  Do not access deprecated fields directly from the schema but defer to the specific resolver

## Resolvers
For context information that cannot be resolved via readily available request information we have Microcontext Resolvers.

Resolvers are:

* Easy (Zero argument)  
* Efficient (built-in multi-level request scoped cache)  

See [Resolvers](../guides/resolvers.md)
