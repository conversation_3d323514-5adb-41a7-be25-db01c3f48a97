
```mermaid
graph
  A[Device] --> B[Zuul];
  B -.-> |Subscriber Call| F[Subscriber];
  B -.-> |Initialize Microcontext| B;
  B --> |µ| C[BFG];
  C --> |µ| D[Gusto];
  D --> |µ| H[Domain Service 1];
  D --> |µ| J[Domain Service 2];
  C --> |µ| E[DGS 1];
  E --> |µ| K[Domain Service 3];
```
In Zuul we will process the inbound device headers and initialize the Microcontext.  This 
Microcontext will be passed down in all subsequent server calls and available for consumption.

Subscriber data is resolved for the current Authorized profile.  If you require subscriber data for
a different profile you will need to call Subscriber from your server logic.

Although this example uses GraphQL this would also apply to different calling paths that originate
from Zuul.
