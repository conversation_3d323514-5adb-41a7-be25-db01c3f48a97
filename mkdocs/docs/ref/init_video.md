# Video

## Context Preview Account

| Type      | Schema                          | Description                                                                                             |                                                 
|-----------|---------------------------------|---------------------------------------------------------------------------------------------------------|
| `Visitor` | `video.content_preview_account` | The profile id for content preview, if present and matches the existing user content preview is enabled |

Source: `RequestContext.getContext("ContentPreviewAccountId")`

## Blocked Proxy
| Type      | Schema                | Description                                  |                                                 
|-----------|-----------------------|----------------------------------------------|
| `Boolean` | `video.blocked_proxy` | Indicates if the request is on a blocked vpn |

Source: Not populated by default but users can update this value to override the value of the Geo Attribute `blocked_proxy`