# Visit

## Secure Port
| Type      | Schema                                 | Description |                                                 
|-----------|----------------------------------------|-------------|
| `boolean` | `context.visit.connection.secure_port` |             |

Parse the `x-forwarded-proto` and if the value is `https` set the secure port to `true`, if it does 
not match set the value to `false`.  The value will be unset if no header exists in the request.

## Request Time
| Type        | Schema                       | Description |                                                 
|-------------|------------------------------|-------------|
| `Timestamp` | `context.visit.request_time` |             |

Pulls the following value out of Request Context as a field `RequestTimestamp`
and populates the value if present

Code:
```
RequestContext.get("RequestTimestamp")
```

## Edge Time
| Type        | Schema                    | Description |                                                 
|-------------|---------------------------|-------------|
| `Timestamp` | `context.visit.edge_time` |             |

Pulls the following value out of Request Context as a Sub context `x-netflix.edge.server.timestamp`
and populates the value if present

Code:
```
RequestContext.getContext("x-netflix.edge.server.timestamp")
```

## Priority
| Type      | Schema                            | Description                                                                                                          |                                                 
|-----------|-----------------------------------|----------------------------------------------------------------------------------------------------------------------|
| `Integer` | `context.visit.priority.priority` | a computed priority (1-100) for a request, with 1 representing highest priority and 100 lowest (or unknown priority) |

Calculated in Zuul using the [Zuul to Device Protocol](https://docs.google.com/document/d/1V7M45WwJrX8NjfAGSBMe6487jTYAbjeQaJyt4gpIr3A/edit#bookmark=id.q8wcbxne4erq)

## Attempt
| Type      | Schema                           | Description                                 |                                                 
|-----------|----------------------------------|---------------------------------------------|
| `Integer` | `context.visit.priority.attempt` | the attempt number as specified by a device |

From the `x-netflix.request.attempt` header

## Category
| Type              | Schema                            | Description                  |                                                 
|-------------------|-----------------------------------|------------------------------|
| `RequestCategory` | `context.visit.priority.category` | the category of the request. |

If the RequestName is categorized as live `REQUEST_CATEGORY_LIVE`

If the passport AppType is GAMES `REQUEST_CATEGORY_GAMES`

If there is any other RequestName in the request `REQUEST_CATEGORY_STREAMING`

Otherwise set the category to `REQUEST_CATEGORY_UNKNOWN`

## App State
| Type       | Schema                             | Description |                                                 
|------------|------------------------------------|-------------|
| `AppState` | `context.visit.priority.app_state` |             |

We will parse the `x-netflix.request.client.context` header and parse the `<state>` out of the json parsed value with the following format ```{ "appState" : <state>, “reason”: <reason> }```

The value will be parsed using the following mapping:

| Header       | AppState               |
|--------------|------------------------|
| `foreground` | `APP_STATE_FOREGROUND` |
| `background` | `APP_STATE_BACKGROUND` |
| `idle`       | `APP_STATE_IDLE`       |
| `unknown`    | `APP_STATE_UNKNOWN`    |

## Device Memory Level
| Type     | Schema                              | Description                                                    |                                                 
|----------|-------------------------------------|----------------------------------------------------------------|
| `String` | `context.visit.device_memory_level` | Sent by samurai devices.  Possible values are `low` and `high` |

From the `x-netflix.devicememorylevel` header.  
!!!Note
    may be equivalent/related to the [memory_range](init_device.md#memory-range) value.

## User Agent
| Type        | Schema                     | Description                    |                                                 
|-------------|----------------------------|--------------------------------|
| `UserAgent` | `context.visit.user_agent` | The browser's User Agent value |

From the `User-Agent` [header](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/User-Agent). 

## Override IANA Timezone
| Type     | Schema                                 | Description                                                                                         |                                                 
|----------|----------------------------------------|-----------------------------------------------------------------------------------------------------|
| `String` | `context.visit.override_iana_timezone` | To be used be used as an override to the [iana_timezone](init_geo.md#IANA-Timezone) from GeoContext |

From the `x-netflix.request.client.timezoneid` header.

!!!Tip
    To access the override then the geo value you can use this utility method  `Optional<String> timezone = VisitUtils.getIanaTimezone();`

!!!Note
    must be a well-formed, otherwise will not be present.

## Load Test
| Type     | Schema                    | Description                                                                        |                                                 
|----------|---------------------------|------------------------------------------------------------------------------------|
| `String` | `context.visit.load_test` | Indicates if this request is synthetic traffic originating from a region load test |

If the `X-Netflix.region-load-test` header is present.
