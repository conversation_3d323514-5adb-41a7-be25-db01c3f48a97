# Geo

!!! Note

    GeoContext is not carried within the Microcontext proto payload, when calling Microcontext.getGeo() the library will pull the GeoData from the current thread context and return that value.  Thus the geo within the context proto is marked as deprecated yet the geo call from Microcontext is not.

# Country
| Type      | Schema                | Description                                                                                                     |                                                 
|-----------|-----------------------|-----------------------------------------------------------------------------------------------------------------|
| `Country` | `context.geo.country` | The ISO-3166-2 country code for the catalog country. |

Source: Geo Attribute `country_code`

!!! Tip

    This differs slightly from the `context.country` in that the context country could have 
    additional fallback values from headers or subscriber

# Real Country
| Type      | Schema                     | Description                              |                                                 
|-----------|----------------------------|------------------------------------------|
| `Country` | `context.geo.real_country` | Always populated and represents the ISO-3166-2 country code of the real physical country, which is sometimes different from the catalog country. Use this if you need to know where somebody physically is in the world. |

Source: Geo Attribute `real_country`

# Region Code
| Type     | Schema                    | Description                                                  |                                                 
|----------|---------------------------|--------------------------------------------------------------|
| `String` | `context.geo.region_code` | ISO-3166 code for the subdivision (such as a state in the US or province in Canada) |

Source: Geo Attribute `region_code`

# City
| Type     | Schema             | Description                        |                                                 
|----------|--------------------|------------------------------------|
| `String` | `context.geo.city` | the city |

Source: Geo Attribute `city`

# Zip
| Type     | Schema            | Description  |                                                 
|----------|-------------------|--------------|
| `String` | `context.geo.zip` | representative postal code for this city (not necessarily the postal code of this IP) |

Source: Geo Attribute `zip`

# IP Address
| Type        | Schema                   | Description                                        |                                                 
|-------------|--------------------------|----------------------------------------------------|
| `IpAddress` | `context.geo.ip_address` | the IP address for which this object contains data |

Source: Geo Attribute `ipaddress`

# Real IP Address
| Type        | Schema                        | Description                             |                                                 
|-------------|-------------------------------|-----------------------------------------|
| `IpAddress` | `context.geo.real_ip_address` | Only populated if ip_address overridden |

Source: Geo Attribute `real_ipaddress`

# ASN
| Type     | Schema            | Description                                                                                                                                                                                                                                |                                                 
|----------|-------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `String` | `context.geo.asn` | The autonomous system number of the request. On the internet, an ISP officially registers an ASN as a form of identification. This is the integer value of the ASN. |

Source: Geo Attribute `asnum`

# Blocked Proxy
| Type      | Schema                      | Description                       |                                                 
|-----------|-----------------------------|-----------------------------------|
| `Boolean` | `context.geo.blocked_proxy` | Indicates this is a blocked proxy |

Source: Geo Attribute `blocked_proxy`

# Fallback
| Type      | Schema                 | Description                     |                                                 
|-----------|------------------------|---------------------------------|
| `Boolean` | `context.geo.fallback` | Indicates if this is a fallback |

Source: Geo Attribute `is_fallback`

# Overriden
| Type      | Schema                  | Description                       |                                                 
|-----------|-------------------------|-----------------------------------|
| `Boolean` | `context.geo.overriden` | Indicates if the geo is overriden |

Source: Geo Attribute `is_override`

# Domain
| Type     | Schema               | Description                           |                                                 
|----------|----------------------|---------------------------------------|
| `String` | `context.geo.domain` | domain that the IP address belongs to |

Source: Geo Attribute `domain`

# IANA Timezone
| Type     | Schema                      | Description       |                                                 
|----------|-----------------------------|-------------------|
| `String` | `context.geo.iana_timezone` | The IANA Timezone |


Source: Geo Attribute `iana_timezone`

!!! Note
    Can be override by providing the `x-netflix.request.client.timezoneid` header see [VisitContext](init_visit.md#override-iana-timezone)

!!! Tip
    Can be converted to a java.time.ZoneId with this logic ```ZoneId.of(geoContext.getIanaTimezone())```

# Coordinates
| Type                 | Schema                    | Description                                     |                                                 
|----------------------|---------------------------|-------------------------------------------------|
| `google.type.LatLng` | `context.geo.coordinates` | the geo coordinates of the representative city for this IP. not necessarily the actual latitude and longitude of this IP |

Source: Geo Attribute `long` for longitude and `lat` for latitude each parsed into `double` values

# Company
| Type     | Schema                | Description                            |                                                 
|----------|-----------------------|----------------------------------------|
| `String` | `context.geo.company` | company that the IP address belongs to |

Source: Geo Attribute `company`

# Network Type
| Type     | Schema                     | Description                                           |                                                 
|----------|----------------------------|-------------------------------------------------------|
| `String` | `context.geo.network_type` | the network connection type the IP address belongs to |

Source: Geo Attribute `network_type`

# Extra Attributes
| Type                  | Schema                         | Description                                                    |                                                 
|-----------------------|--------------------------------|----------------------------------------------------------------|
| `Map<String, String>` | `context.geo.extra_attributes` | contains additional attributes that are not explicitly modeled |

Source: Geo Attributes `*`
