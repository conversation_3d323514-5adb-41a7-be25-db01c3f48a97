# Locales
| Type              | Schema            | Description                             |                                                 
|-------------------|-------------------|-----------------------------------------|
| `repeated Locale` | `context.locales` | List of locale preferences for the user |

*Type* `List of Locale`  
*Schema* `context.locales`  
*Interface* `Microcontext.getLocales()`

## Order of precedence:
1. [Headers](#headers)
2. [Primary Profile Language](#profile)
3. [Accept Language](#accept-language)
4. [Request Context](#request-context)
5. [Country default](#country-default)
6. [Last resort](#last-resort)

### Headers
We will search through the inbound request headers and find the first value from this list:

1. `x-netflix.context.locales`
2. `x-netflix.request.client.languages`
3. `language`

The header value contains comma delimited values which is parsed into a list

!!!Note
    We currently handle json lists as well but would strongly prefer a comma delimited value

### Profile
The value from Subscriber if available
`context.user.current_user.primary_language`

#### Prerequisites
1. The request has an authorized user (via passport)
2. The current user has been resolved (see [Resolvers](../guides/resolvers.md#user))

### Accept Language
Parse the accept-language header into a list (see [documentation](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Language))

### Request Context
The value of 

```java
// Import the RequestContext class
import com.netflix.server.context.RequestContext;

// Example of how to get the locale list from RequestContext
List<Locale> locales = RequestContext.getLocaleList();
```

### Country Default
Using the nfi18n library and the current Geo country we try and determine the supported locales using this method

```java
// Import the NFLocale class
import com.netflix.i18n.NFLocale;

// Get supported locales for a country
List<Locale> supportedLocales = NFLocale.getSupportedLocales(countryCode);
```

### Last Resort
We tried our best to find a locale from any available method but failed, in this case we set a value of `en`
