# Device

# Type
| Type                            | Schema                | Description |                                                 
|---------------------------------|-----------------------|-------------|
| `netflix.basicTypes.DeviceType` | `context.device.type` |             |

Originates from the DeviceType resolved in Zuul based on the [ESN](#esn)

# ESN
| Type     | Schema               | Description |                                                 
|----------|----------------------|-------------|
| `String` | `context.device.esn` |             |

Originates from the ESN in order of precedence:

1. From passport
2. From the following headers:
    - `x-netflix.esn`
    - `x-netflix.client.esn`
    - `x-netflix.client.ftl.esn`
3. From the following query parameters:
    - `esn` (legacy param used exclusively in APINext)
    - `e` (legacy param used mostly in APINext)
    - `fallbackEsn` (legacy param used in API and APINext)

!!! Tip
    The passport does not contain esn in certain scenarios in which this value will contain via header

## Hardware Major Category
| Type                       | Schema                                   | Description |                                                 
|----------------------------|------------------------------------------|-------------|
| `DseHardwareMajorCategory` | `context.device.hardware_major_category` |             |

Derived from the DCMS `HARDWARE_MAJOR_CATEGORY` property

## Client Platform
| Type                    | Schema                           | Description |                                                 
|-------------------------|----------------------------------|-------------|
| `DseClientPlatformName` | `context.device.client_platform` |             |

Derived from the DCMS `CLIENT_PLATFORM_NAME` property

## Support Level 
| Type                 | Schema                         | Description |                                                 
|----------------------|--------------------------------|-------------|
| `DeviceSupportLevel` | `context.device.support_level` |             |

Derived from the DCMS `SUPPORT_LEVEL` property

## Retired
| Type      | Schema                   | Description                                   |                                                 
|-----------|--------------------------|-----------------------------------------------|
| `boolean` | `context.device.retired` | Indicates whether a device is retired or not. |

Determined by Styx (aka Device Retirement Service). Fallback derived from the DCMS `RETIRED` property

## Retirement Message
| Type     | Schema                              | Description                        |
|----------|-------------------------------------|------------------------------------|
| `String` | `context.device.retirement_message` | Retirement message for the device. |

Determined by Styx. Will be empty for devices that are not retired or do not go through Zuul.

## Memory Range
| Type      | Schema                        | Description |                                                 
|-----------|-------------------------------|-------------|
| `integer` | `context.device.memory_range` |             |

Derived from the DCMS `UI_MEMORY_RANGE` property
