# Client

## App Version

| Type      | Schema                       | Description |                                                 
|-----------|------------------------------|-------------|
| `Version` | `context.client.app_version` |             |

### Headers

1. `x-netflix.context.app-version`
2. `x-netflix.client.appversion` (Legacy iOS)
3. `x-netflix.appver` (Legacy Android)
4. `x-netflix.context.nrd-app-version` (Legacy TVUI)
5. `x-netflix.uiversion` (Legacy Web)

### Parameters

1. `appversion` (Legacy iOS)
2. `nrdapp_version` (Legacy TVUI)
3. `ui_sem_ver` (Legacy)
4. `application_v` (Legacy random)
5. `uiversion` (Legacy Web)

## OS Version

| Type      | Schema                      | Description |                                                 
|-----------|-----------------------------|-------------|
| `Version` | `context.client.os_version` |             |

### Headers
1. `x-netflix.context.os-version`
2. `x-netflix.client.iosversion` (Legacy iOS)
3. `x-netflix.androidapi` (Legacy Android)
4. `x-netflix.osversion` (Legacy Web)
5. `x-xetflix.osversion` (Legacy Web Glitch)

### Parameters

1. `osversion` (Chromecast / Legacy Web)
2. `api` (Legacy Android)

## OS Name

| Type     | Schema                   | Description |                                                 
|----------|--------------------------|-------------|
| `String` | `context.client.os_name` |             |

### Headers

1. `x-netflix.context.os-name`
2. `x-netflix.osname` (Chromecast / Legacy Web)

### Parameters

1. `osname` (Chromecast / Legacy Web)

## Device Tier

| Type         | Schema                | Description                                                              |                                                 
|--------------|-----------------------|--------------------------------------------------------------------------|
| `DeviceTier` | `context.client.tier` | Indicates the device tier [Docs](https://go.netflix.com/tvdevicetiering) |

### Header

We will read following request headers:

1. `x-netflix.context.dt`

### User-Agent

If no header is provided (i.e. web) we will derive this based on the User-Agent.
See [Web Device Tiers](web_device_tier.md).

### Values

| Name        | Header Value | Description                                                                                   |
|-------------|--------------|-----------------------------------------------------------------------------------------------|
| INNOVATION  | 1            | Newer devices with the best in class experience and the main focus of experimentation         |
| FOLLOWER    | 2            | Simplified but fully supported experience targeting an aging pool of devices                  |
| MAINTENANCE | 3            | Still a supported experience but even older and more problematic devices with minimal updates |
| UNSUPPORTED | 4            | Typically these are retired but traffic may still be present for use cases such as web        |

!!! Note

    the name can also be provided in the header and will be parsed into the right value.  The numeric values exist to prevent these tiers from leaking

## SDK Version

| Type      | Schema                       | Description |                                                 
|-----------|------------------------------|-------------|
| `Version` | `context.client.sdk_version` |             |

### Headers

1. `x-netflix.context.sdk-version`

### Parameters

1. `sdk_version` (Legacy TVUI)
2. `nrdapp_version` (Legacy TVUI)
3. `currentNrdappVersion` (Legacy TVUI)

## Flavor

| Type           | Schema                         | Description |                                                 
|----------------|--------------------------------|-------------|
| `ClientFlavor` | `context.client.client_flavor` |             |

### Order of Precendence

We will select the first non empty value from the following sources

1. [Headers](#headers)
2. [Device](#device)
3. [Host](#host)

If any of the sources are empty we will default to `UNSPECIFIED`

#### Headers

We will search through the inbound request headers and find the first value from this list:

1. `x-netflix.context.ui-flavor`
2. `x-netflix.client.type`
3. `x-netflix.clienttype`

#### Parameters
Then the following query param values:

1. `clienttype` (Legacy Web)
2. `ab_ui_ver` (Legacy TVUI)

And map that to the following values:

| Header Value   | ClientFlavor                            |
|----------------|-----------------------------------------|
| `samurai`      | `ANDROID`                               |
| `uibootmember` | `DARWIN`                                |
| `*`            | String -> enum value for `ClientFlavor` |

#### Device

Based on the `clientPlatformCategory` for the Device Type we will do the following mapping:

| ClientPlatformCategory | ClientFlavor |
|------------------------|--------------|
| `APPLE`                | `ARGO`       |
| `SILVERLIGHT`          | `AKIRA`      |
| `HTML5`                | `AKIRA`      |
| `ANDROID_MOBILE`       | `ANDROID`    |
| `NRDP`                 | `TV_OTHER`   |

#### Host

We will search through the inbound request headers and find the first value from this list:

1. `x-netflix.client-host`
2. `host`

And perform the following mapping:

| Header Value            | ClientFlavor |
|-------------------------|--------------|
| `android(-.+)?.?(ngp)?` | `ANDROID`    |
| `ios(-.+)?.?(ngp)?`     | `ARGO`       |
| `web(-.+)?`             | `AKIRA`      |
| `nrdp(-.+)?`            | `TV_OTHER`   |

## Category

| Type             | Schema                           | Description |                                                 
|------------------|----------------------------------|-------------|
| `ClientCategory` | `context.client.client_category` |             |

!!! Tip

    We highly recommend using Client Category over Client Flavor as it is much more straightforward and mappable to the standard classification of Clients

To derive `ClientCategory` we first resolve the `ClientFlavor` and use the following mapping:

| ClientFlavor      | ClientCategory | Description               |
|-------------------|----------------|---------------------------|
| `AKIRA`           | `WEB`          |                           |
| `FAKIRA`          | `WEB`          |                           |
| `ANDROID`         | `ANDROID`      |                           |
| `TREX`            | `ANDROID`      | Native Android junior app |
| `ARGO`            | `IOS`          |                           |
| `IOS_LEGACY`      | `IOS`          |                           |
| `BUTTERFLY`       | `IOS`          | Native iOS junior app     |
| `ATV_FUJI`        | `ATV`          |                           |
| `ATV_HOPPER`      | `ATV`          |                           |
| `ATV_ECLIPSE`     | `ATV`          |                           |
| `DARWIN`          | `TV`           |                           |
| `TV_OTHER`        | `TV`           |                           |
| `ECLIPSE`         | `TV`           |                           |
| `WINDOWS_GOTHAM`  | `WIN`          |                           |
| `WINDOWS_PX`      | `WIN`          |                           |
| `WINDOWS_WILDCAT` | `WIN`          |                           |
| `DET`             | `UNSPECIFIED`  |                           |

## Form Factor

| Type               | Schema                              | Description |                                                 
|--------------------|-------------------------------------|-------------|
| `ClientFormFactor` | `context.client.client_form_factor` |             |

### Headers

1. `x-netflix.context.form-factor`
2. `x-netflix.client.idiom`
3. `x-netflix.deviceformfactor`

And perform the following mapping:

| Header   | ClientFormFactor |
|----------|------------------|
| `phone`  | `PHONE`          |
| `tablet` | `TABLET`         |
| `pad`    | `TABLET`         |

# Category Details

This will contain device category specific values for a given request

## Browser Category Details

### Browser Name

| Type     | Schema                                        | Description |                                                 
|----------|-----------------------------------------------|-------------|
| `String` | `context.client.browser_details.browser_name` |             |

### Headers

1. `x-netflix.browsername`

### Parameters

1. `browsername` (Legacy Web)

### Browser Version

| Type     | Schema                                           | Description |                                                 
|----------|--------------------------------------------------|-------------|
| `String` | `context.client.browser_details.browser_version` |             |

### Headers

1. `x-netflix.browserversion`

### Parameters

1. `browserversion` (Legacy Web)

## Android Category Details

### Installer Source

| Type     | Schema                                            | Description                                                                                                                          |                                                 
|----------|---------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------|
| `String` | `context.client.android_details.installer_source` | The installer source of our app in order to inform whether or not we can offer a signup flow or other potential upsell opportunities |

### Headers

1. `x-netflix.context.android.installer-source`

## TV Category Details

### NRD App Version

| Type      | Schema                                      | Description         |                                                 
|-----------|---------------------------------------------|---------------------|
| `Version` | `context.client.tv_details.nrd_app_version` | The NRD App Version |

### Parameters

1. `nrdapp_version`
2. `currentNrdappVersion`

### Headers

1. `x-netflix.context.sdk-version`

### UI Version

| Type      | Schema                                 | Description    |                                                 
|-----------|----------------------------------------|----------------|
| `Version` | `context.client.tv_details.ui_version` | The UI Version |

### Parameters

1. `ui_sem_ver`

### Headers

1. `x-netflix.context.app-version`
