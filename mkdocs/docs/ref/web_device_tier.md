# Web Device Tier

## Overview

The Web Device Tier system classifies client devices into different tiers based on their capabilities, versions, and other characteristics. This classification helps in providing the appropriate feature set and experience to different clients.

The system supports multiple client types:

- **In-app clients**: Native applications on mobile platforms
- **Mobile browsers**: Web browsers on mobile devices
- **Desktop browsers**: Web browsers on desktop platforms

## Device Tiers

Clients are classified into one of the following tiers:

| Tier            | Description                                                                                                                           |
|-----------------|---------------------------------------------------------------------------------------------------------------------------------------|
| **INNOVATION**  | Latest versions with full feature support. These clients receive the latest features and full support.                                |
| **MAINTENANCE** | Older but supported versions with core feature support. These clients receive core functionality but may not get the newest features. |
| **UNSUPPORTED** | Versions too old or platforms not officially supported. These clients may receive a limited experience or be redirected to upgrade.   |

## Tier Classification Logic

The classification logic is as follows:

- **Versions >= innovation threshold**: INNOVATION tier
- **Versions >= maintenance threshold but < innovation threshold**: MAINTENANCE tier
- **Versions < maintenance threshold**: UNSUPPORTED tier

> **Important**: All version comparisons are **inclusive**, meaning a browser with a version exactly matching a threshold will be included in the higher tier.

## Version Thresholds

The system maintains version thresholds for various client types:

- **In-app clients**: Thresholds for native applications on different platforms
- **Mobile browsers**: Thresholds for browsers on mobile operating systems
- **Desktop browsers**: Thresholds for browsers on desktop operating systems

Each browser or platform may have:
- An **innovation threshold** defining the minimum version for INNOVATION tier
- A **maintenance threshold** defining the minimum version for MAINTENANCE tier

Some clients may only have an innovation threshold, in which case versions below that threshold are classified as UNSUPPORTED (no MAINTENANCE tier).

## Resolution Process

The tier resolution process involves:

1. Extracting client information from request parameters, headers, and cookies
2. Determining the client type (in-app, mobile browser, desktop browser)
3. Validating version requirements for the specific client type
4. Applying appropriate tier logic based on version thresholds

### Client Type Detection

The system determines the client type based on the following criteria:

- **In-app clients**: Detected by the presence of valid device identifiers, in-app flags, or specific user agent patterns
- **Mobile browsers**: Non-in-app clients with mobile user agents
- **Desktop browsers**: Non-in-app clients with desktop user agents

## Updating Version Thresholds

The version thresholds are designed to be updated when browser support policies change. Follow these guidelines when updating version thresholds:

### Updating Process

1. Identify the browser/platform constant that needs updating
2. Update the constant value in [VersionThresholds.java](../../../microcontext-init/src/main/java/com/netflix/microcontext/init/resolvers/tiers/VersionThresholds.java) file with the new thresholds
3. Always ensure INNOVATION threshold is >= MAINTENANCE threshold for the same browser
4. Submit a PR and the team will review, merge and release a new version of Microcontext
5. The updated version will need to be pulled in by Zuul and deployed to production