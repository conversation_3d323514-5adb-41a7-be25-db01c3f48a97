# Auth

!!! Note

    AuthContext is not carried within the Microcontext proto payload, when calling Microcontext.getAuth() the library will pull the Passport from the current thread context and return that value

## Current Auth

### Customer ID
| Type   | Schema                          | Description     |                                                 
|--------|---------------------------------|-----------------|
| `Long` | `auth.current_auth.customer_id` | The customer id |

Source: `PassportIdentity.getProfileId()`

### Customer Guid
| Type     | Schema                            | Description       |                                                 
|----------|-----------------------------------|-------------------|
| `String` | `auth.current_auth.customer_guid` | The customer guid |

Source: `PassportIdentity.getProfileGuid()`

### Account Owner ID
| Type   | Schema                               | Description          |                                                 
|--------|--------------------------------------|----------------------|
| `Long` | `auth.current_auth.account_owner_id` | The account owner id |

Source: `PassportIdentity.getAccountId()`

### Account Owner GUID
| Type     | Schema                                 | Description            |                                                 
|----------|----------------------------------------|------------------------|
| `String` | `auth.current_auth.account_owner_guid` | The account owner guid |

Source: `PassportIdentity.getAccountGuid()`

## VDID
| Type     | Schema                   | Description                      |                                                 
|----------|--------------------------|----------------------------------|
| `String` | `auth.visitor_device_id` | The visitor device id (aka VDID) |

Source: `PassportIdentity.getVisitorDeviceId()`

## ESN
| Type     | Schema     | Description                            |                                                 
|----------|------------|----------------------------------------|
| `String` | `auth.esn` | The electronic serial number (aka ESN) |

Source: `PassportIdentity.getEsn()`

## Device Type
| Type                            | Schema             | Description     |                                                 
|---------------------------------|--------------------|-----------------|
| `netflix.basicTypes.DeviceType` | `auth.device_type` | The device type |

Source: `PassportIdentity.getDeviceTypeId()`

## App Type
| Type                              | Schema          | Description                     |                                                 
|-----------------------------------|-----------------|---------------------------------|
| `com.netflix.appregistry.AppType` | `auth.app_type` | The type of app for the request |

Source: `PassportIdentity.getAppTypeIndex()` and converted to the appregistry AppType


## App Id

| Type     | Schema        | Description                |
|----------|---------------|----------------------------|
| `String` | `auth.app_id` | The app id for the request |

Source: `PassportIdentity.getAppId()`