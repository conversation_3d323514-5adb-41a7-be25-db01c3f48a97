# Headers and Parameters

## Headers

This document provides a comprehensive index of all HTTP headers used in the Microcontext system, along with their types and descriptions.

| Header Name                                    | Description                                                                                             |
|------------------------------------------------|---------------------------------------------------------------------------------------------------------|
| `accept-language`                              | Standard HTTP accept-language header used in [Locales initialization](init_locales.md#accept-language)  |
| `host`                                         | Standard HTTP host header used in [Client Flavor initialization](init_client.md#host)                   |
| `language`                                     | Legacy language header used in [Locales initialization](init_locales.md#headers)                        |
| `x-forwarded-proto`                            | Forwarded protocol (http/https) used in [Secure Port initialization](init_visit.md#secure-port)         |
| `x-netflix.androidapi`                         | Legacy Android OS version used in [OS Version initialization](init_client.md#headers-1)                 |
| `x-netflix.appver`                             | Legacy Android app version used in [App Version initialization](init_client.md#headers)                 |
| `x-netflix.browsername`                        | Browser name used in [Browser Name initialization](init_client.md#headers-2)                            |
| `x-netflix.browserversion`                     | Browser version used in [Browser Version initialization](init_client.md#headers-3)                      |
| `x-netflix.client-host`                        | Client host used in [Client Flavor initialization](init_client.md#host)                                 |
| `x-netflix.client.appversion`                  | Legacy iOS app version used in [App Version initialization](init_client.md#headers)                     |
| `x-netflix.client.esn`                         | Client ESN used in [Device ESN initialization](init_device.md#esn)                                      |
| `x-netflix.client.ftl.esn`                     | FTL ESN used in [Device ESN initialization](init_device.md#esn)                                         |
| `x-netflix.client.idiom`                       | Legacy iOS form factor used in [Form Factor initialization](init_client.md#headers-4)                   |
| `x-netflix.client.iosversion`                  | Legacy iOS OS version used in [OS Version initialization](init_client.md#headers-1)                     |
| `x-netflix.client.type`                        | Legacy Argo UI flavor used in [Client Flavor initialization](init_client.md#headers-8)                  |
| `x-netflix.clienttype`                         | Legacy Android UI flavor used in [Client Flavor initialization](init_client.md#headers-8)               |
| `x-netflix.context.android.installer-source`   | Android installer source used in [Android Installer Source initialization](init_client.md#headers-5)    |
| `x-netflix.context.app-version`                | Application version used in [App Version initialization](init_client.md#headers)                        |
| `x-netflix.context.dt`                         | [Device tier](init_client.md#device-tier)                                                               |
| `x-netflix.context.feature-capabilities`       | Feature capabilities (comma-separated)                                                                  |
| `x-netflix.context.form-factor`                | Device form factor (phone, tablet, etc.) used in [Form Factor initialization](init_client.md#headers-4) |
| `x-netflix.context.interactive-originals`      | Interactive originals support                                                                           |
| `x-netflix.context.ixms.service-version`       | IXMS service version                                                                                    |
| `x-netflix.context.ixms.supported-experiences` | IXMS supported experiences                                                                              |
| `x-netflix.context.ixms.ui-spec-version`       | IXMS UI spec version                                                                                    |
| `x-netflix.context.locales`                    | Client locales used in [Locales initialization](init_locales.md#headers)                                |
| `x-netflix.context.localization-features`      | Localization features (comma-separated)                                                                 |
| `x-netflix.context.nrd-app-version`            | TVUI version used in [App Version initialization](init_client.md#headers)                               |
| `x-netflix.context.os-name`                    | OS name used in [OS Name initialization](init_client.md#headers-2)                                      |
| `x-netflix.context.os-version`                 | OS version used in [OS Version initialization](init_client.md#headers-1)                                |
| `x-netflix.context.sdk-version`                | SDK version used in [SDK Version initialization](init_client.md#headers-6)                              |
| `x-netflix.context.title-capabilities`         | Title capabilities (comma-separated)                                                                    |
| `x-netflix.context.ui-flavor`                  | UI flavor identifier used in [Client Flavor initialization](init_client.md#headers-8)                   |
| `x-netflix.deviceformfactor`                   | Legacy Android form factor used in [Form Factor initialization](init_client.md#headers-4)               |
| `x-netflix.esn`                                | Electronic Serial Number used in [Device ESN initialization](init_device.md#esn)                        |
| `x-netflix.osname`                             | Legacy web OS name used in [OS Name initialization](init_client.md#headers-2)                           |
| `x-netflix.osversion`                          | Legacy web OS version used in [OS Version initialization](init_client.md#headers-1)                     |
| `x-netflix.region-load-test`                   | Region load test flag used in [Load Test initialization](init_visit.md#load-test)                       |
| `x-netflix.request.client.languages`           | Legacy locale header used in [Locales initialization](init_locales.md#headers)                          |
| `x-netflix.request.client.timezoneid`          | Client timezone ID override used in [Override IANA Timezone](init_visit.md#override-iana-timezone)      |
| `x-netflix.uiversion`                          | Web UI app version used in [App Version initialization](init_client.md#headers)                         |
| `x-xetflix.osversion`                          | Legacy web OS version (misspelled) used in [OS Version initialization](init_client.md#headers-1)        |

## Query Parameters

This document provides a comprehensive index of all query parameters used in the Microcontext system, along with their types and descriptions.

| Parameter Name         | Description                                                                                                                                                    |
|------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `ab_ui_ver`            | AB UI version (Legacy TVUI) used in [Client Flavor initialization](init_client.md#parameters-9)                                                                |
| `api`                  | API level (Android) used in [OS Version initialization](init_client.md#parameters-1)                                                                           |
| `application_v`        | Application version used in [App Version initialization](init_client.md#parameters)                                                                            |
| `appversion`           | Application version used in [App Version initialization](init_client.md#parameters)                                                                            |
| `browsername`          | Browser name used in [Browser Name initialization](init_client.md#parameters-2)                                                                                |
| `browserversion`       | Browser version used in [Browser Version initialization](init_client.md#parameters-3)                                                                          |
| `clienttype`           | Client type (Legacy Web) used in [Client Flavor initialization](init_client.md#parameters-9)                                                                   |
| `currentNrdappVersion` | Current NRD version (TVUI) used in [SDK Version initialization](init_client.md#parameters-5) and [NRD App Version initialization](init_client.md#parameters-6) |
| `e`                    | Legacy ESN parameter (APINext) used in [Device ESN initialization](init_device.md#esn)                                                                         |
| `esn`                  | Legacy ESN parameter (APINext) used in [Device ESN initialization](init_device.md#esn)                                                                         |
| `fallbackEsn`          | Legacy fallback ESN (API and APINext) used in [Device ESN initialization](init_device.md#esn)                                                                  |
| `nrdapp_version`       | NRD app version (TVUI) used in [SDK Version initialization](init_client.md#parameters-5) and [NRD App Version initialization](init_client.md#parameters-6)     |
| `osname`               | OS name used in [OS Name initialization](init_client.md#parameters-2)                                                                                          |
| `osversion`            | OS version used in [OS Version initialization](init_client.md#parameters-1)                                                                                    |
| `sdk_version`          | SDK version used in [SDK Version initialization](init_client.md#parameters-5)                                                                                  |
| `ui_sem_ver`           | UI semantic version used in [UI Version initialization](init_client.md#parameters-8) and [TV Category Details](init_client.md#tv-category-details)             |
| `uiversion`            | UI version used in [App Version initialization](init_client.md#parameters)                                                                                     |

## Notes

Header and Query parameters are case-insensitive
