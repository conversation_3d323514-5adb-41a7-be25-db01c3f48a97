# Metrics

Microcontext emits some standard metrics that can be accessed from atlas/dashboards

## Caller

| Type      | Id                                     | Description                                                      |                                                 
|-----------|----------------------------------------|------------------------------------------------------------------|
| `Counter` | `microcontext.caller.interceptor.call` | Indicates if/how the Microcontext was communicated to the server |

### Dimensions

| Key      | Value            | Description                                                                      |                                                 
|----------|------------------|----------------------------------------------------------------------------------|
| `source` | `header`         | Microcontext arrived via the `x-netflix.microcontext` header                     |
| `source` | `requestContext` | Microcontext arrived via Request Context                                         |
| `source` | `server`         | Indicates we made a remote call to the Microcontext server to fetch Microcontext |
| `source` | `default`        | No Microcontext was available or resolved                                        |

## Missing Contexts

| Type      | Id                   | Description                                       |                                                 
|-----------|----------------------|---------------------------------------------------|
| `Counter` | `microcontext.error` | Indicates if values are missing from Microcontext |

### Dimensions

| Key                | Value        | Description                                     |                                                 
|--------------------|--------------|-------------------------------------------------|
| `missing_contexts` | `all`        | All contexts are missing (aka likely a default) |
| `missing_contexts` | `auth`       | AuthContext is missing                          |
| `missing_contexts` | `client`     | ClientContext is missing                        |
| `missing_contexts` | `country`    | Country is missing                              |
| `missing_contexts` | `device`     | DeviceContext is missing                        |
| `missing_contexts` | `geo`        | GeoContext is missing                           |
| `missing_contexts` | `video`      | VideoContext is missing                         |
| `missing_contexts` | `visit`      | VisitContext is missing                         |
| `missing_contexts` | `appVersion` | Client.appVersion is missing                    |

## Resolver

| Type      | Id                      | Description                                          |                                                 
|-----------|-------------------------|------------------------------------------------------|
| `Counter` | `microcontext.resolver` | Indicates that a Microcontext resolver has been used |

### Dimensions

| Key      | Value             | Description                        |                                                 
|----------|-------------------|------------------------------------|
| `action` | `experimentation` | ExperimentationResolver was called |
| `action` | `user`            | UserResolver was used              |

| Key      | Value      | Description                                  |                                                 
|----------|------------|----------------------------------------------|
| `result` | `cached`   | The resolver used data cached in the request |
| `result` | `compute`  | The resolver computed the resolver result    |
| `result` | `legacy`   | Specific to InteractiveResolver              |

## Access

| Type      | Id                    | Description                                                     |                                                 
|-----------|-----------------------|-----------------------------------------------------------------|
| `Counter` | `microcontext.access` | Indicates what values on the `Microcontext` class were accessed |

### Dimensions

| Key       | Value       | Description                 |                                                 
|-----------|-------------|-----------------------------|
| `context` | `auth`      | `getAuth()` was called      |
| `context` | `device`    | `getDevice()` was called    |
| `context` | `esn`       | `getEsn()` was called       |
| `context` | `proto`     | `toProto` was called        |
| `context` | `requestId` | `getRequestId()` was called |
| `context` | `user`      | `getUser()` was called      |
