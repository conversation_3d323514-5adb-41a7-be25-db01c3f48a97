# Country
| Type      | Schema            | Description |                                                 
|-----------|-------------------|-------------|
| `Country` | `context.country` |             |

This value is similar to the geo country however if geo is missing we will use some alternate sources to populate a fallback 

The first present value from the following sources

## Order of precendence:
1. [Geo](#geo)
2. [Request Context](#request-context)
3. [Recent Viewing Country](#recent-viewing-country)
4. [Signup Country](#signup-country)
5. [Registration Country](#registration-country)
6. [Last resort](#last-resort)

### Geo
The value from [GeoContext.getCountry()](init_geo.md#country)

### Request Context
The value from `CurrentRequestContext.getCountry()`

### Recent Viewing Country
If present the value from [UserContext.getRecentViewingCountry()](init_user.md#recent-viewing-country)

### Signup Country
If present the value from [UserContext.getSignupCountry()](init_user.md#signup-country)

### Registration Country
If present the value from [UserContext.getRegistrationCountry()](init_user.md#registration-country)

### Last Resort
If no other country can be determined we will use the value of `US` 🇺🇸
