# User

!!! Note

    UserContext is not carried within the Microcontext proto payload, when calling Microcontext.getUser() the library will pull the AccountProfileRemote from the current thread context and return that value

## Current User

### ID

| Type   | Schema                         | Description             |                                                 
|--------|--------------------------------|-------------------------|
| `Long` | `context.user.current_user.id` | The profile/customer id |

Source: `AccountProfileRemote.profileId`

### GUID

| Type     | Schema                           | Description               |                                                 
|----------|----------------------------------|---------------------------|
| `String` | `context.user.current_user.guid` | The profile/customer guid |

Source: `AccountProfileRemote.profileGuid`

### UCID

| Type     | Schema                           | Description |                                                 
|----------|----------------------------------|-------------|
| `String` | `context.user.current_user.ucid` |             |

Source: `AccountProfileRemote.ucid`

### Owner ID

| Type   | Schema                               | Description                           |                                                 
|--------|--------------------------------------|---------------------------------------|
| `Long` | `context.user.current_user.owner_id` | The profile/customer/account owner id |

Source: `AccountProfileRemote.accountOwnerId`

### Owner GUID

| Type     | Schema                                 | Description                             |                                                 
|----------|----------------------------------------|-----------------------------------------|
| `String` | `context.user.current_user.owner_guid` | The profile/customer/account owner guid |

Source: `AccountProfileRemote.accountOwnerId`

### Registration Country

| Type      | Schema                                           | Description |                                                 
|-----------|--------------------------------------------------|-------------|
| `Country` | `context.user.current_user.registration_country` |             |

Source: `AccountProfileRemote.countryOfRegistration`

### Signup Country

| Type      | Schema                                     | Description |                                                 
|-----------|--------------------------------------------|-------------|
| `Country` | `context.user.current_user.signup_country` |             |

Source: `AccountProfileRemote.countryOfSignup`

### Recent Viewing Country

| Type      | Schema                                             | Description |                                                 
|-----------|----------------------------------------------------|-------------|
| `Country` | `context.user.current_user.recent_viewing_country` |             |

Source: `AccountProfileRemote.recentViewingCountry`

### Primary Language

| Type     | Schema                                       | Description |                                                 
|----------|----------------------------------------------|-------------|
| `String` | `context.user.current_user.primary_language` |             |

Source: `AccountProfileRemote.primaryLang`

### Maturity Level

| Type      | Schema                                     | Description |                                                 
|-----------|--------------------------------------------|-------------|
| `Integer` | `context.user.current_user.maturity_level` |             |

Source: `AccountProfileRemote.maturityLevel`

### Profile Name

| Type     | Schema                                   | Description |                                                 
|----------|------------------------------------------|-------------|
| `String` | `context.user.current_user.profile_name` |             |

Source: `AccountProfileRemote.profileName`

### Plan ID

| Type   | Schema                              | Description |                                                 
|--------|-------------------------------------|-------------|
| `Long` | `context.user.current_user.plan_id` |             |

Source: `AccountProfileRemote.planId`

### Current Member

| Type      | Schema                                     | Description |                                                 
|-----------|--------------------------------------------|-------------|
| `Boolean` | `context.user.current_user.current_member` |             |

Source: `true` if `AccountProfileRemote.membership_status_enum` is `CURRENT_MEMBER`, `false`
otherwise

### Active or Hold

| Type      | Schema                                     | Description |                                                 
|-----------|--------------------------------------------|-------------|
| `Boolean` | `context.user.current_user.active_or_hold` |             |

Source:  `AccountProfileRemote.membership_status_enum`

Following mapping

| Enum                    | Value |
|-------------------------|-------|
| `NEVER_MEMBER`          | false |
| `FORMER_MEMBER`         | false |
| `CURRENT_MEMBER`        | true  |
| `FE_HOLD_MEMBER`        | true  |
| `ACCOUNT_HOLD_MEMBER`   | true  |
| `NON_REGISTERED_MEMBER` | false |

### Membership Status

| Type                | Schema                                        | Description |                                                 
|---------------------|-----------------------------------------------|-------------|
| `Membership.Status` | `context.user.current_user.membership_status` |             |

Source: `AccountProfileRemote.membership_status_enum`

### Experience Type

| Type              | Schema                                      | Description |                                                 
|-------------------|---------------------------------------------|-------------|
| `Experience.Type` | `context.user.current_user.experience_type` |             |

Source: `AccountProfileRemote.experience_type_enum`

### Tester

| Type      | Schema                                | Description |                                                 
|-----------|---------------------------------------|-------------|
| `Boolean` | `context.user.current_user.is_tester` |             |

Source: `true` if `AccountProfileRemote.tester_flags` contains `TESTER`

### Profile Creation Time

| Type        | Schema                                            | Description |                                                 
|-------------|---------------------------------------------------|-------------|
| `Timestamp` | `context.user.current_user.profile_creation_time` |             |

Source: `AccountProfileRemote.profile_creation_time`

### Autoplay Enabled

| Type      | Schema                                          | Description |                                                 
|-----------|-------------------------------------------------|-------------|
| `Boolean` | `context.user.current_user.is_autoplay_enabled` |             |

Source: `AccountProfileRemote.hasAutoPlayback`

### Content Preview

Removed

#### Alternate sources

##### ContentPreviewMigration

`ContentPreviewMigration.isCurrentUserContentPreview()` will evalue to `true` if the current
authenticated user matches the request contentpreviewid value

##### VideoContext

[video.content_preview_account](init_video.md#context-preview-account) and compare to [id](#id)

##### RequestContext

`RequestContext.getContext("ContentPreviewAccountId")` will be `true` if equals [id](#id)

### Profile Lock Pin Enabled

| Type      | Schema                                                   | Description |                                                 
|-----------|----------------------------------------------------------|-------------|
| `Boolean` | `context.user.current_user.has_profile_lock_pin_enabled` |             |

Source: `true` if `AccountProfileRemote.youthMaturityPinEnabled` is `true` or
`AccountProfileRemote.profile_access_pin` is set

### Pin Enabled

| Type      | Schema                                     | Description |                                                 
|-----------|--------------------------------------------|-------------|
| `Boolean` | `context.user.current_user.is_pin_enabled` |             |

Source: `true` if `AccountProfileRemote.youthMaturityPinEnabled` is `true`

### Fallback

| Type      | Schema                               | Description |                                                 
|-----------|--------------------------------------|-------------|
| `Boolean` | `context.user.current_user.fallback` |             |

Source: `AccountProfileRemote.isFallback`

Source:  `AccountProfileRemote.sop_type`

Following mapping

| Enum                                      | Value |
|-------------------------------------------|-------|
| `UNSPECIFIED`                             | 0     |
| `SOps_Internal_Automation`                | 1     |
| `SOps_External_Automation`                | 2     |
| `SOps_Internal_Manual`                    | 3     |
| `SOps_External_Manual`                    | 4     |
| `SOps_External_Demo`                      | 5     |
| `SOps_External_Screening`                 | 6     |
| `SOps_Internal_Screening`                 | 7     |
| `SOps_External_Game_Developer_Automation` | 8     |
| `SOps_External_Game_Developer_Manual`     | 9     |
| `SOps_External_Award`                     | 10    |
| `SOps_Load_Testing`                       | 11    |

### SOp Type

| Type           | Schema                               | Description |                                                 
|----------------|--------------------------------------|-------------|
| `SOpType.Type` | `context.user.current_user.sop_type` |             |

Source: `AccountProfileRemote.sop_type`