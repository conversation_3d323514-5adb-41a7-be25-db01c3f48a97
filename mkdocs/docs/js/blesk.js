var BLESK_APP_ID = "microcontext"
var BLESK_SCRIPT_URL = 'https://blesk.prod.netflix.net/static/js/blesk.js'

document.addEventListener('DOMContentLoaded', function(event) {
    var bleskContainer = document.createElement('div')
    bleskContainer.setAttribute('id', 'blesk')
    bleskContainer.setAttribute('data-appid', BLESK_APP_ID)
    // XXX you can add more attributes to this element to configure blesk.
    // See http://insight-docs.prod.netflix.net/blesk/#application-usage
    // for example, bleskContainer.setAttribute('data-subscribed-apps', 'foo')
    document.body.prepend(bleskContainer)

    var bleskScript = document.createElement('script')
    bleskScript.setAttribute('async', 'async')
    bleskScript.setAttribute('src', BLESK_SCRIPT_URL)
    document.body.appendChild(bleskScript)
})
