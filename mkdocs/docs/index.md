![logo.png](img/logo.png)

# Microcontext Overview

## Understanding the Challenge

Request data and how it gets populated in each request is varied in format and distributed throughout our systems. It is possible but bespoke to answer the following questions about each request:

_Which device was it?_  
_What's the client version?_  
_From which country did it originate?_  
_Who was the end-user?_  
_Were there any running experiments?_  

When creating a new service or use case, it isn't clear how to access common data. Service implementers face a daunting task on how to get started. This is typically solved either by asking for the data from the caller (thereby kicking the responsibility upstairs) or by copying the patterns of an existing service, which has already borne the burden of discovery.

## What is Microcontext?

*Microcontext* provides essential information like geo-location, device details, user-profile, video specifics, client data and experimentation, available right at your fingertips for every service request. It is:

- **Well-defined**: A clear, consistent model for request context data
- **Immutable**: Once created, the context doesn't change during a request
- **Comprehensive**: Contains all the common data needed for request processing
- **Efficient**: Resolved early and passed downstream to avoid redundant resolution

## Key Features

- **Automatic initialization** in Zuul for device requests
- **Propagation** to downstream services
- **Easy access** from any service in the request path
- **Extensible** with custom resolvers for additional data
- **Language support** for Java and NodeQuark

## Getting Started

Depending on your platform, you can get started with Microcontext by following these guides:

- [Java Integration Guide](starting/java.md)
- [NodeQuark Integration Guide](starting/nodequark.md)

## Documentation Structure

- **[Conceptual Information](concepts/index.md)**: Understand how Microcontext works
- **[Use Case Guides](guides/index.md)**: Learn how to use Microcontext for specific scenarios
- **[Reference Material](ref/headersandparameters.md)**: Detailed technical reference
- **[Support](support/faq.md)**: Get help and find answers to common questions
- **[Breaking Changes](support/breaking.md)**: Get help on how to migrate to the latest version

## Support

Need help? Here's how to reach us:

Slack: [#tli-support](https://netflix.enterprise.slack.com/archives/C04S66S0G64)  
Email: [TLI](mailto:<EMAIL>)
