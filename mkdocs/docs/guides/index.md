# Use Case Guides

This section provides detailed guides for common use cases and scenarios when working with Microcontext.

## Available Guides

### Core Functionality
- [**Resolvers**](resolvers.md): Learn how to use resolvers to access data not available in the initial context
- [**Debugging Requests**](debugging_requests.md): Techniques for debugging Microcontext in requests

### Special Use Cases
- [**Manual Initialization**](manual.md): How to use Microcontext in non-Zuul request scenarios
- [**Blocked Proxy Handling**](blocked_proxy.md): Working with blocked proxy detection and overrides

### Testing
- [**Java Testing**](testing/java.md): How to test applications using Microcontext in Java
- [**NodeQuark Testing**](testing/nodequark.md): How to test applications using Microcontext in NodeQuark

## When to Use These Guides

- **New to Microcontext?** Start with the [Getting Started](../starting/java.md) guides first
- **Building a service?** Check the [Resolvers](resolvers.md) guide to understand how to access additional context data
- **Debugging issues?** The [Debugging Requests](debugging_requests.md) guide will help you troubleshoot
- **Writing tests?** The testing guides provide platform-specific testing strategies

## Related Resources

- [Reference Documentation](../ref/headersandparameters.md): Detailed technical reference for headers, parameters, and initialization
- [FAQ](../support/faq.md): Answers to common questions about Microcontext
- [Conceptual Information](../concepts/index.md): Understanding how Microcontext works
