# Manual Microcontext Initialization

This guide explains how to set up and use Microcontext for requests that do not originate from Zuul. This includes:

- Offline batch processing
- Testing and development environments
- Ad-hoc debugging
- Command-line requests
- Any HTTP request without Zuul gateway involvement

## Understanding Non-Zuul Requests

Unlike regular device requests where Microcontext is automatically initialized in Zuul, requests without Zuul involvement require manual initialization. This is because:

1. There is no Zuul gateway to handle the initialization
2. Headers and query parameters typically used for initialization may not be available
3. The context needs to be created with specific values relevant to your use case

## Initialization Methods

### Basic Java Implementation for Batch Jobs

```java
import com.netflix.microcontext.MicrocontextInitializer;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.init.MicrocontextBuilder;
import com.netflix.lang.BindingContexts;
import netflix.context.Context;
import netflix.basicTypes.Country;

public class BatchJobExample {

    public void processBatch() {
        // Push a new binding context for this batch job
        BindingContexts.push();

        try {
            // Create a basic context with essential information
            Context context = Context.newBuilder()
                .setRequestId("batch-job-" + System.currentTimeMillis())
                .setCountry(Country.newBuilder().setId("US").build())
                // Add other required fields
                .build();

            // Initialize the context for the current thread
            CurrentMicrocontext.set(context);

            // Perform batch processing
            processItems();

        } finally {
            // Always clean up the binding context
            BindingContexts.pop();
        }
    }

    private void processItems() {
        // Your batch processing logic here
        // You can access the Microcontext using CurrentMicrocontext.get()
    }
}
```

### Using the Test Framework

For any non-Zuul request, you can use the test framework to create a more complete context. This is particularly useful for testing, development, and debugging:

```java
import com.netflix.microcontext.test.TestMicrocontext;
import com.netflix.microcontext.test.TestCurrentMicrocontext;
import com.netflix.lang.BindingContexts;
import netflix.context.Context;
import netflix.context.geo.GeoContext;
import netflix.basicTypes.Country;

public class NonZuulRequestExample {

    public void processRequest() {
        BindingContexts.push();

        try {
            // Create a test Microcontext with all needed data
            TestMicrocontext testContext = TestMicrocontext.builder()
                .setContext(Context.newBuilder()
                    .setRequestId("request-" + System.currentTimeMillis())
                    .build())
                .setGeo(GeoContext.newBuilder()
                    .setCountry(Country.newBuilder().setId("US").build())
                    .build())
                // Add other context data as needed
                .build();

            // Set the test context
            TestCurrentMicrocontext.set(testContext);

            // Process your request
            processRequest();

        } finally {
            BindingContexts.pop();
        }
    }

    private void processRequest() {
        // Your request processing logic here
    }
}
```

## Best Practices for Non-Zuul Requests

1. **Create a unique request ID** for each request to aid in debugging
2. **Set essential context data** that your processing requires (country, locale, etc.)
3. **Clean up resources** by popping the binding context when done
4. **Consider threading** - if your application uses multiple threads, each thread needs to handle the thread contexts appropriately
5. **Error handling** - implement proper error handling to ensure context cleanup even if processing fails

## Ad-hoc Debugging and Command-line Requests

For ad-hoc debugging or command-line requests, you can use serialized Microcontext values:

### Using Serialized Microcontext

```java
import com.netflix.microcontext.init.serializers.Serializers;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.lang.BindingContexts;
import netflix.context.Context;
import java.util.Optional;

public class CommandLineExample {
    public void executeWithSerializedContext(String serializedContext) {
        BindingContexts.push();

        try {
            // Deserialize the context
            Optional<Context> context = Serializers.fromRawString(serializedContext);

            if (context.isPresent()) {
                // Set the context
                CurrentMicrocontext.set(context.get());

                // Execute your logic
                executeRequest();
            }
        } finally {
            BindingContexts.pop();
        }
    }

    private void executeRequest() {
        // Your request processing logic here
    }
}
```

## Related Resources

- [Java Testing Guide](testing/java.md) - Contains more information about the test framework
- [NodeQuark Testing Guide](testing/nodequark.md) - Testing with NodeQuark
- [Resolvers Guide](resolvers.md) - Learn how to use resolvers in non-Zuul requests
- [Debugging Requests](../support/debugging.md) - Techniques for debugging Microcontext in ad-hoc and command-line requests
