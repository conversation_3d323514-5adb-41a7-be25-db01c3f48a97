# Java

Import this library into your test scope

```
testImplementation("com.netflix.microcontext:microcontext-test:latest.release")
```

## Unit Tests

Set the Microcontext values using `TestCurrentMicrocontext.set()` in combination with
`BindingContexts.push()` before setting the Microcontext and `BindingContexts.pop()` after the test
run.

```java
import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import netflix.context.Context;
import org.junit.*;

public class MyTest {

  @Before
  public void setUp() {
    BindingContexts.push();
  }

  @After
  public void tearDown() {
    BindingContexts.pop();
  }

  @Test
  public void TestPassport() {
    // As an example, set a microcontext with the some information.
    Context ctx = Context.newBuilder().setRequestId("fooo").build();

    // this method will store the Microcontext and test passport in request storage
    TestCurrentMicrocontext.set(
      TestMicrocontext.builder()
        .setContext(ctx)
        .setPassport(
          TestPassport.builder()
            .userInfo(
              TestUserInfo.builder()
                .customerId(123L)
                .customerGuid("some globally unique id")
                .build())
            .build())
        .setGeo(GeoContext.newBuilder().setCountry(Country.newBuilder().setId("CA")).build())
        .builder());
    // ... do some testing
    final Microcontext microcontext = CurrentMicrocontext.get();
    String requestId = microcontext.getRequestId(); // fooo
    Country country = microcontext.getCountry(); // id: CA
  }

  @Test
  public void TestAuthContext() {
    // As an example, set a microcontext with some information.
    Context ctx = Context.newBuilder().setRequestId("fooo").build();

    // the set method will use the builder auth parameter and translate it into a test passport and 
    // store the Microcontext and test passport in request storage
    final TestMicrocontext testMicrocontext =
      TestMicrocontext.builder()
        .setContext(ctx)
        .setAuthContext(
          AuthContext.newBuilder()
            .setCurrentAuth(
              Auth.newBuilder()
                .setCustomerId(123L)
                .setCustomerGuid("some globally unique id"))
            .build())
        .setGeo(GeoContext.newBuilder().setCountry(Country.newBuilder().setId("CA")).build())
        .build();
    TestCurrentMicrocontext.set(testMicrocontext);
    // ... do some testing
    final Microcontext microcontext = CurrentMicrocontext.get();
    String requestId = microcontext.getRequestId(); // fooo
    Country country = microcontext.getCountry(); // id: CA
  }
}
```

## Smoke Tests

From the TestMicrocontext you can obtain the set of expected headers based on the inputs via
the `asHeaders()` method. Take the response map and set those as headers on the outbound http
request.

!!! Note
Consult the spring
official [docs](https://manuals.netflix.net/view/java-testing/mkdocs/main/guides/testing-webmvc/)
for testing details

```java

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class SmokeTest {

  @Autowired
  private MockMvc mvc;

  @Test
  public void testIt() throws Exception {
    TestMicrocontext testMicrocontext = TestMicrocontext.builder(
        Context.newBuilder().setRequestId("fooo").build())
      .setGeo(GeoContext.newBuilder().setCountry(
        Country.newBuilder().setId("US")).build()).setPassport(
        TestPassport.builder()
          .deviceInfo(TestDeviceInfo.builder().deviceId("DID").build())
          .userInfo(TestUserInfo.builder().visitorDeviceId("VDID").build())
          .build().toPassportIdentity())
      .build();

    final MockHttpServletRequestBuilder builder = post("/path")
      .contentType(MediaType.APPLICATION_JSON)
      .content("{}");

    for (Entry<String, String> entry : testMicrocontext.asHeaders().entrySet()) {
      builder.header(entry.getKey(), entry.getValue());
    }

    mvc.perform(builder).andExpect(status().isOk());
  }
}
```

### With Thread Context

#### Call

```java

@Test
public void requestContextMakeCall() {
  final TestMicrocontext testMicrocontext =
    TestMicrocontext.of(
      Context.newBuilder()
        .setCountry(US)
        .setDevice(DeviceContext.newBuilder().setEsn("foobar"))
        .build());
  // Note: must handle exceptions
  SomeResponse r =
    testMicrocontext.call(
      () -> blockingStub.someEndpoint(SomeRequest.newBuilder().build()));
  assertEquals(US, r.getCountry());
  assertEquals("foobar", r.getValue());
}
```

#### Run

```java

@Test
public void requestContextRun() {
  final TestMicrocontext testMicrocontext =
    TestMicrocontext.of(
      Context.newBuilder()
        .setCountry(US)
        .setDevice(DeviceContext.newBuilder().setEsn("foobar"))
        .build());
  testMicrocontext.run(
    () -> {
      SomeResponse r = blockingStub.someEndpoint(SomeRequest.newBuilder().build());
      assertEquals(US, r.getCountry());
      assertEquals("foobar", r.getValue());
    });
}
```

#### MakeCall

```java

@Test
public void requestContextMakeCall() {
  final TestMicrocontext testMicrocontext =
    TestMicrocontext.of(
      Context.newBuilder()
        .setCountry(US)
        .setDevice(DeviceContext.newBuilder().setEsn("foobar"))
        .build());
  // Note: exceptions will be sneakily thrown
  SomeResponse r =
    testMicrocontext.makeCall(
      () -> blockingStub.someEndpoint(SomeRequest.newBuilder().build()));
  assertEquals(US, r.getCountry());
  assertEquals("foobar", r.getValue());
}
```

### Manual Header

Set the `x-netflix.microcontext` header which contains a serialized Microcontext. This approach is useful when:
- You need to test with a complex or specific Microcontext configuration
- You're debugging issues by reproducing exact production contexts
- You want to reuse the same context across multiple tests

!!! Note
Obtain an existing serialized Microcontext
from [these steps](../debugging_requests.md#obtaining-a-serialized-microcontext-value)

### gRPC Interceptor

If you are using gRPC you can use `TestMicrocontextInterceptor` to pass a header

```java
final TestMicrocontext testMicrocontext =
  TestMicrocontext.builder(Context.newBuilder().setRequestId("123").build()).build();
// Create a new stub with the test interceptor
FooServiceBlockingStub stubWithInterceptor = blockingStub
  .withInterceptors(new TestMicrocontextInterceptor(testMicrocontext));
// Use the new stub to make the call
GetFooResponse response = stubWithInterceptor.getFoo(GetFooRequest.newBuilder().build());
```

## Utilities

A utility class is provided that creates a default version of a Microcontext proto that can be used
for testing

```java
final Context.Builder basic = MicrocontextTest.basic();
System.out.println("Default microcontext "+basic.build());
```
