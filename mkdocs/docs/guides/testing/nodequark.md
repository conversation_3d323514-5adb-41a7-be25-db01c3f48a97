# Nodequark

The library provides utilities to help with testing microcontext in your applications:

## Decorating Requests
You can decorate a request object with microcontext data:

```typescript
import { decorateRequestWithMicrocontext } from '@netflix-internal/microcontext/testing';

describe('My Handler', () => {
  it('uses microcontext from request', () => {
    const request = {} as Request;
    const decoratedRequest = decorateRequestWithMicrocontext(request, {
      requestId: { value: "test-request-id" },
      country: {
        id: "US"
      }
    });

    // Now you can use decoratedRequest.getCurrentMicrocontext()
  });
});
```

## Mocking Request Context
For testing scenarios that require encoded microcontext data:

```typescript
import { getCurrentMicrocontextFromRequestContext } from '@netflix-internal/microcontext';
import { requestContextWithMicrocontext } from '@netflix-internal/microcontext/testing';
import { RequestContext } from "@nf-graph/types-external";


describe('My Handler', () => {
  it('handles encoded microcontext', async () => {
    const mockRequestContext : RequestContext = await requestContextWithMicrocontext({
      requestId: { value: "test-request-id" },
      country: { id: "US" },
    });

    // Verify the encoded context exists
    expect(mockRequestContext.getContext("microcontext").context).toBeDefined();

    // Or verify the decoded microcontext
    const decodedContext = getCurrentMicrocontextFromRequestContext(mockRequestContext);
    expect(decodedContext.requestId.value).toBe("test-request-id");
    expect(decodedContext.country.id).toBe("US");
  });
});
```