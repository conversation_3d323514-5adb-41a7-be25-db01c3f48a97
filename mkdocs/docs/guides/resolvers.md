Resolvers
=====
For data that is not resolved in the Gateway (typically marked as deprecated and commented in the schema) we provide an alternative mechanism to access via the Resolver pattern

The resolver will take responsibility for calling and caching the resolved results as well as updating the data within the Microcontext to be propagated downstream so the same values will not need to be resolved again

## Usage

Add the resolver artifact to your build.gradle

```gradle
implementation 'com.netflix.microcontext:microcontext-resolver:latest.release'
```

## Types
Each resolver will implement the `ContextResolver` interface:

```java
public interface ContextResolver<T extends Message> {

  CompletionStage<T> resolve();

  CompletionStage<T> resolve(Context context);

  CompletionStage<Context.Builder> resolve(Context.Builder builder);
}
```

### User
Class: `UserResolver`  
Type: `UserContext`

### Experimentation
Class: `ExperimentationResolver`  
Type `ExperimentationContext`

### Interactive
Class: `InteractiveResolver`  
Type `InteractiveContext`

## Example

```java
import com.netflix.microcontext.resolver.MicrocontextResolver;
import java.util.concurrent.CompletionStage;
import javax.inject.Inject;

public class MicrocontextResolverExample {

  private final MicrocontextResolver microcontextResolver;

  @Inject
  public MicrocontextResolverExample(MicrocontextResolver microcontextResolver) {
    this.microcontextResolver = microcontextResolver;
  }

  public CompletionStage<Boolean> doExperiment() {
    return microcontextResolver
        .resolveExperimentation()
        .thenApply(
            experimentationContext -> {
              // do something with experimentation
              return true;
            });
  }

  public CompletionStage<Boolean> doUser() {
    return microcontextResolver
        .resolveUser()
        .thenApply(
            userContext -> {
              // do something with user context
              if (userContext.hasCurrentUser()) {
                User user = userContext.getCurrentUser();
                // do something with user
              }
              return true;
            });
  }
}
```