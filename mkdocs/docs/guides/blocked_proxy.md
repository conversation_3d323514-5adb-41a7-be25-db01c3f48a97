# Blocked Proxy

## Background

The [Geo information](../ref/init_geo.md#blocked-proxy) contains an flag indicating if the IP address is associated with a blocked proxy (aka VPN).

## Overrides

For some use cases (i.e. testing) this data may be overriden to simulate the blocked proxy to override the authoratative geo information.  This data is stored in RequestContext and/or Microcontext.  All reading and writing of the overrides must consult all the possible sources of data or else we can have issues such as what occured [here](https://docs.google.com/document/d/1q6OEGvNe5-4RC354_9dH193pjXvRc3V46KqSxDNw97E/edit?tab=t.0#heading=h.4uv0cwuufztl)

The order of precedence is as follows:

1. Microcontext
2. RequestContext (deprecated)
3. Geo

All places that interact with the blocked proxy flag should consult all three sources of data to ensure the correct value is being used.  VMS has already been updated to read from all locations thus we need all code to be updated simiularly.

!!! Important

    Once we validate that all readers are consulting Microcontext for the override the RequestContext will be removed.

## Utilities

To faciliate proper interactions with the blocked proxy flag we have created a utility class to help with the reading and writing of the blocked proxy flag.  This will also manage the eventual deprecation and removal of the RequestContext aspects.

Make sure you have [imported](../starting/java.md#import-the-client) microcontext into your project.

Use the following patterns to interact with the blocked proxy flag

### Reading
This will return the blocked proxy flag from all contexts
```java
boolean isBlockedProxy = GeoMigration.getBlockedProxyAll();
```
### Writing
This will set the blocked proxy flag in all contexts
```java
boolean success = GeoMigration.setBlockedProxyAll(true);
```
### Clearing
This removes all overrides from contexts but leaves the Geo data intact
```java
boolean success = GeoMigration.clearBlockedProxyAll();
```
!!! Note

    There are some alternative methods to interact with the blocked proxy flag that are cover some corner cases such as explicitly providing the RequestContext for unit tests.  Please consult the utility class for the details and/or reach out support.