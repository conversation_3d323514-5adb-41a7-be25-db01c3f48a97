# Nodequark


## Installation
If you're using Nodequark `>= 11.15.0`, the Microcontext plugin is automatically installed and enabled. If you're using an older version, you can install the plugin manually:

```bash
npm install @netflix-internal/microcontext
```

## Usage

### Nodequark Plugin

As of Nodequark version 11.15.0 (which you can check on your `nodequark.json` file), the Microcontext plugin is automatically installed and enabled. This plugin provides a middleware that will automatically decorate (i.e. deserialize) the request object with the microcontext data.
Depending on the types used by your app, you'll want to import the `Microcontext` type directly from nodequark platform, or from `@nf-graph/types-external` package.

Then you can simply access the microcontext object from the request object:
```typescript
    import { Request } from '@nf-graph/types-external'; // this could also be NQ type
    import { Microcontext } from '@nf-graph/types-external'; // this could also be NQ type
    const microcontext: Microcontext = req.getCurrentMicrocontext();
    const requestId = microcontext.requestId?.value;
    const country = microcontext.country?.id;
    
    let response = {
        requestId,
        country
    };
```

### Middleware
The recommended way to use Microcontext is to use the provided middleware in your application.

Add the middleware to your middleware chain:

```typescript
// In your middleware configuration
import { microcontextMiddleware } from '@netflix-internal/microcontext';

module.exports = [
  // ... other middleware
  microcontextMiddleware,
  // ... other middleware
];

// In your route handler (e.g., helloWorldGet.js)
function handler(req, res, next) {
  const microcontext = req.getCurrentMicrocontext();
  
  const requestId = microcontext.requestId?.value;
  const country = microcontext.country?.id;
  
  res.send(200, { message: "Hello World" });
  next();
}

module.exports = handler;
```

### Direct Access

You can also get the microcontext directly from a request object or requestContext object:

```typescript
import { getCurrentMicrocontext, getCurrentMicrocontextFromRequestContext } from '@netflix-internal/microcontext';
import { Request, RequestContext } from '@nf-graph/types-external';

// From Request object
async function handlerWithRequest(req: Request, res, next) {
  try {
    const microcontext = getCurrentMicrocontext(req);
    
    // Access microcontext properties
    const requestId = microcontext.requestId?.value;
    const country = microcontext.country?.id;
    
    res.send(200, { success: true });
    next();
  } catch (error) {
    next(error);
  }
}

// From RequestContext object
async function handlerWithRequestContext(requestContext: RequestContext) {
  try {
    const microcontext = getCurrentMicrocontextFromRequestContext(requestContext);
    
    // Access microcontext properties
    const requestId = microcontext.requestId?.value;
    const country = microcontext.country?.id;
    
    return microcontext;
  } catch (error) {
    throw error;
  }
}
```