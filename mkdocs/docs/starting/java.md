# Java Integration Guide

This guide explains how to integrate and use Microcontext in Java applications.

## Setup

### Import Dependencies

Add the following dependency to your build.gradle file:

```gradle
implementation("com.netflix.microcontext:microcontext-access:latest.release")
```

For testing, add:

```gradle
testImplementation("com.netflix.microcontext:microcontext-test:latest.release")
```

If you need to use resolvers, add:

```gradle
implementation("com.netflix.microcontext:microcontext-resolver:latest.release")
```

## Accessing Microcontext

There are multiple ways to access Microcontext in your Java application:

### Method 1: Static Access

The simplest way to get the Microcontext for a request is to use the globally scoped accessor `CurrentMicrocontext.get()`.

```java
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;

public class MyController {
  public void handleRequest() {
    Microcontext mc = CurrentMicrocontext.get();

    // Access context data
    String requestId = mc.getRequestId();
    String country = mc.getCountry().getId();

    // Log or use the data
    logger.info("Processing request {} from {}", requestId, country);
  }
}
```

### Method 2: Dependency Injection

You can also inject a `MicrocontextManager` into your components:

```java
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.MicrocontextManager;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

@Component
public class MicrocontextManagerExample {

  private final MicrocontextManager manager;

  @Autowired
  public MicrocontextManagerExample(MicrocontextManager manager) {
    this.manager = manager;
  }

  public void doSomething() {
    final Microcontext context = manager.getContext();
    // Use context data
  }
}
```

## Common Operations

### Accessing Basic Information

```java
Microcontext mc = CurrentMicrocontext.get();

// Request information
String requestId = mc.getRequestId();

// Geo information
Country country = mc.getCountry();
String countryId = country.getId();

// Device information
DeviceContext device = mc.getDevice();
String esn = device.getEsn();
DeviceType deviceType = device.getType();

// Client information
ClientContext client = mc.getClient();
String appVersion = client.getAppVersion();
```

### Using Resolvers for Additional Data

For data not available in the initial context, use resolvers:

```java
import com.netflix.microcontext.resolver.MicrocontextResolver;
import java.util.concurrent.CompletionStage;

@Component
public class MyService {
    private final MicrocontextResolver resolver;

    @Autowired
    public MyService(MicrocontextResolver resolver) {
        this.resolver = resolver;
    }

    public CompletionStage<String> getUserInfo() {
        return resolver.resolveUser()
            .thenApply(userContext -> {
                if (userContext.hasCurrentUser()) {
                    return userContext.getCurrentUser().getName();
                }
                return "Unknown User";
            });
    }
}
```

## Next Steps

- Learn about [testing with Microcontext](../guides/testing/java.md)
- Explore [resolvers](../guides/resolvers.md) for accessing additional context data
- See the [reference documentation](../ref/headersandparameters.md) for detailed information about available fields
