# Debugging Requests

## Obtaining a serialized Microcontext value

### From an existing request
1. Obtain an existing request (i.e. via [<PERSON>](http://go/raven))
2. Get the value of the `x-netflix.request.sub.context.microcontext` header (Note: the name of this header may change in the future)  
Example: ```x-netflix.request.sub.context.microcontext: 1|com.netflix.microcontext.init.requestcontext.ContextContextSerializer|0|CgUKA2Zvbw==```
3. Strip out the preamble `1|com.netflix.microcontext.init.requestcontext.ContextContextSerializer|0|`
### From the Microcontext gRPC endpoint
1. Navigate to the Microcontext gRPC Web UI [site](https://microcontext.cluster.us-east-1.test.cloud.netflix.net:9443/)
2. Select `SerializeContext` as the Method Name  
![img_1.png](img_1.png)
3. Populate values in the `context` parameter  
![img_3.png](img_3.png)
4. Hit the `invoke` button
5. Grab the value from the `serialized_context` response  
![img_2.png](img_2.png)
### Programmatically
#### Java
``` java
import com.netflix.microcontext.init.serializers.Serializers;
import netflix.context.Context;

String microcontext = Serializers.toString(Context.newBuilder().setRequestId("foo").build());
```
#### Nodequark
On Nodequark you can directly serialize and decorate a request by using the following helpers mentioned in [Nodequark's Mocking Request Context docs](../guides/testing/nodequark.md#mocking-request-context)

### Deserializing a Microcontext value
The Microcontext String representation is Base64 encoded version of the protobuf byte value for `netflix.context.Context`.  It may be possible to use generic decoding utilities if you want to quickly decode the string value.


## Using in Swagger
Set the following property in your server configuration (i.e. `application.properties` or `application.yml`)
```
grpc.swagger.enableMicrocontextHeader=true
```
!!! Important
    This requires a version of `1.55.6+` for `netflix.grpc:netflix-grpc-swagger` and for `com.netflix.spring:spring-boot-netflix-grpc-swagger` a version of `2.7.82+` (SBN2) or `3.1.18+` (SBN3) 

This will unlock a header value for Microcontext in Swagger UI  
![img.png](img.png)

Place the serialized Microcontext value from the above steps into the `x-netflix.microcontext` header field in `x-netflix.microcontext` field

### Bonus: Passport in Swagger
Set the following property in your server configuration (i.e. `application.properties` or `application.yml`)
```
grpc.swagger.enablePassportHeader=true
```

This will unlock a header value for Passport in Swagger UI
![img_5.png](img_5.png)

## Using in GRPC UI
Add a value in `Request Metadata` with a name of `x-netflix.microcontext` and value using the serialized Microcontext
![img_4.png](img_4.png)

## Using in Curl

```commandline
metatron curl -a microcontext -X 'POST' \
  'https://microcontext.cluster.us-east-1.test.cloud.netflix.net:8443/grpc/microcontext/com.netflix.microcontext.MicrocontextService/EchoContext' \
  -H 'x-netflix.microcontext: CgUKA2Zvbw==' \
  -d '{}'
```
