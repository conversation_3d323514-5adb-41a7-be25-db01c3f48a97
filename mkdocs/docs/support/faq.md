# Frequently Asked Questions

This page provides answers to common questions about Microcontext. If you don't find an answer to your question, please reach out to the support team via [Slack](https://netflix.enterprise.slack.com/archives/C04S66S0G64) or [email](mailto:<EMAIL>).

## General Questions

### What is Microcontext and why should I use it?

Microcontext is a well-defined, immutable context model containing expected request data. It's resolved in the earliest stage of request execution and passed to downstream services. You should use it because:

- It provides a consistent way to access common request data
- It eliminates the need to pass the same data between services repeatedly
- It's resolved early in the request lifecycle, improving performance
- It provides a unified model across different platforms and services

### How is Microcontext initialized?

For device requests, Microcontext is automatically populated in Zuul and propagated downstream to each service in the call path. For other requests (testing, batch processing), you need to initialize Microcontext manually. See the [initialization guide](../concepts/initialization.md) for details.

### Which languages/platforms are supported?

Microcontext currently supports:
- Java
- NodeQuark

See the [Getting Started](../starting/java.md) guides for platform-specific integration instructions.

### How do I access data that's not in the initial context?

For data not available in the initial context (marked as deprecated in the schema), you need to use [Resolvers](../guides/resolvers.md). Resolvers provide a way to access additional data that requires service calls or complex resolution logic.

## Technical Questions

### Why is AuthContext removed from Context?

The preference of the Consumer Identity team is to use [Passport](http://go/man/passport) directly vs using the AuthContext provided by Microcontext. There are certain use cases in which the passport may become out of sync with the value provided in Microcontext thus it is considered safer to use Passports.

We recommend you migrate any usages of Microcontext Auth to use Passport directly however to assist in ease of migration we have created some constructs to help aid/ease the transition:

#### Testing

Netflix lacks a cohesive test setup framework and in lieu of that some applications had aligned on using Microcontext to bridge that gap. Unfortunately now that Auth is removed from Microcontext you can no longer use it for Authentication needs. For ease of retrofitting these use cases we've introduced a test Microcontext framework that takes care of setting the relevant data in the request for testing purposes.

Old:
```java
// This code will not compile with current versions
import netflix.context.Context;
import netflix.context.auth.AuthContext;
import netflix.context.auth.Auth;
import com.netflix.microcontext.access.server.CurrentMicrocontext;

public class OldExample {
    public void setupContext() {
        final Context context =
            Context.newBuilder().setRequestId("123").build()
                .setAuth( // this will not compile 💀
                    AuthContext.newBuilder()
                        .setCurrentAuth(Auth.newBuilder().setCustomerId(456L))
                        .build())
                .build();
        CurrentMicrocontext.set(context);
    }
}
```

New:
```java
import netflix.context.Context;
import netflix.context.auth.AuthContext;
import netflix.context.auth.Auth;
import com.netflix.microcontext.test.TestMicrocontext;
import com.netflix.microcontext.test.TestCurrentMicrocontext;

public class NewExample {
    public void setupContext() {
        final TestMicrocontext testMicrocontext =
            TestMicrocontext.builder()
                .setContext(Context.newBuilder().setRequestId("123").build())
                .setAuthContext(
                    AuthContext.newBuilder()
                        .setCurrentAuth(Auth.newBuilder().setCustomerId(456L))
                        .build())
                .build();
        TestCurrentMicrocontext.set(testMicrocontext);
    }
}
```

See the [testing](../guides/testing/java.md) section for more details

#### Access

To aid migration (and to reduce breakages) we retrofitted the Microcontext.getAuth() method to derive the AuthContext from the current request-scoped Passport. This mechanism will continue to work for the short/medium term but long term it is recommended we migrate to the direct Authorization representation owned by the Consumer Authenticate team ([CCA](http://go/cca))

### Why is User marked as deprecated?

Subscriber information is stored in RequestContext, initially Microcontext also carried this data in the form of UserContext however to reduce dual transmission of the data and to reduce the size and cost to transmit Microcontext we stopped populating UserContext and instead read from the RequestContext when Microcontext.getUser() is invoked. Eventually context.user will be removed from the schema.

### How do I handle Microcontext in multi-threaded applications?

Microcontext is stored in thread-local storage, so each thread needs its own context. When spawning new threads or using thread pools, you need to:

1. Get the current Microcontext before creating the new thread
2. Pass it to the new thread
3. Set it in the new thread before performing any operations

See the [Batch Processing](../guides/batch.md) guide for examples of handling Microcontext in multi-threaded environments.

### What's the performance impact of using Microcontext?

Microcontext is designed to be lightweight and efficient. The initial resolution happens once at the edge, and the context is then passed along with requests. This is more efficient than having each service resolve the same information independently.

For performance-critical paths, you can monitor Microcontext metrics (see [Metrics Reference](../ref/metrics.md)) to ensure it's not causing any bottlenecks.

## Troubleshooting

### How can I debug Microcontext issues?

See the [Debugging Requests](../guides/debugging_requests.md) guide for detailed information on debugging Microcontext issues, including:

- How to obtain a serialized Microcontext value
- How to use Microcontext in Swagger UI
- How to use Microcontext in gRPC UI
- How to use Microcontext in curl commands

## Migration and Compatibility

### How do I migrate from legacy context mechanisms to Microcontext?

If you're migrating from a legacy context mechanism:

1. Add the Microcontext dependencies to your project
2. Replace direct access to headers, query parameters, or other context sources with Microcontext access
3. For testing, use the Microcontext test framework instead of manually setting headers or context values

See the [Getting Started](../starting/java.md) guides for platform-specific integration instructions.

### Will my existing code break if I upgrade Microcontext?

Microcontext follows semantic versioning, so:

- Major version changes may include breaking changes
- Minor and patch versions should be backward compatible

When fields are deprecated, they typically continue to work through compatibility layers for a transition period before being removed. Always check the release notes when upgrading to a new major version.
