# Breaking Changes

## Version 1.172.0
- **InteractiveResolver Deprecation**: The entire `InteractiveResolver` class has been deprecated and its implementation has been gutted. All methods now return empty or default values instead of providing actual functionality.
  - The public constructor for `InteractiveResolver` has changed, which will impact any code extending this class
  - All public methods in `InteractiveResolver` are marked with `@Deprecated` and will be removed in a future release
  - Methods affected include:
    - `computeInteractiveContext(Microcontext)`
    - `computeInteractiveContext(Context)`
    - `resolveIfMissing(Microcontext)`
    - `resolveIfMissing(Context)`
    - `getInteractiveContext()`
    - `setInteractiveContext(InteractiveContext)`
  - All methods now return default/empty values instead of actual implementations, you can safely remove any references to these methods to avoid compilation issues going forward
