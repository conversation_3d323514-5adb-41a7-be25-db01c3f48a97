# Manuals metadata file.
# For more details, see https://manuals.netflix.net/view/docs/mkdocs/master/manuals/manuals-metadata/

metadataVersion: 2

# Identifiers for this documentation bundle
name: microcontext

# Email distro for doc maintainers
owner: <EMAIL>

# Display name and description
displayName: Microcontext
description: Microcontext is a compact, well-defined model containing common data that will be resolved in the gateways  and be available for easy access in services.

# Relative path to an image located in either the .manuals metadata directory or site root directory.
coverImagePath:

# Grouping will help users discover your docs by clustering them
# in hierarchical trees. One such tree could be org -> repo -> name,
# but you are free to choose whatever levels make sense for your docs
groupingLevel1:
groupingLevel2:
groupingLevel3:

# Comma separated tags for discovery (not implemented yet)
tags:

# Git clone URL of the repo whose code is being documented by this manual page.
# Sometimes this is the same as the manual repo (when the manual is stored in the same repo as the code)
# and sometimes it is its own repository.
codeRepoPath: ssh://**************************:7999/dna/microcontext.git

# Valid options are:
# draft - The manual is incomplete and not ready to be published.
# published - The docs are complete and up-to-date.
# deprecated - The content has sufficiently changed so as to render this documentation irrelevant, and it is being kept for posterity only.
# superseded - A new manual with sufficiently different information such that this manual exists for historical purposes only. In this state, the old documentation should redirect to the new version.
docLifecycleStatus: "published"