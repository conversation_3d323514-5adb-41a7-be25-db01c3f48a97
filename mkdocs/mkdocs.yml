site_name: Microcontext

# Managed by newt to support integration with Manuals and Stash.
repo_url: !ENV REPO_URL
site_url: !ENV SITE_URL

edit_uri: edit
repo_name: Stash

nav:
  - Overview: index.md
  - Conceptual Information:
    - concepts/index.md
    - concepts/initialization.md
  - Getting Started:
    - starting/java.md
    - starting/nodequark.md
  - Use Case Guides:
    - guides/index.md
    - guides/debugging_requests.md
    - guides/resolvers.md
    - guides/manual.md
    - guides/blocked_proxy.md
    - Testing:
      - guides/testing/java.md
      - guides/testing/nodequark.md
  - Reference Material:
    - ref/metrics.md
    - ref/headersandparameters.md
    - Initialization:
        - ref/init_auth.md
        - ref/init_client.md
        - ref/init_country.md
        - ref/init_device.md
        - ref/init_geo.md
        - ref/init_locales.md
        - ref/init_user.md
        - ref/init_video.md
        - ref/init_visit.md
    - ref/web_device_tier.md
  - Support:
    - support/faq.md
    - support/debugging.md
    - support/breaking.md

theme:
  name: material
  logo: img/logo.png
  favicon: 'img/favicon.ico'
  palette:
    primary: 'white'
    accent: 'red'
  font:
    text: 'Netflix Sans'
    code: 'Source Code Pro'

extra_javascript: ['js/mermaid.js']
extra_css:
   - stylesheets/extra.css
# There are many available formatting extensions available, please read:
# https://facelessuser.github.io/pymdown-extensions/
markdown_extensions:
  - toc:
      permalink: True
  - admonition
  - pymdownx.tilde
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format