# Docs Site app type

App type for authoring and publishing MkDocs sites to [go/man](http://go.netflix.com/man).

To get started:

1.  Follow the instructions at [Initializing a Manual](http://go.netflix.com/man/docs/manuals/initialize/).
1.  Read [Documentation Best Practices](http://go.netflix.com/man/docs/mkdocs/master/manuals/) and optionally [MkDocs documentation](https://www.mkdocs.org).

## Helpful Newt commands

*   `newt serve` - Serves a live-reloading docs server at `localhost:8000`.
*   `newt build` - Builds the Docs Site.
*   `newt publish` - Publishes the Docs Site to [go/man](http://go.netflix.com/man).
    Generally you should not run this manually and let the Jenkins job publish for you after a commit is made to your default branch.
*   `newt health-score` - Checks for maintainability best practices and prints the output
*   `newt upgrade` - Migrates your docs-site to the latest supported version of
    manuals, themeing, and mkdocs metadata.

## Project layout

```
mkdocs.yml    # The configuration file.
docs/
    index.md  # The documentation homepage.
    ...       # Other markdown pages, images and other files.
```
