# To target a specific stack of microcontext set MICROCONTEXT_TARGET_STACK to the target application's stack
# By default this target will point at the stackless cluster of "microcontext" until MICROCONTEXT_TARGET_STACK is set.
#
# Note, for EurekaDNS replace with:
# grpc.client.microcontext.channel.target=dns:///microcontext${MICROCONTEXT_TARGET_STACK:}.vip.${NETFLIX_REGION:us-east-1}.${NETFLIX_ENVIRONMENT:test}.cloud.netflix.net:8980
grpc.client.microcontext.channel.target=eureka:///microcontextgrpc${MICROCONTEXT_TARGET_STACK:}
grpc.client.microcontext.MicrocontextService.interceptor.retry.default.maxRetries=1
grpc.client.microcontext.MicrocontextService.interceptor.retry.default.statuses=UNAVAILABLE
grpc.client.microcontext.MicrocontextService.interceptor.timeout.default=200
grpc.client.microcontext.MicrocontextService.interceptor.hedge.methods=GetContext
grpc.client.microcontext.MicrocontextService.interceptor.hedge.default.percentile=0.99
grpc.client.microcontext.MicrocontextService.interceptor.concurrency_limit.type=fixed
grpc.client.microcontext.MicrocontextService.interceptor.concurrency_limit.type.fixed.limit=30
grpc.client.microcontext.channel.sslContextFactory=metatron
grpc.client.microcontext.channel.targetApplication=microcontext
grpc.client.microcontext.channel.loadBalancer=choiceof2

# prime connections
grpc.client.microcontext.channel.primeConnectionsEnabled=true
grpc.client.microcontext.channel.primeConnectionsTimeoutSeconds=5
