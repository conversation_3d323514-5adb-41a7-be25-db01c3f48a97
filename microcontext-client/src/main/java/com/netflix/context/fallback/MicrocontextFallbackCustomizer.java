package com.netflix.context.fallback;

import com.netflix.grpc.interceptor.fallback.Fallbacks;
import com.netflix.grpc.interceptor.fallback.Fallbacks.Customizer;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.headers.MapHeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.requestcontext.ContextContextSerializer;
import com.netflix.microcontext.init.resolvers.BaseResolvers;
import com.netflix.microcontext.init.resolvers.ClientResolvers;
import com.netflix.microcontext.init.resolvers.GeoResolvers;
import com.netflix.microcontext.init.resolvers.geo.Geos;
import com.netflix.microcontext.init.utils.Passports;
import com.netflix.microcontext.protogen.GetContextRequest;
import com.netflix.microcontext.protogen.GetContextResponse;
import com.netflix.microcontext.protogen.MicrocontextServiceGrpc;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.type.IdObject;
import com.netflix.type.protogen.BasicTypes.DeviceType;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;
import netflix.context.Context;
import netflix.context.Context.Builder;
import netflix.context.Details;
import netflix.context.Models;
import netflix.context.device.DeviceContext;
import netflix.context.geo.GeoContext;

@SuppressWarnings("unused")
public class MicrocontextFallbackCustomizer implements Customizer {

  private static final Pattern COMMA = Pattern.compile(",");
  public static final String CONTEXT_NAME = "GeoData";

  @Override
  public void customize(Fallbacks fallbacks) {
    fallbacks
        .forService(MicrocontextServiceGrpc.SERVICE_NAME)
        .addMethodFallbackHandler(
            MicrocontextServiceGrpc.getGetContextMethod(),
            (request, status) -> getFallbackResponse(request));
  }

  static Optional<GetContextResponse> getFallbackResponse(GetContextRequest request) {
    RequestContext requestContext = CurrentRequestContext.get();
    try {
      Context context =
          requestContext.getContext("microcontext", ContextContextSerializer.INSTANCE);
      if (context != null) {
        return Optional.of(
            GetContextResponse.newBuilder()
                .setContext(context.toBuilder().setDetails(Details.newBuilder().setFallback(true)))
                .build());
      }
    } catch (Throwable ignore) {
    }

    final HeaderResolver headerResolver = MapHeaderResolver.of(request.getHeadersMap());
    final Builder builder =
        Context.newBuilder()
            .setDetails(Details.newBuilder().setFallback(true))
            .setRequestId(requestContext.getRequestId());

    Optional<GeoContext> geo = getGeo(headerResolver);
    if (geo.isPresent()) {
      GeoContext geoContext = geo.get();
      builder.setCountry(geoContext.getCountry());
    } else {
      Optional.ofNullable(requestContext.getCountry())
          .map(IdObject::getId)
          .map(Models::country)
          .ifPresent(builder::setCountry);
    }
    DeviceContext.Builder deviceBuilder =
        DeviceContext.newBuilder().setEsn(requestContext.getDeviceId());
    if (requestContext.getDeviceType() != null) {
      deviceBuilder.setType(DeviceType.newBuilder().setId(requestContext.getDeviceType().getId()));
    }
    builder.setDevice(deviceBuilder);
    builder.addAllLocales(
        BaseResolvers.locale(headerResolver, null, requestContext, builder.getCountry()));
    Passports.passportIdentity(request.getBoxedPassport())
        .ifPresent(
            pass -> {
              builder.getDeviceBuilder().setEsn(pass.getEsn());
              pass.getDeviceTypeId()
                  .ifPresent(
                      type ->
                          builder.getDeviceBuilder().setType(DeviceType.newBuilder().setId(type)));
            });
    ClientResolvers.resolve(
            headerResolver,
            ParamResolver.EMPTY,
            true,
            builder.getDevice(),
            builder.getVisit().getUserAgent().getClientDetails())
        .ifPresent(builder::setClient);
    return Optional.of(GetContextResponse.newBuilder().setContext(builder).build());
  }

  private static Optional<GeoContext> getGeo(HeaderResolver headers) {
    Map<String, String> attributes = GeoResolvers.geoAttributes(CurrentRequestContext.get());
    if (attributes != null && !attributes.isEmpty()) {
      return Optional.of(Geos.context(headers, attributes));
    }
    return Optional.empty();
  }
}
