package com.netflix.microcontext.client.configuration;

import com.netflix.context.fallback.MicrocontextFallbackCustomizer;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
public class MicrocontextClientFallbacksConfiguration {

  @Bean
  public MicrocontextFallbackCustomizer microcontext_clientGetMicrocontextFallbackCustomizer() {
    return new MicrocontextFallbackCustomizer();
  }
}
