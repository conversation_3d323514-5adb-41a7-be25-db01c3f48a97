package com.netflix.context.fallback;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.netflix.microcontext.init.headers.Headers;
import com.netflix.microcontext.init.requestcontext.ContextContextSerializer;
import com.netflix.microcontext.protogen.GetContextRequest;
import com.netflix.microcontext.protogen.GetContextResponse;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.server.context.junit.BindingContextRule;
import com.netflix.type.protogen.BasicTypes.Locale;
import java.util.Optional;
import netflix.context.Context;
import netflix.context.common.StringList;
import org.junit.Rule;
import org.junit.Test;

public class MicrocontextFallbackCustomizerTest {

  @Rule public BindingContextRule bindingContextRule = new BindingContextRule();

  @Test
  public void getFallbackResponse() {
    GetContextRequest request =
        GetContextRequest.newBuilder()
            .putHeaders(Headers.LOCALES, StringList.newBuilder().addValues("en, en-US").build())
            .build();
    Optional<GetContextResponse> fallbackResponse =
        MicrocontextFallbackCustomizer.getFallbackResponse(request);
    assertTrue(fallbackResponse.isPresent());
    Context context = fallbackResponse.get().getContext();
    assertEquals(2, context.getLocalesCount());
    assertEquals("en", context.getLocales(0).getId());
    assertEquals("en-US", context.getLocales(1).getId());
  }

  @Test
  public void getFallbackResponseRequestContext() {
    final RequestContext requestContext = CurrentRequestContext.get();
    requestContext.addContext(
        "microcontext",
        Context.newBuilder()
            .addLocales(Locale.newBuilder().setId("xx").build())
            .setRequestId("123")
            .build(),
        ContextContextSerializer.INSTANCE);
    GetContextRequest request =
        GetContextRequest.newBuilder()
            .putHeaders(Headers.LOCALES, StringList.newBuilder().addValues("en, en-US").build())
            .build();
    Optional<GetContextResponse> fallbackResponse =
        MicrocontextFallbackCustomizer.getFallbackResponse(request);
    assertTrue(fallbackResponse.isPresent());
    Context context = fallbackResponse.get().getContext();
    // if a microcontext is provided in the requestcontext that will be preferred for fallbacks
    assertEquals(1, context.getLocalesCount());
    assertEquals("xx", context.getLocales(0).getId());
  }
}
