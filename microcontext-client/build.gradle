apply plugin: 'com.netflix.grpc.client'
apply plugin: 'netflix.jvm-library'
apply plugin: 'netflix.spring-boot-netflix-library'

netflixGrpc {
    protoDefinitionProject = ':microcontext-proto-definition'
}

dependencies {
    api project(':microcontext-proto-definition')
    api project(':microcontext-init')
    implementation 'netflix.grpc:netflix-grpc-common'
    implementation "netflix:passport:latest.release"
api 'com.netflix.spring:spring-boot-netflix-starter-grpc-client'

    testImplementation("junit:junit")
    testImplementation("org.mockito:mockito-core")
    testImplementation("org.springframework:spring-test")

    compileOnly 'netflix.grpc:netflix-grpc-runtime-guice'
    compileOnly "org.springframework:spring-context"
    compileOnly 'com.google.inject:guice'
    testImplementation 'netflix:server-context'
    testRuntimeOnly 'org.junit.vintage:junit-vintage-engine'
}
tasks.withType(Test).configureEach { 
    useJUnitPlatform()
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(8)
    }
}


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations:latest.release'
}
