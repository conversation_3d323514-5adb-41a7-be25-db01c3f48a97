package com.netflix.microcontext.resolver.lifecycle;

import com.netflix.microcontext.resolver.MicrocontextResolver;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(
    classes = {MicrocontextResolverAutoConfiguration.class},
    properties = {"spring.application.name=MicrocontextResolverSmokeTest"})
@EnableAutoConfiguration
public class SpringSmokeTest {

  @Autowired MicrocontextResolver microcontextResolver;

  @Test
  public void testInjectionWorks() {}
}
