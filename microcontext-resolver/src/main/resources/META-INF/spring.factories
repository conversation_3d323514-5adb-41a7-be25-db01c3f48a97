# Please keep org.springframework.boot.autoconfigure.EnableAutoConfiguration list up to date at all times with org.springframework.boot.autoconfigure.AutoConfiguration.imports.
# As of spring boot 2.7 EnableAutoConfiguration is deprecated. Please consider removing this section when all consuming apps are on SBN 2.7+.
# See http://go/java-library#library-consumer-insights to find your library consumers.
org.springframework.boot.autoconfigure.EnableAutoConfiguration=com.netflix.microcontext.resolver.lifecycle.MicrocontextResolverAutoConfiguration
