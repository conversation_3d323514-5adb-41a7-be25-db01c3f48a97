package com.netflix.microcontext.resolver.metrics;

import com.netflix.spectator.api.CompositeRegistry;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Spectator;

public class ResolverMetrics {
  // constants
  public static final String NAME = "microcontext.resolver";
  public static final String ACTION = "action";
  public static final String E = "experimentation";
  public static final String U = "user";
  public static final String I = "interactive";
  public static final String RESULT = "result";
  public static final String CACHED = "cached";
  public static final String COMPUTE = "compute";

  // counters
  public static final Counter cachedExperimentation;
  public static final Counter cachedUser;
  public static final Counter computeExperimentation;
  public static final Counter computeUser;
  public static final Counter cachedInteractive;
  public static final Counter contextLegacyInteractive;
  public static final Counter computeInteractive;

  static {
    final CompositeRegistry registry = Spectator.globalRegistry();
    cachedExperimentation = registry.counter(NAME, ACTION, E, RESULT, CACHED);
    cachedUser = registry.counter(NAME, ACTION, U, RESULT, CACHED);
    computeExperimentation = registry.counter(NAME, ACTION, E, RESULT, COMPUTE);
    computeUser = registry.counter(NAME, ACTION, U, RESULT, COMPUTE);
    cachedInteractive = registry.counter(NAME, ACTION, I, RESULT, CACHED);
    contextLegacyInteractive = registry.counter(NAME, ACTION, I, RESULT, "legacy");
    computeInteractive = registry.counter(NAME, ACTION, I, RESULT, COMPUTE);
  }
}
