package com.netflix.microcontext.resolver.microconfig.comparator;

public class Semver implements Comparable<Semver>, ContextComparator<Semver> {

  private int major = 0;
  private int minor = 0;
  private int patch = 0;

  public Semver(String semVer) {

    String[] dashSplit = semVer.split("-");
    if (dashSplit.length <= 0) {
      throw new IllegalArgumentException(semVer + " is not a valid semver string");
    }
    String semVerNoDash = dashSplit[0];
    String[] majorMinorPatch = semVerNoDash.split("\\.");
    if (majorMinorPatch.length > 3 || majorMinorPatch.length <= 0) {
      throw new IllegalArgumentException(semVer + " is not a valid semver");
    }
    if (majorMinorPatch.length == 3) {
      patch = Integer.parseInt(majorMinorPatch[2]);
    }
    if (majorMinorPatch.length >= 2) {
      minor = Integer.parseInt(majorMinorPatch[1]);
    }
    if (majorMinorPatch.length >= 1) {
      major = Integer.parseInt(majorMinorPatch[0]);
    }
  }

  @Override
  public int compareTo(Semver other) {
    if (this.getMajor() > other.getMajor()) {
      return 1;
    } else if (this.getMajor() == other.getMajor()) {
      if (this.getMinor() > other.getMinor()) {
        return 1;
      } else if (this.getMinor() == other.getMinor()) {
        if (this.getPatch() > other.getPatch()) {
          return 1;
        } else if (this.getPatch() == other.getPatch()) {
          return 0;
        } else {
          return -1;
        }
      } else {
        return -1;
      }
    } else {
      return -1;
    }
  }

  public int getMajor() {
    return major;
  }

  public int getMinor() {
    return minor;
  }

  public int getPatch() {
    return patch;
  }

  @Override
  public Boolean matches(Semver minSemver) {
    return this.compareTo(minSemver) >= 0;
  }
}
