package com.netflix.microcontext.resolver.user;

import com.google.rpc.Status;
import com.netflix.microcontext.access.server.CurrentContextCache;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.resolver.ContextResolver;
import com.netflix.microcontext.resolver.MicrocontextResolver;
import com.netflix.microcontext.resolver.config.ResolverConfig;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.subscriberservice.protogen.ReadRequest;
import com.netflix.subscriberservice.protogen.SubscriberServiceGrpc.SubscriberServiceStub;
import io.grpc.Status.Code;
import io.grpc.StatusRuntimeException;
import io.grpc.protobuf.StatusProto;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import javax.annotation.Nonnull;
import javax.inject.Inject;
import javax.inject.Singleton;
import netflix.async.util.grpc.GrpcCallHelpers.Future;
import netflix.context.Context;
import netflix.context.Context.Builder;
import netflix.context.auth.Auth;
import netflix.context.auth.AuthContext;
import netflix.context.user.User;
import netflix.context.user.UserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class UserResolver implements ContextResolver<UserContext> {

  private static final Logger logger = LoggerFactory.getLogger(UserResolver.class);
  private final SubscriberServiceStub subscriber;
  private final ResolverConfig resolverConfig;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Inject
  public UserResolver(SubscriberServiceStub subscriber, ResolverConfig resolverConfig) {
    this.subscriber = subscriber;
    this.resolverConfig = resolverConfig;
  }

  /**
   * This will call subscriber to resolve the {@link UserContext} based on the provided
   * Microcontext.
   *
   * <p>Note: this method is uncached and will result in a remote call for every invocation. For
   * calls that may be cached please use {@link MicrocontextResolver#resolveUser()} ()}
   *
   * @param context the values used to resolve {@link UserContext}
   * @return the resolved {@link UserContext}
   */
  @Override
  public CompletionStage<UserContext> resolve(final Context context) {
    // not possible to not have authContext and have a valid completable future
    final AuthContext authContext = CurrentMicrocontext.get().getAuth();
    if (authContext == AuthContext.getDefaultInstance()) {
      DynamicCounter.increment("microcontext.resolver.user.authmissing");
      return CompletableFuture.completedFuture(UserContext.getDefaultInstance());
    }

    if (authContext.hasCurrentAuth()) {
      Auth currentAuth = authContext.getCurrentAuth();
      return resolveAccountProfile(currentAuth)
          .thenApply(
              accountProfileRemote ->
                  accountProfileRemote.map(UserResolver::convertUser).orElse(null))
          .handle(
              (user, throwable) -> {
                final UserContext.Builder userContextBuilder = UserContext.newBuilder();
                if (user != null) {
                  DynamicCounter.increment("microcontext.resolver.user.success");
                  return userContextBuilder.setCurrentUser(user).build();
                }
                if (throwable instanceof StatusRuntimeException) {
                  final StatusRuntimeException statusRuntimeException =
                      (StatusRuntimeException) throwable;
                  DynamicCounter.increment(
                      "microcontext.resolver.user.error",
                      "status",
                      statusRuntimeException.getStatus().toString());
                } else {
                  DynamicCounter.increment("microcontext.resolver.user.error", "status", "none");
                }
                if (resolverConfig.userFallbackEnabled()) {
                  DynamicCounter.increment("microcontext.resolver.user.fallback", "result", "true");
                  return userContextBuilder
                      .setCurrentUser(
                          User.newBuilder()
                              .setId(currentAuth.getCustomerId())
                              .setGuid(currentAuth.getCustomerGuid())
                              .setOwnerId(currentAuth.getAccountOwnerId())
                              .setOwnerGuid(currentAuth.getAccountOwnerGuid()))
                      .build();
                } else {
                  DynamicCounter.increment(
                      "microcontext.resolver.user.fallback", "result", "false");
                  return UserContext.getDefaultInstance();
                }
              });
    }

    if (authContext.hasVisitorDeviceId()) {
      DynamicCounter.increment("microcontext.resolver.user.visit");
      return CompletableFuture.completedFuture(UserContext.newBuilder().build());
    }

    DynamicCounter.increment("microcontext.resolver.user.empty");
    return CompletableFuture.completedFuture(UserContext.getDefaultInstance());
  }

  @Override
  public CompletionStage<Builder> resolve(final Context.Builder contextBuilder) {
    return resolve(contextBuilder.build())
        .thenApply(
            userContext -> {
              CurrentContextCache.get().setUserContext(userContext);
              return contextBuilder.setUser(userContext);
            });
  }

  private static User.Builder convertUser(@Nonnull AccountProfileRemote account) {
    if (account.getOptionalIsFallback().orElse(false)) {
      return null;
    }
    return UserConverter.convert(account);
  }

  private CompletableFuture<Optional<AccountProfileRemote>> resolveAccountProfile(
      @Nonnull Auth auth) {
    return Future.call(
            subscriber::getCustomer,
            ReadRequest.newBuilder()
                .setProfileid(auth.getCustomerId())
                .setAccountId(auth.getOptionalAccountOwnerId().orElse(auth.getCustomerId()))
                .build())
        .thenApply(Optional::ofNullable)
        .exceptionally(
            throwable -> {
              Status status = StatusProto.fromThrowable(throwable);
              if (status == null || status.getCode() != Code.NOT_FOUND.value()) {
                logger.error("Could not call subscriber", throwable);
              }
              return Optional.empty();
            });
  }
}
