package com.netflix.microcontext.resolver.lifecycle;

import com.google.inject.AbstractModule;
import com.netflix.aballocator.client.configuration.guice.ABAllocatorClientModule;
import com.netflix.microcontext.resolver.MicrocontextResolver;
import com.netflix.microcontext.resolver.MicrocontextResolverImpl;
import com.netflix.subscriberservice.client.lifecycle.SubscriberserviceClientModule;

public class MicrocontextResolverModule extends AbstractModule {

  @Override
  protected void configure() {
    install(new ABAllocatorClientModule());
    install(new SubscriberserviceClientModule());
    bind(MicrocontextResolver.class).to(MicrocontextResolverImpl.class);
  }

  @Override
  public boolean equals(final Object obj) {
    return obj != null && getClass().equals(obj.getClass());
  }

  @Override
  public int hashCode() {
    return getClass().hashCode();
  }
}
