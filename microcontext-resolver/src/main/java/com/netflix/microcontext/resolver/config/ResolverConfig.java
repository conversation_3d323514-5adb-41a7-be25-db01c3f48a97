package com.netflix.microcontext.resolver.config;

import com.netflix.archaius.api.PropertyRepository;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class ResolverConfig {

  private final PropertyRepository propertyRepository;

  @Inject
  public ResolverConfig(PropertyRepository propertyRepository) {
    this.propertyRepository = propertyRepository;
  }

  public boolean userFallbackEnabled() {
    return propertyRepository
        .get("microcontext.user.resolver.fallback", Boolean.class)
        .orElse(true)
        .get();
  }
}
