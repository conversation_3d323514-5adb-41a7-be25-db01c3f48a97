package com.netflix.microcontext.resolver;

import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.utils.ResolverContextUtils;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import netflix.context.experimentation.ExperimentationContext;
import netflix.context.user.UserContext;

@SuppressWarnings("unused")
public class DefaultMicrocontextResolver implements MicrocontextResolver {

  @Override
  public CompletionStage<ExperimentationContext> resolveExperimentation() {
    return CompletableFuture.completedFuture(
        ResolverContextUtils.resolvedExperimentation(CurrentMicrocontext.get().toProto())
            .orElse(ExperimentationContext.getDefaultInstance()));
  }

  @Override
  public CompletionStage<UserContext> resolveUser() {
    return CompletableFuture.completedFuture(CurrentMicrocontext.get().getUser());
  }
}
