package com.netflix.microcontext.resolver;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import netflix.context.experimentation.ExperimentationContext;
import netflix.context.user.UserContext;

/** Utility class, especially helpful for testing */
@SuppressWarnings("unused")
public class SettableMicrocontextResolver implements MicrocontextResolver {

  private final ExperimentationContext experimentationContext;
  private final UserContext userContext;

  public SettableMicrocontextResolver() {
    this(ExperimentationContext.getDefaultInstance(), UserContext.getDefaultInstance());
  }

  public SettableMicrocontextResolver(ExperimentationContext experimentationContext) {
    this(experimentationContext, UserContext.getDefaultInstance());
  }

  public SettableMicrocontextResolver(UserContext userContext) {
    this(ExperimentationContext.getDefaultInstance(), userContext);
  }

  public SettableMicrocontextResolver(
      ExperimentationContext experimentationContext, UserContext userContext) {
    this.experimentationContext = experimentationContext;
    this.userContext = userContext;
  }

  @Override
  public CompletionStage<ExperimentationContext> resolveExperimentation() {
    return CompletableFuture.completedFuture(experimentationContext);
  }

  @Override
  public CompletionStage<UserContext> resolveUser() {
    return CompletableFuture.completedFuture(userContext);
  }
}
