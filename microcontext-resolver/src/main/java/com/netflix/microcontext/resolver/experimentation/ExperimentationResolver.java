package com.netflix.microcontext.resolver.experimentation;

import com.netflix.aballocator.protogen.ABAllocatorServiceGrpc.ABAllocatorServiceStub;
import com.netflix.aballocator.protogen.AbCtx;
import com.netflix.aballocator.protogen.Alloc;
import com.netflix.aballocator.protogen.AllocId;
import com.netflix.aballocator.protogen.AllocIdType;
import com.netflix.aballocator.protogen.AllocResponse;
import com.netflix.aballocator.protogen.AllocStatus;
import com.netflix.aballocator.protogen.GetRequest;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.utils.ResolverContextUtils;
import com.netflix.microcontext.resolver.ContextResolver;
import com.netflix.microcontext.resolver.MicrocontextResolver;
import com.netflix.microcontext.resolver.experimentation.platform.TVUIABContext;
import com.netflix.servo.monitor.DynamicCounter;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.inject.Inject;
import javax.inject.Singleton;
import netflix.async.util.grpc.GrpcCallHelpers.Future;
import netflix.context.Context;
import netflix.context.auth.AuthContext;
import netflix.context.client.category.ClientCategory;
import netflix.context.experimentation.Allocations;
import netflix.context.experimentation.ExperimentationContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
@Singleton
public class ExperimentationResolver implements ContextResolver<ExperimentationContext> {

  private static final Logger logger = LoggerFactory.getLogger(ExperimentationResolver.class);

  private final ABAllocatorServiceStub ab;

  @Inject
  public ExperimentationResolver(ABAllocatorServiceStub ab) {
    this.ab = ab;
  }

  /**
   * This will call ab to resolve the {@link ExperimentationContext} based on the provided
   * Microcontext.
   *
   * <p>Note: this method is uncached and will result in a remote call for every invocation. For
   * calls that may be cached please use {@link MicrocontextResolver#resolveExperimentation()}
   *
   * @param context the values used to resolve {@link ExperimentationContext}
   * @return the resolved {@link ExperimentationContext}
   */
  @Override
  public CompletionStage<ExperimentationContext> resolve(@Nonnull Context context) {
    final AuthContext auth = CurrentMicrocontext.get().getAuth();
    final CompletableFuture<Allocations> account;
    String accountOwnerId =
        auth.getCurrentAuth().getOptionalAccountOwnerId().map(String::valueOf).orElse(null);
    final String esn =
        auth.getOptionalEsn().orElse(context.getDevice().getOptionalEsn().orElse(null));
    final AbCtx abCtx;
    if (context.getClient().getClientCategory() == ClientCategory.TV) {
      abCtx = TVUIABContext.getTvuiAbCtx(context);
    } else {
      abCtx = defaultAbCtx(esn);
    }
    final String userTag;
    if (accountOwnerId != null) {
      userTag = "true";
      account =
          Future.call(
                  ab::getAllocations,
                  GetRequest.newBuilder()
                      .setAbCtx(abCtx)
                      .setAllocId(
                          AllocId.newBuilder()
                              .setIdType(AllocIdType.ACCOUNT_ID)
                              .setId(accountOwnerId))
                      .build())
              .thenApply(response -> allocations(response).build())
              .exceptionally(
                  e -> {
                    handleInterruptedException(e);
                    return Allocations.getDefaultInstance();
                  });
    } else {
      userTag = "false";
      account = CompletableFuture.completedFuture(Allocations.getDefaultInstance());
    }

    final CompletableFuture<Allocations> visitor;
    String vdid = auth.getOptionalVisitorDeviceId().orElse(null);
    final String visitorTag;
    if (vdid != null) {
      visitorTag = "true";
      visitor =
          Future.call(
                  ab::getAllocations,
                  GetRequest.newBuilder()
                      .setAbCtx(abCtx)
                      .setAllocId(
                          AllocId.newBuilder().setIdType(AllocIdType.VISITOR_DEVICE_ID).setId(vdid))
                      .build())
              .thenApply(response -> allocations(response).build())
              .exceptionally(
                  e -> {
                    handleInterruptedException(e);
                    return Allocations.getDefaultInstance();
                  });
    } else {
      visitorTag = "false";
      visitor = CompletableFuture.completedFuture(Allocations.getDefaultInstance());
    }

    DynamicCounter.increment(
        "microcontext.resolver.experimentation.result", "user", userTag, "visitor", visitorTag);
    return account.thenCombine(
        visitor,
        (a, v) -> {
          final ExperimentationContext.Builder expBuilder = ExperimentationContext.newBuilder();
          if (a != Allocations.getDefaultInstance()) {
            expBuilder.setAccountAllocations(a);
          }
          if (v != Allocations.getDefaultInstance()) {
            expBuilder.setVdidAllocations(v);
          }
          return expBuilder.build();
        });
  }

  @Override
  public CompletionStage<Context.Builder> resolve(@Nonnull Context.Builder builder) {
    return resolve(builder.build())
        .thenApply(value -> ResolverContextUtils.setExperimentation(builder, value));
  }

  public static void handleInterruptedException(final Throwable throwable) {
    boolean hasInterruptedException = false;
    int maxDepth = 10;
    Throwable current = throwable;
    while (current != null && --maxDepth >= 0) {
      if (current instanceof InterruptedException) {
        hasInterruptedException = true;
        break;
      }
      current = current.getCause();
    }

    final Thread currentThread = Thread.currentThread();
    final boolean interrupted = currentThread.isInterrupted();
    if (hasInterruptedException && !interrupted) {
      logger.warn("Interrupt it {}", currentThread, throwable);
      currentThread.interrupt();
    }
    DynamicCounter.increment(
        "microcontext.resolver.experimentation.error",
        "thread",
        Boolean.toString(interrupted),
        "hasexception",
        Boolean.toString(hasInterruptedException));
  }

  private AbCtx defaultAbCtx(@Nullable String esn) {
    if (esn != null) {
      return AbCtx.newBuilder().setEsn(esn).build();
    }
    return AbCtx.getDefaultInstance();
  }

  private Allocations.Builder allocations(AllocResponse response) {
    Allocations.Builder allocBuilder = Allocations.newBuilder();
    for (Entry<Integer, Alloc> entry : response.getAllocMap().entrySet()) {
      Alloc alloc = entry.getValue();
      if (alloc.getAllocStatus() == AllocStatus.ALLOCATED) {
        allocBuilder.putTestToCell(entry.getKey(), alloc.getCell());
      }
    }
    return allocBuilder;
  }
}
