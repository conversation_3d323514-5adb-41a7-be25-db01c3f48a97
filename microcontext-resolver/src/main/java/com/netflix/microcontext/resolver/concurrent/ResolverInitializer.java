package com.netflix.microcontext.resolver.concurrent;

import java.util.concurrent.CompletionStage;
import java.util.function.Supplier;

public class ResolverInitializer<T> {

  private volatile CompletionStage<T> reference;

  /**
   * @param supplier Will be called once and only once if the value needs to be resolved
   * @return A resolved value, either by the previously resolved value or by calling the provided
   *     supplier
   */
  public CompletionStage<T> resolveIfAbsent(Supplier<CompletionStage<T>> supplier) {
    CompletionStage<T> result = reference;

    if (result == null) {
      synchronized (this) {
        result = reference;
        if (result == null) {
          // Create the CompletionStage first, ensuring it's fully constructed
          CompletionStage<T> newResult = supplier.get();
          // Only assign to the volatile field after the object is fully constructed
          // This ensures proper publication semantics
          reference = newResult;
          result = newResult;
        }
      }
    }

    return result;
  }

  /**
   * Gets the current state of the value
   *
   * @return will be null if nothing has been resolved or the value if resolved
   */
  public CompletionStage<T> get() {
    return reference;
  }

  /**
   * @return true if a value is present and false otherwise
   */
  public boolean resolved() {
    return reference != null;
  }
}
