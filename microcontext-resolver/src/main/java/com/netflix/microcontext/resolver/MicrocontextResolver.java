package com.netflix.microcontext.resolver;

import java.util.concurrent.CompletionStage;
import netflix.context.experimentation.ExperimentationContext;
import netflix.context.user.UserContext;

/** For use when the underlying contexts may not have been populated */
public interface MicrocontextResolver {

  /**
   * @return the {@link ExperimentationContext} will fetch if a value is not present or cached
   *     instance if already fetched
   */
  CompletionStage<ExperimentationContext> resolveExperimentation();

  /**
   * @return the {@link UserContext} will fetch if a value is not present or cached instance if
   *     already fetched
   */
  CompletionStage<UserContext> resolveUser();
}
