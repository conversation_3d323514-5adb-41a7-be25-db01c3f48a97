package com.netflix.microcontext.resolver.lifecycle;

import com.netflix.aballocator.protogen.ABAllocatorServiceGrpc.ABAllocatorServiceStub;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.microcontext.resolver.MicrocontextResolver;
import com.netflix.microcontext.resolver.MicrocontextResolverImpl;
import com.netflix.microcontext.resolver.config.ResolverConfig;
import com.netflix.microcontext.resolver.experimentation.ExperimentationResolver;
import com.netflix.microcontext.resolver.user.UserResolver;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.subscriberservice.protogen.SubscriberServiceGrpc.SubscriberServiceStub;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
@ConditionalOnProperty(name = "microcontext.resolver.configuration.enabled", matchIfMissing = true)
public class MicrocontextResolverAutoConfiguration {

  @Bean
  public MicrocontextResolver microcontext_resolverMicrocontextResolver(
      ExperimentationResolver experimentationResolver, UserResolver userResolver) {
    return new MicrocontextResolverImpl(experimentationResolver, userResolver);
  }

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Bean
  public UserResolver microcontext_resolverUserResolver(
      @GrpcSpringClient("subscriberservice") SubscriberServiceStub subscriberServiceStub,
      PropertyRepository propertyRepository) {
    return new UserResolver(subscriberServiceStub, new ResolverConfig(propertyRepository));
  }

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Bean
  public ExperimentationResolver microcontext_resolverExperimentationResolver(
      @GrpcSpringClient("aballocator") ABAllocatorServiceStub abAllocatorServiceStub) {
    return new ExperimentationResolver(abAllocatorServiceStub);
  }
}
