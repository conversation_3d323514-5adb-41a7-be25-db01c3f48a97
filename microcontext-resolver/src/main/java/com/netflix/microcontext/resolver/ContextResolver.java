package com.netflix.microcontext.resolver;

import com.google.protobuf.Message;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import java.util.concurrent.CompletionStage;
import netflix.context.Context;

public interface ContextResolver<T extends Message> {

  default CompletionStage<T> resolve() {
    return resolve(CurrentMicrocontext.get().toProto());
  }

  CompletionStage<T> resolve(Context context);

  CompletionStage<Context.Builder> resolve(Context.Builder builder);
}
