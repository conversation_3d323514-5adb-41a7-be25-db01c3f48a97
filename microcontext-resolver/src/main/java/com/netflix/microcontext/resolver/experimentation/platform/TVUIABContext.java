package com.netflix.microcontext.resolver.experimentation.platform;

import com.netflix.aballocator.protogen.AbCtx;
import com.netflix.microcontext.access.server.migration.DeviceMigration;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nonnull;
import netflix.context.Context;

public class TVUIABContext {
  /**
   * <PERSON><PERSON> previously communicated AB context via a URL query param. In GraphQL, we moved the UI
   * version resolution to a header. This class maps MicroContext into a matching AB context before
   * fetching allocations via the ExperimentationResolver. See original TVUI AB Context
   * (https://go.netflix.com/rn0z4l) and AbAllocator context enrichment
   * (https://go.netflix.com/@eEvsM)
   */
  public static AbCtx getTvuiAbCtx(@Nonnull Context context) {
    final Map<String, String> matchCriteria = new HashMap<>();
    matchCriteria.put("currentClientType", "darwin");
    matchCriteria.put("currentUIType", "darwin");
    Optional.ofNullable(context.getClient().getAppVersion().getVersion())
        .ifPresent(version -> matchCriteria.put("currentNrdappVersion", version));
    AbCtx.Builder result = AbCtx.newBuilder().putAllExtraDataMap(matchCriteria);
    DeviceMigration.esn(context).ifPresent(result::setEsn);
    return result.build();
  }
}
