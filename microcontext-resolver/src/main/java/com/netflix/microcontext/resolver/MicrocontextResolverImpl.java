package com.netflix.microcontext.resolver;

import static com.netflix.microcontext.access.server.utils.ResolverContextUtils.resolvedExperimentation;
import static com.netflix.microcontext.resolver.metrics.ResolverMetrics.cachedExperimentation;
import static com.netflix.microcontext.resolver.metrics.ResolverMetrics.cachedUser;
import static com.netflix.microcontext.resolver.metrics.ResolverMetrics.computeExperimentation;
import static com.netflix.microcontext.resolver.metrics.ResolverMetrics.computeUser;

import com.netflix.lang.RequestVariable;
import com.netflix.microcontext.access.server.CurrentContextCache;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.MicrocontextInternalUtils;
import com.netflix.microcontext.access.server.migration.UserMigration;
import com.netflix.microcontext.resolver.concurrent.ResolverInitializer;
import com.netflix.microcontext.resolver.experimentation.ExperimentationResolver;
import com.netflix.microcontext.resolver.user.UserResolver;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.Supplier;
import javax.inject.Inject;
import javax.inject.Singleton;
import netflix.context.Context;
import netflix.context.experimentation.ExperimentationContext;
import netflix.context.user.UserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class MicrocontextResolverImpl implements MicrocontextResolver {

  private static final Logger logger = LoggerFactory.getLogger(MicrocontextResolverImpl.class);

  // resolver initializers
  private static final RequestVariable<ResolverInitializer<ExperimentationContext>>
      EXPERIMENTATION =
          new RequestVariable<ResolverInitializer<ExperimentationContext>>() {
            @Override
            protected ResolverInitializer<ExperimentationContext> initialValue() {
              return new ResolverInitializer<>();
            }
          };
  private static final RequestVariable<ResolverInitializer<UserContext>> USER =
      new RequestVariable<ResolverInitializer<UserContext>>() {
        @Override
        protected ResolverInitializer<UserContext> initialValue() {
          return new ResolverInitializer<>();
        }
      };

  // resolvers
  private final ExperimentationResolver experimentationResolver;
  private final UserResolver userResolver;

  @Inject
  public MicrocontextResolverImpl(
      ExperimentationResolver experimentationResolver, UserResolver userResolver) {
    this.experimentationResolver = experimentationResolver;
    this.userResolver = userResolver;
  }

  @Override
  public CompletionStage<ExperimentationContext> resolveExperimentation() {
    final ResolverInitializer<ExperimentationContext> initializer = EXPERIMENTATION.get();
    if (initializer.resolved()) {
      cachedExperimentation.increment();
      return initializer.get();
    }
    final Context context = CurrentMicrocontext.get().toProto();
    Supplier<CompletionStage<ExperimentationContext>> supplier;
    final Optional<ExperimentationContext> resolved = resolvedExperimentation(context);
    supplier =
        resolved
            .<Supplier<CompletionStage<ExperimentationContext>>>map(
                experimentationContext ->
                    () -> {
                      cachedExperimentation.increment();
                      return CompletableFuture.completedFuture(experimentationContext);
                    })
            .orElseGet(
                () ->
                    () -> {
                      computeExperimentation.increment();
                      return experimentationResolver
                          .resolve(context.toBuilder())
                          .thenApply(
                              enhancedContext -> {
                                MicrocontextInternalUtils.internalSet(enhancedContext.build());
                                return resolvedExperimentation(enhancedContext)
                                    .orElse(ExperimentationContext.getDefaultInstance());
                              });
                    });

    return initializer.resolveIfAbsent(supplier);
  }

  @Override
  public CompletionStage<UserContext> resolveUser() {
    final ResolverInitializer<UserContext> initializer = USER.get();
    if (initializer.resolved()) {
      logger.debug("Resolved user from initializer cache");
      cachedUser.increment();
      return initializer.get();
    }
    Context context = CurrentMicrocontext.get().toProto();
    final Supplier<CompletionStage<UserContext>> supplier;
    final Optional<UserContext> userContextCache =
        Optional.ofNullable(CurrentContextCache.get().getUserContextCache());
    final Optional<UserContext> userContext =
        userContextCache.isPresent() ? userContextCache : UserMigration.getUser(context);
    if (userContext.isPresent()) {
      supplier =
          () -> {
            logger.debug(
                "Resolved user from request context {} request {}", userContext, userContextCache);
            cachedUser.increment();
            return CompletableFuture.completedFuture(userContext.get());
          };
    } else {
      supplier =
          () -> {
            computeUser.increment();
            return userResolver.resolve();
          };
    }

    return initializer.resolveIfAbsent(supplier);
  }
}
