package com.netflix.microcontext.resolver.concurrent;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import org.junit.Assert;
import org.junit.Test;

/**
 * This test class specifically targets publication safety issues in double-checked locking by
 * creating scenarios that could expose incomplete object construction visibility.
 */
public class ResolverInitializerPublicationTest {

  /**
   * Custom CompletionStage implementation that has a visible construction process to help detect
   * publication safety issues.
   */
  private static class TrackableCompletableFuture<T> extends CompletableFuture<T> {
    private volatile boolean fullyConstructed = false;
    private final T value;

    public TrackableCompletableFuture(T value) {
      this.value = value;
      // Simulate some construction work
      try {
        Thread.sleep(1); // Small delay to increase chance of catching publication issues
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
      }
      complete(value);
      this.fullyConstructed = true;
    }

    public boolean isFullyConstructed() {
      return fullyConstructed;
    }

    public T getValue() {
      return value;
    }
  }

  @Test(timeout = 10000)
  public void testPublicationSafety() throws InterruptedException {
    // Run multiple iterations to increase chance of catching publication issues
    for (int iteration = 0; iteration < 10; iteration++) {
      ResolverInitializer<String> initializer = new ResolverInitializer<>();
      AtomicInteger supplierCallCount = new AtomicInteger(0);
      AtomicReference<Exception> exception = new AtomicReference<>();
      AtomicInteger publicationViolations = new AtomicInteger(0);

      Supplier<CompletionStage<String>> supplier =
          () -> {
            int callNumber = supplierCallCount.incrementAndGet();
            return new TrackableCompletableFuture<>("value-" + callNumber);
          };

      int threadCount = 100;
      ExecutorService executor = Executors.newFixedThreadPool(threadCount);
      CountDownLatch startLatch = new CountDownLatch(1);
      CountDownLatch completionLatch = new CountDownLatch(threadCount);

      // Create threads that will all try to access the result
      for (int i = 0; i < threadCount; i++) {
        executor.submit(
            () -> {
              try {
                startLatch.await();
                CompletionStage<String> result = initializer.resolveIfAbsent(supplier);

                // Check if we got a TrackableCompletableFuture and if it's fully constructed
                if (result instanceof TrackableCompletableFuture) {
                  TrackableCompletableFuture<String> trackable =
                      (TrackableCompletableFuture<String>) result;
                  if (!trackable.isFullyConstructed()) {
                    publicationViolations.incrementAndGet();
                  }
                }
              } catch (Exception e) {
                exception.set(e);
              } finally {
                completionLatch.countDown();
              }
            });
      }

      startLatch.countDown();
      completionLatch.await();
      executor.shutdown();

      // Verify results
      Assert.assertNull("No exceptions should occur during concurrent access", exception.get());
      Assert.assertEquals(
          "Supplier should be called exactly once in iteration " + iteration,
          1,
          supplierCallCount.get());
      Assert.assertEquals(
          "No publication safety violations should occur in iteration " + iteration,
          0,
          publicationViolations.get());
    }
  }

  @Test(timeout = 15000)
  public void testHighContentionScenario() throws InterruptedException {
    ResolverInitializer<String> initializer = new ResolverInitializer<>();
    AtomicInteger supplierCallCount = new AtomicInteger(0);
    AtomicReference<Exception> exception = new AtomicReference<>();

    // Supplier that creates a complex object with visible construction
    Supplier<CompletionStage<String>> complexSupplier =
        () -> {
          int callNumber = supplierCallCount.incrementAndGet();

          // Create a more complex CompletionStage that takes time to construct
          CompletableFuture<String> future = new CompletableFuture<>();

          // Simulate async work
          CompletableFuture.runAsync(
              () -> {
                try {
                  Thread.sleep(5); // Simulate some work
                  future.complete("complex-result-" + callNumber);
                } catch (InterruptedException e) {
                  Thread.currentThread().interrupt();
                  future.completeExceptionally(e);
                }
              });

          return future;
        };

    int threadCount = 200;
    ExecutorService executor = Executors.newFixedThreadPool(threadCount);
    CountDownLatch startLatch = new CountDownLatch(1);
    CountDownLatch completionLatch = new CountDownLatch(threadCount);
    CompletionStage<String>[] results = new CompletionStage[threadCount];

    for (int i = 0; i < threadCount; i++) {
      final int index = i;
      executor.submit(
          () -> {
            try {
              startLatch.await();
              results[index] = initializer.resolveIfAbsent(complexSupplier);
            } catch (Exception e) {
              exception.set(e);
            } finally {
              completionLatch.countDown();
            }
          });
    }

    startLatch.countDown();
    completionLatch.await();
    executor.shutdown();

    // Verify results
    Assert.assertNull("No exceptions should occur during high contention access", exception.get());
    Assert.assertEquals(
        "Supplier should be called exactly once under high contention", 1, supplierCallCount.get());

    // All results should be the same instance
    CompletionStage<String> expectedResult = results[0];
    Assert.assertNotNull("First result should not be null", expectedResult);

    for (int i = 1; i < threadCount; i++) {
      Assert.assertSame(
          "All results should be the same instance under high contention",
          expectedResult,
          results[i]);
    }
  }

  @Test(timeout = 10000)
  public void testMemoryOrderingWithVolatileReads() throws InterruptedException {
    ResolverInitializer<String> initializer = new ResolverInitializer<>();
    AtomicInteger supplierCallCount = new AtomicInteger(0);
    AtomicReference<String> observedValue = new AtomicReference<>();
    AtomicReference<Exception> exception = new AtomicReference<>();

    Supplier<CompletionStage<String>> supplier =
        () -> {
          supplierCallCount.incrementAndGet();
          return CompletableFuture.completedFuture("initialized-value");
        };

    CountDownLatch initializationStarted = new CountDownLatch(1);
    CountDownLatch observationComplete = new CountDownLatch(1);

    // Thread 1: Initialize the value
    Thread initializerThread =
        new Thread(
            () -> {
              try {
                initializationStarted.countDown();
                initializer.resolveIfAbsent(supplier);
              } catch (Exception e) {
                exception.set(e);
              }
            });

    // Thread 2: Continuously check for the value
    Thread observerThread =
        new Thread(
            () -> {
              try {
                initializationStarted.await();

                // Keep checking until we see a value
                CompletionStage<String> result = null;
                while (result == null) {
                  result = initializer.get();
                  if (result != null) {
                    // Try to get the actual value from the CompletionStage
                    result.whenComplete(
                        (value, throwable) -> {
                          if (throwable == null) {
                            observedValue.set(value);
                          }
                        });
                    break;
                  }
                  Thread.yield(); // Give other threads a chance
                }
                observationComplete.countDown();
              } catch (Exception e) {
                exception.set(e);
              }
            });

    initializerThread.start();
    observerThread.start();

    observationComplete.await();

    initializerThread.join();
    observerThread.join();

    // Verify results
    Assert.assertNull("No exceptions should occur during memory ordering test", exception.get());
    Assert.assertEquals("Supplier should be called exactly once", 1, supplierCallCount.get());
    Assert.assertEquals(
        "Observer should see the correct value", "initialized-value", observedValue.get());
  }
}
