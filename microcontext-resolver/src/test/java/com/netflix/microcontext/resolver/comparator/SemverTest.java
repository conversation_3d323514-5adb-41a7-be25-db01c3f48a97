package com.netflix.microcontext.resolver.comparator;

import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;

import com.netflix.microcontext.resolver.microconfig.comparator.Semver;
import org.junit.Test;

public class SemverTest {

  @Test
  public void shouldThrowForInvalidSemVer() {
    assertThrows(
        IllegalArgumentException.class,
        () -> {
          Semver v1 = new Semver("1.1.1.1");
        });
  }

  @Test
  public void shouldIgnoreDashedSemverElements() {
    Semver v1 = new Semver("1.1.1-rc1-foo");
    Semver v2 = new Semver("1.1.1-rc2-bar");
    assertTrue(v1.compareTo(v2) == 0);
  }

  @Test
  public void shouldRespectPachVersionsThird() {
    Semver v1 = new Semver("1.1.1");
    Semver v2 = new Semver("1.1.2");
    assertTrue(v1.compareTo(v2) == -1);
  }

  @Test
  public void shouldRespectMinorVersionsSecond() {
    Semver v1 = new Semver("1.1.1");
    Semver v2 = new Semver("1.2.1");
    assertTrue(v1.compareTo(v2) == -1);
  }

  @Test
  public void shouldRespectMajorVersionsFirst() {
    Semver v1 = new Semver("1.1.1");
    Semver v2 = new Semver("2.0.0");
    assertTrue(v1.compareTo(v2) == -1);
  }

  @Test
  public void shouldDetectExactMatch() {
    Semver smA = new Semver("28795.0.0");
    Semver smB = new Semver("28795.0.0");
    assertTrue(smA.compareTo(smB) == 0);
  }
}
