package com.netflix.microcontext.resolver.experimentation;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.AdditionalAnswers.delegatesTo;
import static org.mockito.Mockito.mock;

import com.netflix.aballocator.protogen.ABAllocatorServiceGrpc;
import com.netflix.aballocator.protogen.ABAllocatorServiceGrpc.ABAllocatorServiceImplBase;
import com.netflix.aballocator.protogen.Alloc;
import com.netflix.aballocator.protogen.AllocId;
import com.netflix.aballocator.protogen.AllocIdType;
import com.netflix.aballocator.protogen.AllocResponse;
import com.netflix.aballocator.protogen.AllocStatus;
import com.netflix.aballocator.protogen.GetRequest;
import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.test.utils.TestCurrentMicrocontext;
import com.netflix.microcontext.test.utils.TestMicrocontext;
import io.grpc.ManagedChannel;
import io.grpc.inprocess.InProcessChannelBuilder;
import io.grpc.inprocess.InProcessServerBuilder;
import io.grpc.stub.StreamObserver;
import io.grpc.testing.GrpcCleanupRule;
import java.util.concurrent.ExecutionException;
import netflix.context.Context;
import netflix.context.auth.Auth;
import netflix.context.auth.AuthContext;
import netflix.context.experimentation.Allocations;
import netflix.context.experimentation.ExperimentationContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

public class ExperimentationResolverTest {

  @Rule public final GrpcCleanupRule grpcCleanup = new GrpcCleanupRule();

  @Before
  public void before() {
    BindingContexts.push();
  }

  @After
  public void after() {
    BindingContexts.pop();
  }

  private final ABAllocatorServiceImplBase serviceImpl =
      mock(
          ABAllocatorServiceImplBase.class,
          delegatesTo(
              new ABAllocatorServiceImplBase() {
                @Override
                public void getAllocations(
                    GetRequest request, StreamObserver<AllocResponse> responseObserver) {
                  final AllocId allocId = request.getAllocId();
                  if (allocId.getIdType() == AllocIdType.ACCOUNT_ID) {
                    responseObserver.onNext(
                        AllocResponse.newBuilder()
                            .putAlloc(
                                100,
                                Alloc.newBuilder()
                                    .setAllocStatus(AllocStatus.ALLOCATED)
                                    .setCell(2)
                                    .build())
                            .build());
                    responseObserver.onCompleted();
                  } else if (allocId.getIdType() == AllocIdType.VISITOR_DEVICE_ID) {
                    responseObserver.onNext(
                        AllocResponse.newBuilder()
                            .putAlloc(
                                200,
                                Alloc.newBuilder()
                                    .setAllocStatus(AllocStatus.ALLOCATED)
                                    .setCell(3)
                                    .build())
                            .build());
                    responseObserver.onCompleted();
                  } else {
                    responseObserver.onError(new IllegalArgumentException("invalid id type"));
                  }
                }
              }));

  private ExperimentationResolver experimentationResolver;

  @Before
  public void setUp() throws Exception {
    String serverName = InProcessServerBuilder.generateName();
    grpcCleanup.register(
        InProcessServerBuilder.forName(serverName)
            .directExecutor()
            .addService(serviceImpl)
            .build()
            .start());
    ManagedChannel channel =
        grpcCleanup.register(InProcessChannelBuilder.forName(serverName).directExecutor().build());
    experimentationResolver = new ExperimentationResolver(ABAllocatorServiceGrpc.newStub(channel));
  }

  @Test
  public void resolveAuth() throws ExecutionException, InterruptedException {
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setAuth(
                AuthContext.newBuilder()
                    .setVisitorDeviceId("bar")
                    .setCurrentAuth(
                        Auth.newBuilder()
                            .setCustomerId(1234L)
                            .setCustomerGuid("foo")
                            .setAccountOwnerId(456L)
                            .setAccountOwnerGuid("bar"))
                    .build()));
    ExperimentationContext experimentation =
        experimentationResolver.resolve(Context.getDefaultInstance()).toCompletableFuture().get();
    assertNotNull(experimentation);
    assertTrue(experimentation.hasAccountAllocations());
    final Allocations accountAllocations = experimentation.getAccountAllocations();
    assertEquals(1, accountAllocations.getTestToCellCount());
    assertTrue(experimentation.hasVdidAllocations());
    final Allocations vdidAllocations = experimentation.getVdidAllocations();
    assertEquals(1, vdidAllocations.getTestToCellCount());
  }

  @Test
  public void resolveUser() throws ExecutionException, InterruptedException {
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setAuth(
                AuthContext.newBuilder()
                    .setVisitorDeviceId("bar")
                    .setCurrentAuth(
                        Auth.newBuilder()
                            .setCustomerId(1234L)
                            .setCustomerGuid("foo")
                            .setAccountOwnerId(456L)
                            .setAccountOwnerGuid("bar"))
                    .build()));
    ExperimentationContext experimentation =
        experimentationResolver.resolve(Context.getDefaultInstance()).toCompletableFuture().get();
    assertNotNull(experimentation);
    assertTrue(experimentation.hasAccountAllocations());
    final Allocations accountAllocations = experimentation.getAccountAllocations();
    assertEquals(1, accountAllocations.getTestToCellCount());
    assertTrue(experimentation.hasVdidAllocations());
    final Allocations vdidAllocations = experimentation.getVdidAllocations();
    assertEquals(1, vdidAllocations.getTestToCellCount());
  }

  @Test
  public void resolveVdidAuth() throws ExecutionException, InterruptedException {
    final Context defaultInstance = Context.getDefaultInstance();
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setAuth(AuthContext.newBuilder().setVisitorDeviceId("bar").build()));

    final ExperimentationContext experimentation =
        experimentationResolver.resolve(defaultInstance).toCompletableFuture().get();
    assertNotNull(experimentation);
    assertFalse(experimentation.hasAccountAllocations());
    assertTrue(experimentation.hasVdidAllocations());
    final Allocations vdidAllocations = experimentation.getVdidAllocations();
    assertEquals(1, vdidAllocations.getTestToCellCount());
  }

  @Test
  public void resolveVdidUser() throws ExecutionException, InterruptedException {
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setAuth(AuthContext.newBuilder().setVisitorDeviceId("bar").build()));
    ExperimentationContext experimentation =
        experimentationResolver.resolve(Context.getDefaultInstance()).toCompletableFuture().get();
    assertNotNull(experimentation);
    assertFalse(experimentation.hasAccountAllocations());
    assertTrue(experimentation.hasVdidAllocations());
    final Allocations vdidAllocations = experimentation.getVdidAllocations();
    assertEquals(1, vdidAllocations.getTestToCellCount());
  }
}
