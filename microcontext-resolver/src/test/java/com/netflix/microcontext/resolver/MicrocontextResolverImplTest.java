package com.netflix.microcontext.resolver;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.netflix.microcontext.access.server.ContextUtils;
import com.netflix.microcontext.access.server.CurrentContextCache;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.utils.ResolverContextUtils;
import com.netflix.microcontext.init.resolvers.UserResolvers;
import com.netflix.microcontext.resolver.experimentation.ExperimentationResolver;
import com.netflix.microcontext.resolver.user.UserResolver;
import com.netflix.server.context.junit.BindingContextRule;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import netflix.context.Context;
import netflix.context.experimentation.Allocations;
import netflix.context.experimentation.ExperimentationContext;
import netflix.context.user.User;
import netflix.context.user.UserContext;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MicrocontextResolverImplTest {

  @Rule public BindingContextRule bindingContextRule = new BindingContextRule();

  @Mock ExperimentationResolver experimentationResolver;
  @Mock UserResolver userResolver;

  private final ExperimentationContext defaultContext =
      ExperimentationContext.newBuilder()
          .setAccountAllocations(Allocations.newBuilder().putTestToCell(1, 2))
          .setVdidAllocations(Allocations.newBuilder().putTestToCell(2, 3).putTestToCell(4, 5))
          .build();

  private final ExperimentationContext altContext =
      ExperimentationContext.newBuilder()
          .setAccountAllocations(Allocations.newBuilder().putTestToCell(3, 4))
          .setVdidAllocations(Allocations.newBuilder().putTestToCell(5, 6).putTestToCell(4, 5))
          .build();

  @Test
  public void testResolveDefault() throws ExecutionException, InterruptedException {
    when(experimentationResolver.resolve(any(Context.Builder.class)))
        .thenAnswer(answer -> CompletableFuture.completedFuture(answer.getArgument(0)));
    MicrocontextResolverImpl microcontextResolver =
        new MicrocontextResolverImpl(experimentationResolver, userResolver);
    final CompletionStage<ExperimentationContext> future =
        microcontextResolver.resolveExperimentation();

    ExperimentationContext experimentationContext = future.toCompletableFuture().get();
    assertNotNull(experimentationContext);
    assertTrue(ContextUtils.isDefault(experimentationContext));

    verify(experimentationResolver, times(1)).resolve(any(Context.Builder.class));
  }

  @Test
  public void testResolveCurrent() throws ExecutionException, InterruptedException {
    CurrentMicrocontext.set(
        ResolverContextUtils.setExperimentation(Context.newBuilder(), defaultContext).build());
    MicrocontextResolverImpl microcontextResolver =
        new MicrocontextResolverImpl(experimentationResolver, userResolver);
    ExperimentationContext experimentationContext =
        microcontextResolver.resolveExperimentation().toCompletableFuture().get();
    assertNotNull(experimentationContext);
    assertFalse(ContextUtils.isDefault(experimentationContext));
    assertEquals(1, experimentationContext.getAccountAllocations().getTestToCellCount());
    assertEquals(2, experimentationContext.getVdidAllocations().getTestToCellCount());
    verifyNoInteractions(experimentationResolver);
  }

  @Test
  public void testResolveCurrentResolved() throws ExecutionException, InterruptedException {
    CurrentMicrocontext.set(
        Context.newBuilder()
            .setResolved(ResolverContextUtils.createResolvedContexts(defaultContext))
            .build());
    MicrocontextResolverImpl microcontextResolver =
        new MicrocontextResolverImpl(experimentationResolver, userResolver);
    ExperimentationContext experimentationContext =
        microcontextResolver.resolveExperimentation().toCompletableFuture().get();
    assertNotNull(experimentationContext);
    assertFalse(ContextUtils.isDefault(experimentationContext));
    assertEquals(1, experimentationContext.getAccountAllocations().getTestToCellCount());
    assertEquals(2, experimentationContext.getVdidAllocations().getTestToCellCount());
    verifyNoInteractions(experimentationResolver);
  }

  @Test
  public void testResolveCurrentResolvedWins() throws ExecutionException, InterruptedException {
    CurrentMicrocontext.set(
        Context.newBuilder()
            .setResolved(ResolverContextUtils.createResolvedContexts(defaultContext))
            .build());
    MicrocontextResolverImpl microcontextResolver =
        new MicrocontextResolverImpl(experimentationResolver, userResolver);
    ExperimentationContext experimentationContext =
        microcontextResolver.resolveExperimentation().toCompletableFuture().get();
    assertNotNull(experimentationContext);
    assertFalse(ContextUtils.isDefault(experimentationContext));
    assertEquals(1, experimentationContext.getAccountAllocations().getTestToCellCount());
    assertEquals(2, experimentationContext.getVdidAllocations().getTestToCellCount());
    verifyNoInteractions(experimentationResolver);
  }

  @Test
  public void testResolveResolved() throws ExecutionException, InterruptedException {
    when(experimentationResolver.resolve(any(Context.Builder.class)))
        .thenAnswer(
            answer ->
                CompletableFuture.completedFuture(
                    answer
                        .getArgument(0, Context.Builder.class)
                        .setResolved(ResolverContextUtils.createResolvedContexts(altContext))));
    MicrocontextResolverImpl microcontextResolver =
        new MicrocontextResolverImpl(experimentationResolver, userResolver);
    ExperimentationContext experimentationContext =
        microcontextResolver.resolveExperimentation().toCompletableFuture().get();
    assertNotNull(experimentationContext);
    assertFalse(ContextUtils.isDefault(experimentationContext));
    assertEquals(1, experimentationContext.getAccountAllocations().getTestToCellCount());
    assertEquals(2, experimentationContext.getVdidAllocations().getTestToCellCount());
    verify(experimentationResolver, times(1)).resolve(any(Context.Builder.class));
  }

  @Test
  public void testResolveUserDefault() throws ExecutionException, InterruptedException {
    when(userResolver.resolve())
        .thenReturn(CompletableFuture.completedFuture(UserContext.getDefaultInstance()));
    MicrocontextResolverImpl microcontextResolver =
        new MicrocontextResolverImpl(experimentationResolver, userResolver);
    final CompletionStage<UserContext> future = microcontextResolver.resolveUser();

    final UserContext userContext = future.toCompletableFuture().get();
    assertNotNull(userContext);
    assertTrue(ContextUtils.isDefault(userContext));

    verify(userResolver, times(1)).resolve();
  }

  @Test
  public void testResolveUserCurrentCache() throws ExecutionException, InterruptedException {
    CurrentContextCache.get()
        .setUserContext(
            UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(124)).build());
    MicrocontextResolverImpl microcontextResolver =
        new MicrocontextResolverImpl(experimentationResolver, userResolver);
    UserContext userContext = microcontextResolver.resolveUser().toCompletableFuture().get();
    assertNotNull(userContext);
    assertFalse(ContextUtils.isDefault(userContext));
    assertEquals(124, userContext.getCurrentUser().getId());
    verifyNoInteractions(userResolver);
  }

  @Test
  public void testResolveUserCurrentRC() throws ExecutionException, InterruptedException {
    UserResolvers.setUser(
        UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(124)).build());
    MicrocontextResolverImpl microcontextResolver =
        new MicrocontextResolverImpl(experimentationResolver, userResolver);
    UserContext userContext = microcontextResolver.resolveUser().toCompletableFuture().get();
    assertNotNull(userContext);
    assertFalse(ContextUtils.isDefault(userContext));
    assertEquals(124, userContext.getCurrentUser().getId());
    verifyNoInteractions(userResolver);
  }

  @Test
  public void testResolveUserResolved() throws ExecutionException, InterruptedException {
    when(userResolver.resolve())
        .thenReturn(
            CompletableFuture.completedFuture(
                UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(124)).build()));
    MicrocontextResolverImpl microcontextResolver =
        new MicrocontextResolverImpl(experimentationResolver, userResolver);
    UserContext userContext = microcontextResolver.resolveUser().toCompletableFuture().get();
    assertNotNull(userContext);
    assertFalse(ContextUtils.isDefault(userContext));
    assertEquals(124, userContext.getCurrentUser().getId());
    verify(userResolver, times(1)).resolve();
  }
}
