package com.netflix.microcontext.resolver.concurrent;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import org.junit.Assert;
import org.junit.Test;

public class ResolverInitializerTest {

  @Test
  public void testBasicFunctionality() {
    ResolverInitializer<String> initializer = new ResolverInitializer<>();
    AtomicInteger callCount = new AtomicInteger(0);

    Supplier<CompletionStage<String>> supplier =
        () -> {
          callCount.incrementAndGet();
          return CompletableFuture.completedFuture("test-value");
        };

    // First call should invoke supplier
    CompletionStage<String> result1 = initializer.resolveIfAbsent(supplier);
    Assert.assertNotNull(result1);
    Assert.assertEquals(1, callCount.get());

    // Second call should not invoke supplier
    CompletionStage<String> result2 = initializer.resolveIfAbsent(supplier);
    Assert.assertNotNull(result2);
    Assert.assertEquals(1, callCount.get());

    // Should return the same instance
    Assert.assertSame(result1, result2);
  }

  @Test
  public void testGetAndResolvedMethods() {
    ResolverInitializer<String> initializer = new ResolverInitializer<>();

    // Initially should be null and not resolved
    Assert.assertNull(initializer.get());
    Assert.assertFalse(initializer.resolved());

    // After resolving should return value and be resolved
    CompletionStage<String> result =
        initializer.resolveIfAbsent(() -> CompletableFuture.completedFuture("test"));

    Assert.assertNotNull(initializer.get());
    Assert.assertTrue(initializer.resolved());
    Assert.assertSame(result, initializer.get());
  }

  @Test(timeout = 10000)
  public void testConcurrentAccess() throws InterruptedException {
    ResolverInitializer<String> initializer = new ResolverInitializer<>();
    AtomicInteger supplierCallCount = new AtomicInteger(0);
    AtomicReference<Exception> exception = new AtomicReference<>();

    // Slow supplier to increase chance of race conditions
    Supplier<CompletionStage<String>> slowSupplier =
        () -> {
          try {
            Thread.sleep(10); // Small delay to simulate work
            int count = supplierCallCount.incrementAndGet();
            return CompletableFuture.completedFuture("value-" + count);
          } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
          }
        };

    int threadCount = 20;
    ExecutorService executor = Executors.newFixedThreadPool(threadCount);
    CountDownLatch startLatch = new CountDownLatch(1);
    CountDownLatch completionLatch = new CountDownLatch(threadCount);
    CompletionStage<String>[] results = new CompletionStage[threadCount];

    // Start all threads simultaneously
    for (int i = 0; i < threadCount; i++) {
      final int index = i;
      executor.submit(
          () -> {
            try {
              startLatch.await();
              results[index] = initializer.resolveIfAbsent(slowSupplier);
            } catch (Exception e) {
              exception.set(e);
            } finally {
              completionLatch.countDown();
            }
          });
    }

    startLatch.countDown(); // Start all threads
    completionLatch.await(); // Wait for completion
    executor.shutdown();

    // Verify no exceptions occurred
    Assert.assertNull("Exception occurred during concurrent access", exception.get());

    // Supplier should be called exactly once
    Assert.assertEquals(
        "Supplier should be called exactly once, but was called "
            + supplierCallCount.get()
            + " times",
        1,
        supplierCallCount.get());

    // All results should be the same instance
    CompletionStage<String> firstResult = results[0];
    Assert.assertNotNull(firstResult);

    for (int i = 1; i < threadCount; i++) {
      Assert.assertSame(
          "All threads should get the same CompletionStage instance", firstResult, results[i]);
    }
  }

  @Test(timeout = 10000)
  public void testConcurrentAccessWithFailingSupplier() throws InterruptedException {
    ResolverInitializer<String> initializer = new ResolverInitializer<>();
    AtomicInteger supplierCallCount = new AtomicInteger(0);

    Supplier<CompletionStage<String>> failingSupplier =
        () -> {
          supplierCallCount.incrementAndGet();
          CompletableFuture<String> future = new CompletableFuture<>();
          future.completeExceptionally(new RuntimeException("Supplier failed"));
          return future;
        };

    int threadCount = 10;
    ExecutorService executor = Executors.newFixedThreadPool(threadCount);
    CountDownLatch startLatch = new CountDownLatch(1);
    CountDownLatch completionLatch = new CountDownLatch(threadCount);
    CompletionStage<String>[] results = new CompletionStage[threadCount];

    for (int i = 0; i < threadCount; i++) {
      final int index = i;
      executor.submit(
          () -> {
            try {
              startLatch.await();
              results[index] = initializer.resolveIfAbsent(failingSupplier);
            } catch (Exception e) {
              // Expected for failing supplier
            } finally {
              completionLatch.countDown();
            }
          });
    }

    startLatch.countDown();
    completionLatch.await();
    executor.shutdown();

    // Even with failing supplier, it should only be called once
    Assert.assertEquals(1, supplierCallCount.get());

    // All results should be the same failed CompletionStage
    CompletionStage<String> firstResult = results[0];
    Assert.assertNotNull(firstResult);

    for (int i = 1; i < threadCount; i++) {
      Assert.assertSame(firstResult, results[i]);
    }
  }

  @Test
  public void testMemoryVisibility() throws InterruptedException {
    ResolverInitializer<String> initializer = new ResolverInitializer<>();
    AtomicReference<CompletionStage<String>> observedResult = new AtomicReference<>();
    CountDownLatch initializationLatch = new CountDownLatch(1);
    CountDownLatch observationLatch = new CountDownLatch(1);

    // Thread 1: Initialize the value
    Thread initializerThread =
        new Thread(
            () -> {
              CompletionStage<String> result =
                  initializer.resolveIfAbsent(
                      () -> CompletableFuture.completedFuture("initialized"));
              initializationLatch.countDown();
            });

    // Thread 2: Observe the value after initialization
    Thread observerThread =
        new Thread(
            () -> {
              try {
                initializationLatch.await();
                // Small delay to ensure the other thread has completed
                Thread.sleep(1);
                observedResult.set(initializer.get());
                observationLatch.countDown();
              } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
              }
            });

    initializerThread.start();
    observerThread.start();

    observationLatch.await();

    initializerThread.join();
    observerThread.join();

    // The observer should see the initialized value
    Assert.assertNotNull(observedResult.get());
    Assert.assertTrue(initializer.resolved());
  }

  /**
   * This test specifically targets potential issues with double-checked locking by creating high
   * contention scenarios that could expose race conditions.
   */
  @Test(timeout = 15000)
  public void testDoubleCheckedLockingRaceConditions() throws InterruptedException {
    // Run multiple iterations to increase chance of catching race conditions
    for (int iteration = 0; iteration < 5; iteration++) {
      ResolverInitializer<String> initializer = new ResolverInitializer<>();
      AtomicInteger supplierCallCount = new AtomicInteger(0);
      AtomicInteger successCount = new AtomicInteger(0);
      AtomicReference<Exception> firstException = new AtomicReference<>();

      // Create a supplier that takes some time to execute
      Supplier<CompletionStage<String>> supplier =
          () -> {
            int callNumber = supplierCallCount.incrementAndGet();
            try {
              // Simulate some work
              Thread.sleep(5);
              return CompletableFuture.completedFuture("result-" + callNumber);
            } catch (InterruptedException e) {
              Thread.currentThread().interrupt();
              throw new RuntimeException(e);
            }
          };

      int threadCount = 50;
      ExecutorService executor = Executors.newFixedThreadPool(threadCount);
      CountDownLatch startLatch = new CountDownLatch(1);
      CountDownLatch completionLatch = new CountDownLatch(threadCount);
      CompletionStage<String>[] results = new CompletionStage[threadCount];

      // Create threads that will all try to initialize simultaneously
      for (int i = 0; i < threadCount; i++) {
        final int index = i;
        executor.submit(
            () -> {
              try {
                startLatch.await();
                results[index] = initializer.resolveIfAbsent(supplier);
                successCount.incrementAndGet();
              } catch (Exception e) {
                firstException.compareAndSet(null, e);
              } finally {
                completionLatch.countDown();
              }
            });
      }

      // Start all threads at once
      startLatch.countDown();
      completionLatch.await();
      executor.shutdown();

      // Verify results
      Assert.assertNull(
          "No exceptions should occur during concurrent access", firstException.get());
      Assert.assertEquals(
          "All threads should complete successfully", threadCount, successCount.get());
      Assert.assertEquals(
          "Supplier should be called exactly once per iteration " + iteration,
          1,
          supplierCallCount.get());

      // Verify all threads got the same result
      CompletionStage<String> expectedResult = results[0];
      Assert.assertNotNull("First result should not be null", expectedResult);

      for (int i = 1; i < threadCount; i++) {
        Assert.assertSame(
            "All results should be the same instance in iteration " + iteration,
            expectedResult,
            results[i]);
      }
    }
  }
}
