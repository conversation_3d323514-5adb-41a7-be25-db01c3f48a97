package com.netflix.microcontext.resolver.user;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.mockito.AdditionalAnswers.delegatesTo;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.resolver.config.ResolverConfig;
import com.netflix.microcontext.test.utils.TestCurrentMicrocontext;
import com.netflix.microcontext.test.utils.TestMicrocontext;
import com.netflix.subscriber.types.protogen.SOpType;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.subscriberservice.protogen.ReadRequest;
import com.netflix.subscriberservice.protogen.SubscriberServiceGrpc;
import com.netflix.subscriberservice.protogen.SubscriberServiceGrpc.SubscriberServiceImplBase;
import io.grpc.ManagedChannel;
import io.grpc.inprocess.InProcessChannelBuilder;
import io.grpc.inprocess.InProcessServerBuilder;
import io.grpc.stub.StreamObserver;
import io.grpc.testing.GrpcCleanupRule;
import java.util.concurrent.ExecutionException;
import netflix.context.Context;
import netflix.context.auth.Auth;
import netflix.context.auth.AuthContext;
import netflix.context.user.User;
import netflix.context.user.UserContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UserResolverTest {

  @Rule public final GrpcCleanupRule grpcCleanup = new GrpcCleanupRule();
  @Mock ResolverConfig resolverConfig;

  @Before
  public void before() {
    BindingContexts.push();
  }

  @After
  public void after() {
    BindingContexts.pop();
  }

  private final SubscriberServiceImplBase serviceImpl =
      mock(
          SubscriberServiceImplBase.class,
          delegatesTo(
              new SubscriberServiceImplBase() {
                @Override
                public void getCustomer(
                    ReadRequest request, StreamObserver<AccountProfileRemote> responseObserver) {
                  if (request.getProfileid() == 1111L) {
                    responseObserver.onError(new RuntimeException("oops"));
                  } else {
                    responseObserver.onNext(
                        AccountProfileRemote.newBuilder()
                            .setProfileId(request.getProfileid())
                            .setSopType(SOpType.Type.SOps_Internal_Automation)
                            .build());
                    responseObserver.onCompleted();
                  }
                }
              }));
  private UserResolver userResolver;

  @Before
  public void setUp() throws Exception {
    String serverName = InProcessServerBuilder.generateName();
    grpcCleanup.register(
        InProcessServerBuilder.forName(serverName)
            .directExecutor()
            .addService(serviceImpl)
            .build()
            .start());
    ManagedChannel channel =
        grpcCleanup.register(InProcessChannelBuilder.forName(serverName).directExecutor().build());
    userResolver = new UserResolver(SubscriberServiceGrpc.newStub(channel), resolverConfig);
  }

  @Test
  public void resolveNormal() throws ExecutionException, InterruptedException {
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setAuth(
                AuthContext.newBuilder()
                    .setCurrentAuth(
                        Auth.newBuilder()
                            .setCustomerId(1234L)
                            .setCustomerGuid("foo")
                            .setAccountOwnerId(456L)
                            .setAccountOwnerGuid("bar"))
                    .build()));
    UserContext userContext =
        userResolver.resolve(Context.getDefaultInstance()).toCompletableFuture().get();
    final User currentUser = userContext.getCurrentUser();
    assertEquals(1234L, currentUser.getId());
    assertEquals(SOpType.Type.SOps_Internal_Automation, currentUser.getSopType());
  }

  @Test
  public void resolveAuth() throws ExecutionException, InterruptedException {
    final UserContext userContext =
        userResolver.resolve(Context.newBuilder().build()).toCompletableFuture().get();
    assertSame(userContext, UserContext.getDefaultInstance());
  }

  @Test
  public void resolveError() throws ExecutionException, InterruptedException {
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setAuth(
                AuthContext.newBuilder()
                    .setCurrentAuth(
                        Auth.newBuilder()
                            .setCustomerId(1111L)
                            .setCustomerGuid("foo")
                            .setAccountOwnerId(456L)
                            .setAccountOwnerGuid("bar"))
                    .build()));
    when(resolverConfig.userFallbackEnabled()).thenReturn(true);
    UserContext userContext =
        userResolver.resolve(Context.getDefaultInstance()).toCompletableFuture().get();
    assertNotNull(userContext);
    User currentUser = userContext.getCurrentUser();
    assertEquals(1111L, currentUser.getId());
  }

  @Test
  public void resolveErrorNoFallback() throws ExecutionException, InterruptedException {
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder()
            .setAuth(
                AuthContext.newBuilder()
                    .setCurrentAuth(
                        Auth.newBuilder()
                            .setCustomerId(1111L)
                            .setCustomerGuid("foo")
                            .setAccountOwnerId(456L)
                            .setAccountOwnerGuid("bar"))
                    .build()));
    when(resolverConfig.userFallbackEnabled()).thenReturn(false);
    UserContext userContext =
        userResolver.resolve(Context.getDefaultInstance()).toCompletableFuture().get();
    assertNotNull(userContext);
    assertFalse(userContext.hasCurrentUser());
  }

  @Test
  public void resolveVisitor() throws ExecutionException, InterruptedException {
    AuthContext.newBuilder().setVisitorDeviceId("foo-vdid").build();
    Context.Builder builder =
        userResolver.resolve(Context.newBuilder()).toCompletableFuture().get();
    UserContext userContext = builder.getUser();
    assertFalse(userContext.hasCurrentUser());
  }
}
