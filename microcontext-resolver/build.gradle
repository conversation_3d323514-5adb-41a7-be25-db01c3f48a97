apply plugin: 'netflix.jvm-library'
apply plugin: 'nebula.facet'
apply plugin: 'netflix.spring-boot-netflix-library'

facets {
    springTest
}

dependencies {
    api project(':microcontext-access')
    api "com.netflix.ab:aballocator-client-guice:latest.release"
    api "netflix:subscriberservice-client-guice:latest.release"
    api 'com.netflix.archaius:archaius2-api'
    implementation "netflix:basicTypes-proto-bridge:latest.release"
    implementation "netflix:basicTypes-proto:latest.release"
    implementation "netflix.async.util:async-util:latest.release"
    compileOnly 'com.google.inject:guice'
    compileOnly 'org.springframework.boot:spring-boot'
    compileOnly 'org.springframework.boot:spring-boot-autoconfigure'
    compileOnly 'com.netflix.spring:spring-boot-netflix-grpc'
    testImplementation("junit:junit")
    testImplementation("org.mockito:mockito-core")
    testImplementation("netflix.grpc:netflix-grpc-testkit")
    testImplementation project(':microcontext-test')
    testRuntimeOnly 'org.junit.vintage:junit-vintage-engine'
    springTestImplementation 'com.netflix.spring:spring-boot-netflix-starter-test'
    springTestImplementation 'com.netflix.spring:spring-boot-netflix-starter-application-web-ossonly'
}
tasks.withType(Test).configureEach {
    useJUnitPlatform()
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(8)
    }
}


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations:latest.release'
}
