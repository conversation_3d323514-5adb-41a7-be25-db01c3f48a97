{"config": {"title": "microcontextdashboard", "content": {"toolbar": {"controls": [{"type": "start-end-select", "options": {"defaultEnd": "now-5m"}}, {"extend": "no_legend_toggle"}, {"extend": "log-toggle"}, {"import": "atlas-step"}, {"import": "atlas-shift"}, {"type": "atlas-target", "options": {"validTargets": ["prod.global", "prod.eu-west-1", "prod.us-east-1", "prod.us-west-2", "test.global", "test.eu-west-1", "test.us-east-1", "test.us-west-2"]}}, {"extend": "taglist", "vars": {"tag": "service"}, "options": {"adhoc": true, "default": {"value": "MicrocontextService"}}}, {"extend": "taglist", "vars": {"tag": "nf.app"}}, {"extend": "taglist", "vars": {"tag": "nf.cluster"}}, {"extend": "taglist", "vars": {"tag": "nf.asg"}}, {"extend": "taglist", "vars": {"tag": "nf.node"}}, {"extend": "taglist", "vars": {"tag": "owner"}}, {"extend": "taglist", "vars": {"tag": "caller"}}]}, "views": [{"title": "gRPC Server", "extend": "gRPC-Server", "vars": {"commonQuery": ""}}, {"title": "gRPC Client", "extend": "gRPC-Client", "vars": {"commonQuery": ""}}, {"title": "gRPC Detailed Stats", "extend": "gRPC-Stats-by-Service", "vars": {"tag": "{{service}}", "commonQuery": ""}}], "vars": {"customOverlay": "", "customFilter": "", "overlay": "", "eventTimeline": {"enabled": true}}}}, "metadata": {"description": "microcontext dashboard", "owner": "<EMAIL>", "id": "microcontextdashboard"}}