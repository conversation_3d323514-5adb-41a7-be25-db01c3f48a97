buildscript {
    final netflix_grpc_plugins_version = System.properties['netflix.grpc-plugins_netflix-grpc-plugins.version']
    dependencies {
        classpath "netflix.grpc-plugins:netflix-grpc-plugin-client:${netflix_grpc_plugins_version}"
        classpath "netflix.grpc-plugins:netflix-grpc-plugin-proto-definition:${netflix_grpc_plugins_version}"
        classpath 'netflix:nebula-lumen-dashboard-plugin:latest.release'
        classpath 'com.netflix.spring.gradle:spring-boot-netflix-gradle-plugin:3.+'
        classpath "com.diffplug.spotless:spotless-plugin-gradle:6.10.0"
    }
}

allprojects {
    apply plugin: 'netflix.nebula'
    apply plugin: 'java'
    apply plugin: 'jacoco'
    apply plugin: 'nebula.release'
    apply plugin: "com.diffplug.spotless"
    apply plugin: 'netflix.spring-boot-netflix'

    spotless {
        java {
            targetExclude '**/build/generated/**'
            googleJavaFormat()
        }
    }

    group = 'com.netflix.microcontext'
}

apply plugin: 'netflix.lumen-dashboard'



// Print out full stack traces when our tests fail to assist debugging (e.g., when scanning Jenkins console output)
tasks.withType(Test).configureEach {
    useJUnitPlatform()
    testLogging {
        // set options for log level LIFECYCLE
        events "passed", "skipped", "failed", "standardOut"
        showExceptions true
        exceptionFormat "short"
        showCauses true
        showStackTraces true
        showStandardStreams true

        // set options for log level DEBUG and INFO
        debug {
            events "started", "passed", "skipped", "failed", "standardOut", "standardError"
            exceptionFormat "short"
        }
        info.events = debug.events
        info.exceptionFormat = debug.exceptionFormat
    }
    maxHeapSize = '8G'
}

ext.netflixIPC = {
    clientCodeGen {
        grpc {
            nodejs { }
        }
    }
}
