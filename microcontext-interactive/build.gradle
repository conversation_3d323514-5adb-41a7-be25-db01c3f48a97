apply plugin: 'com.netflix.grpc.proto-definition'
apply plugin: 'netflix.jvm-library'
apply plugin: 'netflix.spring-boot-netflix-library'
apply plugin: 'nebula.facet'

facets {
    springTest
}

dependencies {
    api project(':microcontext-access')
    api 'com.google.protobuf:protobuf-java'
    api 'io.grpc:grpc-protobuf'
    api 'io.grpc:grpc-stub'
    api 'netflix.grpc:netflix-grpc-options-proto-definition'
    api 'netflix:basicTypes-proto:latest.release'
    api 'netflix:subscriberservice-proto-definition:latest.release'
    api 'com.netflix.dcms:dcms-runtime-proto:latest.release'
    api 'netflix:server-context'
    api 'netflix.grpc:netflix-grpc-common'
    api 'commons-codec:commons-codec:1.15'
    api 'com.netflix.archaius:archaius2-api'

    implementation "netflix.async.util:async-util:latest.release"
    implementation "netflix:basicTypes-proto-bridge:latest.release"
    implementation('commons-io:commons-io')
    implementation("org.apache.maven:maven-artifact:3.+")
    compileOnly 'com.google.inject:guice'
    compileOnly 'org.springframework.boot:spring-boot'
    compileOnly 'org.springframework.boot:spring-boot-autoconfigure'
    compileOnly "com.netflix.spring:spring-boot-netflix-starter-library"
    compileOnly 'com.netflix.spring:spring-boot-netflix-grpc'
    testImplementation("junit:junit")
    testImplementation("org.mockito:mockito-core")
    testImplementation("org.springframework:spring-test")
    testImplementation project(':microcontext-test')
    testImplementation 'com.netflix.archaius:archaius2-core'
    testRuntimeOnly 'org.junit.vintage:junit-vintage-engine'
    springTestImplementation "com.netflix.spring:spring-boot-netflix-starter-test"
    springTestImplementation 'com.netflix.spring:spring-boot-netflix-starter-application-web-ossonly'

    testRuntimeOnly 'org.junit.vintage:junit-vintage-engine'
}
tasks.withType(Test).configureEach {
    useJUnitPlatform()
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(8)
    }
}


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations:latest.release'
}
