package com.netflix.microcontext.interactive;

import com.netflix.lang.RequestVariable;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.ContextUtils;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Registry;
import com.netflix.spectator.api.Spectator;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.inject.Singleton;
import netflix.context.Context;
import netflix.context.InteractiveContext;

/**
 * @deprecated will be removed in a future release.
 */
@Deprecated
@SuppressWarnings("unused")
@Singleton
public class InteractiveResolver {

  private static final RequestVariable<InteractiveContext> requestCache = new RequestVariable<>();

  private static final Counter getCache;
  private static final Counter getCacheMissing;
  private static final Counter setCache;

  static {
    final Registry registry = Spectator.globalRegistry();
    getCache = registry.counter("microcontext.interactiveresolver.getcontext", "result", "cache");
    getCacheMissing =
        registry.counter("microcontext.interactiveresolver.getcontext", "result", "cachemissing");
    setCache = registry.counter("microcontext.interactiveresolver.setcontext");
  }

  /**
   * @deprecated will be removed in a future release.
   */
  @Deprecated
  @Nonnull
  public InteractiveContext computeInteractiveContext(final Microcontext ctx) {
    return InteractiveContext.getDefaultInstance();
  }

  /**
   * @deprecated will be removed in a future release.
   */
  @Deprecated
  public InteractiveContext computeInteractiveContext(final Context ctx) {
    return computeInteractiveContext(ContextUtils.fromProto(ctx));
  }

  /**
   * @deprecated will be removed in a future release.
   */
  @Deprecated
  private InteractiveContext buildOrDefault(Microcontext mc) {
    return InteractiveContext.getDefaultInstance();
  }

  /**
   * @deprecated will be removed in a future release.
   */
  @Deprecated
  @Nonnull
  public InteractiveContext resolveIfMissing(final Microcontext microcontext) {
    return getInteractiveContext().orElse(computeInteractiveContext(microcontext));
  }

  /**
   * @deprecated will be removed in a future release.
   */
  @Deprecated
  @Nonnull
  public InteractiveContext resolveIfMissing(final Context ctx) {
    return resolveIfMissing(ContextUtils.fromProto(ctx));
  }

  /**
   * @deprecated will be removed in a future release.
   */
  @Deprecated
  public static Optional<InteractiveContext> getInteractiveContext() {
    final Optional<InteractiveContext> interactiveContext = Optional.ofNullable(requestCache.get());
    if (interactiveContext.isPresent()) {
      final InteractiveContext context = interactiveContext.get();
      if (context != InteractiveContext.getDefaultInstance()) {
        getCache.increment();
        return interactiveContext;
      }
    }
    getCacheMissing.increment();
    return Optional.empty();
  }

  /**
   * @deprecated will be removed in a future release.
   */
  @Deprecated
  public static void setInteractiveContext(final InteractiveContext interactiveContext) {
    setCache.increment();
    requestCache.set(interactiveContext);
  }
}
