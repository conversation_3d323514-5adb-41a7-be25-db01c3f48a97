package com.netflix.microcontext.interactive.lifecycle;

import com.netflix.microcontext.interactive.InteractiveResolver;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;

@SuppressWarnings("DeprecatedIsStillUsed")
@AutoConfiguration
@ConditionalOnProperty(name = "microcontext.resolver.configuration.enabled", matchIfMissing = true)
@Deprecated
public class MicrocontextInteractiveAutoConfiguration {

  @Bean
  public InteractiveResolver microcontext_interactiveInteractiveResolver() {
    return new InteractiveResolver();
  }
}
