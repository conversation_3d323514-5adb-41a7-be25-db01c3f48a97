package com.netflix.microcontext.interactive.lifecycle;

import com.google.inject.AbstractModule;

/**
 * @deprecated will be removed in a future release.
 */
@Deprecated
public class MicrocontextInteractiveModule extends AbstractModule {

  @Override
  protected void configure() {}

  @Override
  public boolean equals(final Object obj) {
    return obj != null && getClass().equals(obj.getClass());
  }

  @Override
  public int hashCode() {
    return getClass().hashCode();
  }
}
