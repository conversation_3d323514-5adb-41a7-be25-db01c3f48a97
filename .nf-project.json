{"inputs": {"teamEmail": "<EMAIL>", "repoOwner": "<PERSON><PERSON><PERSON><PERSON>", "repoName": "microcontext", "repoOwnerType": "user", "projectName": "microcontext", "branchName": "master", "generatorTasks": ["generate"], "frameworkType": "springboot-service", "ipcType": "grpc", "features": ["metatronGrpc"], "examples": "", "javaVersion": 11, "requiresGuiceBridge": true, "cloudProvider": ["aws"], "applicationName": "microcontext", "packageName": "com.netflix.microcontext", "serviceName": "Microcontext", "deliveryApproach": "Managed Delivery", "notificationChannel": "", "testFramework": "junit", "stashUser": "<PERSON><PERSON><PERSON><PERSON>"}, "generator_user": "<PERSON><PERSON><PERSON><PERSON>", "ci_generator_version": "8.4.0", "project_resources": {}}