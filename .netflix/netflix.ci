#!/bin/bash -efx

if [ "$1" == '--no-op' ]
then
  echo='echo'
else
  echo=
fi

# Call gradle through newt to pick up the configured jdk version
# https://manuals.netflix.net/view/ci-docs/mkdocs/main/RocketCI/user-guide/getting-started-existing/#releasing-by-pushing-to-branches
gradle=" ./gradlew"

export ROCKET_TAG_TYPE

gradle_options='--stacktrace'
gradle_tasks='clean'

if [ "${ROCKET_TAG_TYPE}" = "FINAL" ]; then
  gradle_options="${gradle_options} -Prelease.useLastTag=true"
  gradle_tasks="${gradle_tasks} final"
elif [ "${ROCKET_TAG_TYPE}" = "CANDIDATE" ]; then
  gradle_options="${gradle_options} -Prelease.useLastTag=true"
  gradle_tasks="${gradle_tasks} candidate"
else
  gradle_options="${gradle_options} -Prelease.scope=patch"
  gradle_tasks="${gradle_tasks} devSnapshot"
fi

${echo} ${gradle} ${gradle_options} ${gradle_tasks}

${echo} newt --app-type spinnaker validate



