# yaml-language-server: $schema=https://api.spinnaker.mgmt.netflix.net/managed/delivery-configs/schema

# Managed Delivery config generated by Newt. It's OK to manually edit this file.
# For details on the file format, see our JSON schema at http://go/mdschema

name: microcontext
application: microcontext
metadata:
  repoType: "stash"
  repoProjectKey: "dna"
  repoSlug: "microcontext"
  appType: "springboot-service"
artifacts:
  - name: "microcontext-server"
    reference: "microcontext-deb"
    type: deb
    vmOptions:
      baseOs: jammy-classic
      regions:
        - us-east-1
        - us-west-2
        - eu-west-1
        - us-east-2
    from:
      branch:
        name: "master"

environments:
  - name: test
    resources:
      - kind: "ec2/cluster@v1.1"
        spec:
          artifactReference: "microcontext-deb"
          deployWith:
            strategy: highlander
            health: auto
          launchConfiguration:
            cooldown: PT10S
            ebsOptimized: false
            instanceType: m6a.xlarge
            instanceMonitoring: true
            keyPair: "nf-test-keypair-a"
          health:
            warmup: PT600S
            healthCheckType: EC2
          moniker:
            app: microcontext
          locations:
            account: test
            regions:
              - name: us-east-1
          dependencies:
            securityGroupNames:
              - "nf-datacenter"
              - "nf-infrastructure"
              - "microcontext"
      - kind: "ec2/security-group@v1"
        spec:
          moniker:
            app: microcontext
          description: microcontext
          locations:
            account: test
            regions:
              - name: us-east-1
          inboundRules:
            - name: gusto
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
            - name: consumeridentitydgs
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
            - name: entityhydrationdgs
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
            - name: rosetta
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
            - name: pulsedgs
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
            - name: ngbootcampdgs
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
            - name: discoverydgs
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
            - name: jenkins
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
            - ipObject: jenkins/awstest
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
  - name: staging
    constraints:
      - type: depends-on
        environment: test
      - type: allowed-times
        windows:
          - days: Monday-Friday
            hours: 9-16
    resources:
      - kind: ec2/cluster@v1.1
        metadata: { }
        spec:
          moniker:
            app: microcontext
            stack: staging
          artifactReference: "microcontext-deb"
          locations:
            account: prod
            regions:
              - name: eu-west-1
              - name: us-east-1
              - name: us-west-2
              - name: us-east-2
          capacity:
            desired: 2
            max: 2
            min: 2
          dependencies:
            securityGroupNames:
              - nf-datacenter
              - nf-infrastructure
              - microcontext
          deployWith:
            health: AUTO
            strategy: highlander
          launchConfiguration:
            instanceMonitoring: true
            ebsOptimized: true
            instanceType: m6a.xlarge
    verifyWith: [ ]
  - name: prod
    constraints:
      - type: depends-on
        environment: staging
      - type: allowed-times
        windows:
          - days: Monday-Friday
            hours: 9-16
        maxDeploysPerWindow: 2
      - type: canary
        configId: 832c8535-43cb-4b9c-95b0-611a2260800a
        disableRegionalStaggering: true
        clusters:
          - cluster: microcontext
            clusterTag: canary
        resultStrategy: AVERAGE
        numberOfInstances: 1
        durationInMinutes: 60
        intervalInMinutes: 20
        successfulScore: 95
        marginalScore: 80
        global: true
    notifications:
      - type: slack
        address: "#pes-tli-notifications"
        frequency: quiet
    resources:
      - kind: "ec2/cluster@v1.1"
        spec:
          artifactReference: "microcontext-deb"
          deployWith:
            strategy: red-black
            delayBeforeDisable: PT60S
            delayBeforeScaleDown: PT60S
            maxServerGroups: 2
            rollbackOnFailure: true
            resizePreviousToZero: true
          launchConfiguration:
            cooldown: PT10S
            ebsOptimized: false
            instanceType: m6a.xlarge
            instanceMonitoring: true
            keyPair: "nf-prod-keypair-a"
          health:
            warmup: PT600S
            healthCheckType: EC2
          moniker:
            app: microcontext
          locations:
            account: prod
            regions:
              - name: us-east-1
              - name: us-west-2
              - name: eu-west-1
              - name: us-east-2
          dependencies:
            securityGroupNames:
              - "nf-datacenter"
              - "nf-infrastructure"
              - "microcontext"
          capacity:
            max: 200
            min: 10
          scaling:
            template:
              blueprint: Fully Managed
              overrides:
                Scale Up:
                  threshold: 50.0
                Scale Down:
                  threshold: 40.0
          rolloutWith:
            strategy:
              type: staggered
              postDeployWait: PT5M
              order:
                - eu-west-1
                - us-east-1
                - us-west-2
                - us-east-2
      - kind: "ec2/security-group@v1"
        spec:
          moniker:
            app: microcontext
          description: microcontext
          locations:
            account: prod
            regions:
              - name: us-east-1
              - name: us-west-2
              - name: eu-west-1
              - name: us-east-2
          inboundRules:
            - name: gusto
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
            - name: consumeridentitydgs
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
            - name: entityhydrationdgs
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
            - name: rosetta
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
            - name: discoverydgs
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP
            - name: pulsedgs
              portRange:
                endPort: 8980
                startPort: 8980
              protocol: TCP

# This enables the automatic creation and cleanup of temporary environments based on 'test' for every
# PR with a branch name starting with "feature/". For more details, see http://go/md-preview-env
previewEnvironments:
  - baseEnvironment: test
    branch:
      startsWith: "feature/"
