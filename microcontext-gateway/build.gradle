apply plugin: 'netflix.jvm-library'
apply plugin: 'netflix.spring-boot-netflix-library'

dependencies {
    api project(':microcontext-init')
    implementation 'netflix:server-context'
    implementation 'netflix:dts-standalone-common:latest.release'
    implementation 'com.netflix.zuul:zuul-core:2.5.0'
    implementation 'netflix:ua_parser:latest.release'
    testImplementation('com.netflix.passport.test:passport-test-core:latest.release')
    testImplementation "com.netflix.spring:spring-boot-netflix-starter-test"
}

tasks.named('test', Test).configure {
    useJUnitPlatform()
}

nebulaJavaVersionCheck {
    acceptResponsibilityForPossiblyPoisoningTheNetflixGraphByUsingNewerJDK = true
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations:latest.release'
}
