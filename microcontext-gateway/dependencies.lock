{"annotationProcessor": {"com.netflix.spring:spring-boot-netflix-application-dependencies": {"locked": "2.7.226"}}, "compileClasspath": {"aopalliance:aopalliance": {"locked": "1.0", "transitive": ["com.google.inject:guice"]}, "com.fasterxml.jackson.core:jackson-annotations": {"locked": "2.16.1", "transitive": ["com.fasterxml.jackson.core:jackson-databind", "com.netflix.eureka:eureka-client"]}, "com.fasterxml.jackson.core:jackson-core": {"locked": "2.16.1", "transitive": ["com.fasterxml.jackson.core:jackson-databind", "com.netflix.eureka:eureka-client"]}, "com.fasterxml.jackson.core:jackson-databind": {"locked": "2.16.1", "transitive": ["com.netflix.eureka:eureka-client", "com.netflix.zuul:zuul-core"]}, "com.github.andrewoma.dexx:dexx-collections": {"locked": "0.2", "transitive": ["com.github.vlsi.compactmap:compactmap"]}, "com.github.vlsi.compactmap:compactmap": {"locked": "2.0", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.netflix.eureka:eureka-client", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx"]}, "com.google.guava:guava": {"locked": "20.0", "transitive": ["com.google.inject:guice", "com.netflix.servo:servo-core"]}, "com.google.inject:guice": {"locked": "4.1.0", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:passport"]}, "com.netflix.archaius:archaius-core": {"locked": "0.7.7", "transitive": ["com.netflix.eureka:eureka-client", "com.netflix.zuul:zuul-core"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0", "transitive": ["com.netflix.microcontext:microcontext-model"]}, "com.netflix.eureka:eureka-client": {"locked": "1.10.18", "transitive": ["com.netflix.zuul:zuul-core"]}, "com.netflix.microcontext:microcontext-init": {"project": true}, "com.netflix.microcontext:microcontext-model": {"project": true, "transitive": ["com.netflix.microcontext:microcontext-init"]}, "com.netflix.netflix-commons:netflix-commons-util": {"locked": "0.3.0", "transitive": ["com.netflix.zuul:zuul-core"]}, "com.netflix.netflix-commons:netflix-eventbus": {"locked": "0.3.0", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "com.netflix.ribbon:ribbon-archaius": {"locked": "2.4.4", "transitive": ["com.netflix.zuul:zuul-core"]}, "com.netflix.ribbon:ribbon-core": {"locked": "2.4.4", "transitive": ["com.netflix.zuul:zuul-core"]}, "com.netflix.ribbon:ribbon-loadbalancer": {"locked": "2.4.4", "transitive": ["com.netflix.zuul:zuul-discovery"]}, "com.netflix.servo:servo-core": {"locked": "0.12.21", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.netflix.spectator:spectator-api": {"locked": "1.7.7", "transitive": ["com.netflix.zuul:zuul-core", "netflix:server-context"]}, "com.netflix.spring:spring-boot-netflix-application-dependencies": {"locked": "2.7.226"}, "com.netflix.zuul:zuul-core": {"locked": "2.5.0"}, "com.netflix.zuul:zuul-discovery": {"locked": "2.5.0", "transitive": ["com.netflix.zuul:zuul-core"]}, "com.sun.jersey.contribs:jersey-apache-client4": {"locked": "1.19.1", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.sun.jersey:jersey-client": {"locked": "1.19.1", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey.contribs:jersey-apache-client4"]}, "com.sun.jersey:jersey-core": {"locked": "1.19.1", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey:jersey-client"]}, "com.thoughtworks.xstream:xstream": {"locked": "1.4.19", "transitive": ["com.netflix.eureka:eureka-client"]}, "commons-codec:commons-codec": {"locked": "1.9", "transitive": ["org.apache.httpcomponents:httpclient"]}, "commons-collections:commons-collections": {"locked": "3.2.1", "transitive": ["netflix:ua_parser"]}, "commons-configuration:commons-configuration": {"locked": "1.10", "transitive": ["com.netflix.eureka:eureka-client"]}, "commons-lang:commons-lang": {"locked": "2.6", "transitive": ["commons-configuration:commons-configuration"]}, "commons-logging:commons-logging": {"locked": "1.2", "transitive": ["commons-configuration:commons-configuration", "org.apache.httpcomponents:httpclient"]}, "io.github.x-stream:mxparser": {"locked": "1.2.2", "transitive": ["com.thoughtworks.xstream:xstream"]}, "io.netty:netty-buffer": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-codec", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler", "io.netty:netty-transport", "io.netty:netty-transport-native-unix-common"]}, "io.netty:netty-codec": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler"]}, "io.netty:netty-codec-http": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-codec-http2"]}, "io.netty:netty-codec-http2": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-common": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-buffer", "io.netty:netty-codec", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler", "io.netty:netty-resolver", "io.netty:netty-transport", "io.netty:netty-transport-native-unix-common"]}, "io.netty:netty-handler": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-codec-http", "io.netty:netty-codec-http2"]}, "io.netty:netty-resolver": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-handler", "io.netty:netty-transport"]}, "io.netty:netty-transport": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-codec", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler", "io.netty:netty-transport-native-unix-common"]}, "io.netty:netty-transport-native-unix-common": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-handler"]}, "io.reactivex:rxjava": {"locked": "1.3.8", "transitive": ["com.netflix.zuul:zuul-core"]}, "javax.inject:javax.inject": {"locked": "1", "transitive": ["com.google.inject:guice"]}, "javax.servlet:javax.servlet-api": {"locked": "3.1.0", "transitive": ["netflix:server-context"]}, "javax.ws.rs:jsr311-api": {"locked": "1.1.1", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey:jersey-core"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:dts-standalone-common", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition", "netflix:dts-standalone-common"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix:basicTypes": {"locked": "1.86.0", "transitive": ["netflix:server-context"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:passport"]}, "netflix:dts-standalone-common": {"locked": "603.257.0"}, "netflix:passport": {"locked": "4.6.0", "transitive": ["netflix:subscriberservice-proto-definition"]}, "netflix:server-context": {"locked": "4.7.633"}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0", "transitive": ["com.netflix.microcontext:microcontext-model"]}, "netflix:ua_parser": {"locked": "0.6.0"}, "org.apache.httpcomponents:httpclient": {"locked": "4.5.3", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey.contribs:jersey-apache-client4"]}, "org.apache.httpcomponents:httpcore": {"locked": "4.4.6", "transitive": ["org.apache.httpcomponents:httpclient"]}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}, "org.slf4j:slf4j-api": {"locked": "1.7.36", "transitive": ["com.netflix.servo:servo-core", "com.netflix.zuul:zuul-core", "netflix:server-context"]}, "org.yaml:snakeyaml": {"locked": "1.23", "transitive": ["netflix:ua_parser"]}, "xmlpull:xmlpull": {"locked": "*******", "transitive": ["io.github.x-stream:mxparser"]}}, "nebulaRecommenderBom": {"com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations": {"locked": "2.7.226"}}, "resolutionRules": {"com.netflix.microcontext:microcontext": {"project": true}, "com.netflix.nebula:gradle-resolution-rules": {"locked": "0.82.0", "transitive": ["com.netflix.microcontext:microcontext"]}, "netflix.nebula.resolutionrules:resolution-rules": {"locked": "1.357.0", "transitive": ["com.netflix.microcontext:microcontext"]}}, "runtimeClasspath": {"antlr:antlr": {"locked": "2.7.7", "transitive": ["org.antlr:antlr-runtime", "org.antlr:stringtemplate"]}, "aopalliance:aopalliance": {"locked": "1.0", "transitive": ["com.google.inject:guice"]}, "build.buf.protoc-gen-validate:pgv-java-stub": {"locked": "1.1.0", "transitive": ["com.netflix.mesh:mesh-api-java"]}, "ch.qos.reload4j:reload4j": {"locked": "1.2.25", "transitive": ["com.netflix.blitz4j:blitz4j", "netflix:platform-jdk-compat"]}, "com.amazonaws:aws-java-sdk-core": {"locked": "1.12.783", "transitive": ["netflix:platform-core"]}, "com.fasterxml.jackson.core:jackson-annotations": {"locked": "2.17.3", "transitive": ["com.fasterxml.jackson.core:jackson-databind", "com.netflix.archaius:archaius-core", "com.netflix.eureka:eureka-client"]}, "com.fasterxml.jackson.core:jackson-core": {"locked": "2.17.3", "transitive": ["com.fasterxml.jackson.core:jackson-databind", "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor", "com.netflix.archaius:archaius-core", "com.netflix.eureka:eureka-client", "com.netflix.microcontext:microcontext-init", "com.netflix.zuul:zuul-core"]}, "com.fasterxml.jackson.core:jackson-databind": {"locked": "2.17.3", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor", "com.netflix.archaius:archaius-core", "com.netflix.eureka:eureka-client", "com.netflix.ksclient:ksclient-api", "com.netflix.microcontext:microcontext-init", "com.netflix.zuul:zuul-core", "netflix.grpc:netflix-grpc-common", "netflix:platform-core"]}, "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor": {"locked": "2.17.3", "transitive": ["com.amazonaws:aws-java-sdk-core"]}, "com.github.andrewoma.dexx:dexx-collections": {"locked": "0.2", "transitive": ["com.github.vlsi.compactmap:compactmap"]}, "com.github.vlsi.compactmap:compactmap": {"locked": "2.0", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.google.android:annotations": {"locked": "*******", "transitive": ["netflix.io.grpc:grpc-core-nflx"]}, "com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1", "transitive": ["com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-core", "com.netflix.ribbon:ribbon-eureka", "netflix:basicTypes", "netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge", "netflix:geoip-common"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "com.google.guava:guava", "com.netflix.archaius:archaius-core", "com.netflix.eureka:eureka-client", "com.netflix.netflix-commons:netflix-infix", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:dts-standalone-common", "netflix:netflix-config"]}, "com.google.code.gson:gson": {"locked": "2.10.1", "transitive": ["com.netflix.netflix-commons:netflix-infix", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-core-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["com.google.guava:guava", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-services-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "com.google.guava:failureaccess": {"locked": "1.0.1", "transitive": ["com.google.guava:guava"]}, "com.google.guava:guava": {"locked": "31.1-jre", "transitive": ["com.google.inject:guice", "com.netflix.archaius:archaius-core", "com.netflix.microcontext:microcontext-init", "com.netflix.netflix-commons:netflix-infix", "com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-core", "com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "com.netflix.zuul:zuul-core", "com.netflix.zuul:zuul-discovery", "netflix.grpc:netflix-grpc-common", "netflix:platform-core", "netflix:platform-logimpl", "netflix:platform-utils"]}, "com.google.guava:listenablefuture": {"locked": "9999.0-empty-to-avoid-conflict-with-guava", "transitive": ["com.google.guava:guava"]}, "com.google.inject.extensions:guice-multibindings": {"locked": "4.1.0", "transitive": ["netflix:atlas-client"]}, "com.google.inject:guice": {"locked": "4.1.0", "transitive": ["com.google.inject.extensions:guice-multibindings", "com.netflix.eureka:eureka-client", "com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client"]}, "com.google.j2objc:j2objc-annotations": {"locked": "1.3", "transitive": ["com.google.guava:guava"]}, "com.google.re2j:re2j": {"locked": "1.7", "transitive": ["build.buf.protoc-gen-validate:pgv-java-stub"]}, "com.ibm.icu:icu4j": {"locked": "73.2", "transitive": ["netflix:nfi18n-core"]}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:passport"]}, "com.netflix.archaius:archaius-core": {"locked": "0.7.12", "transitive": ["com.netflix.blitz4j:blitz4j", "com.netflix.eureka:eureka-client", "com.netflix.netflix-commons:netflix-eventbus", "com.netflix.ribbon:ribbon-archaius", "com.netflix.zuul:zuul-core", "netflix:netflix-config", "netflix:platform-core"]}, "com.netflix.archaius:archaius2-api": {"locked": "2.8.5", "transitive": ["com.netflix.archaius:archaius2-core", "com.netflix.mesh:mesh-integration-java"]}, "com.netflix.archaius:archaius2-core": {"locked": "2.8.5", "transitive": ["com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client"]}, "com.netflix.blitz4j:blitz4j": {"locked": "1.42.0", "transitive": ["netflix:platform-core"]}, "com.netflix.concurrency-limits:concurrency-limits-core": {"locked": "0.5.3", "transitive": ["com.netflix.concurrency-limits:concurrency-limits-grpc", "netflix.grpc:netflix-grpc-common"]}, "com.netflix.concurrency-limits:concurrency-limits-grpc": {"locked": "0.5.3", "transitive": ["netflix.grpc:netflix-grpc-common"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0", "transitive": ["com.netflix.microcontext:microcontext-model"]}, "com.netflix.eureka:eureka-client": {"locked": "1.10.18", "transitive": ["com.netflix.ribbon:ribbon-eureka", "com.netflix.zuul:zuul-core", "com.netflix.zuul:zuul-discovery"]}, "com.netflix.frigga:frigga": {"locked": "0.26.0", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix:platform-core"]}, "com.netflix.governator:governator-api": {"locked": "1.17.13", "transitive": ["netflix:nfi18n-core"]}, "com.netflix.ksclient:ksclient-api": {"locked": "3.11.0", "transitive": ["netflix:platform-core", "netflix:platform-logimpl"]}, "com.netflix.mesh:mesh-api-java": {"locked": "0.60.0", "transitive": ["com.netflix.mesh:mesh-integration-java"]}, "com.netflix.mesh:mesh-integration-java": {"locked": "0.5.93", "transitive": ["netflix.grpc:netflix-grpc-common"]}, "com.netflix.microcontext:microcontext-init": {"project": true}, "com.netflix.microcontext:microcontext-model": {"project": true, "transitive": ["com.netflix.microcontext:microcontext-init"]}, "com.netflix.netflix-commons:netflix-commons-util": {"locked": "0.3.0", "transitive": ["com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.zuul:zuul-core"]}, "com.netflix.netflix-commons:netflix-eventbus": {"locked": "0.3.0", "transitive": ["com.netflix.eureka:eureka-client", "netflix:nf-eventbus-core", "netflix:platform-core"]}, "com.netflix.netflix-commons:netflix-infix": {"locked": "0.3.0", "transitive": ["com.netflix.netflix-commons:netflix-eventbus"]}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "com.netflix.ribbon:ribbon-archaius": {"locked": "2.4.4", "transitive": ["com.netflix.zuul:zuul-core", "com.netflix.zuul:zuul-discovery"]}, "com.netflix.ribbon:ribbon-core": {"locked": "2.4.4", "transitive": ["com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-eureka", "com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.zuul:zuul-core", "com.netflix.zuul:zuul-discovery"]}, "com.netflix.ribbon:ribbon-eureka": {"locked": "2.4.4", "transitive": ["com.netflix.zuul:zuul-discovery"]}, "com.netflix.ribbon:ribbon-loadbalancer": {"locked": "2.4.4", "transitive": ["com.netflix.ribbon:ribbon-eureka", "com.netflix.zuul:zuul-discovery"]}, "com.netflix.servo:servo-apache": {"locked": "0.13.2", "transitive": ["netflix:atlas-client"]}, "com.netflix.servo:servo-core": {"locked": "0.13.2", "transitive": ["com.netflix.blitz4j:blitz4j", "com.netflix.eureka:eureka-client", "com.netflix.netflix-commons:netflix-eventbus", "com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.servo:servo-apache", "com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client", "netflix:geoip-common", "netflix:netflix-config"]}, "com.netflix.spectator:spectator-api": {"locked": "1.8.12", "transitive": ["com.netflix.ksclient:ksclient-api", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "com.netflix.spectator:spectator-ext-gc", "com.netflix.spectator:spectator-ext-ipc", "com.netflix.spectator:spectator-ext-jvm", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spectator:spectator-reg-atlas", "com.netflix.zuul:zuul-core", "netflix.grpc:netflix-grpc-common", "netflix:atlas-client", "netflix:platform-core", "netflix:server-context"]}, "com.netflix.spectator:spectator-ext-gc": {"locked": "1.8.12", "transitive": ["com.netflix.spectator:spectator-nflx-plugin"]}, "com.netflix.spectator:spectator-ext-ipc": {"locked": "1.8.12", "transitive": ["com.netflix.spectator:spectator-reg-atlas", "netflix.grpc:netflix-grpc-common", "netflix:atlas-client"]}, "com.netflix.spectator:spectator-ext-jvm": {"locked": "1.8.12", "transitive": ["com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client"]}, "com.netflix.spectator:spectator-nflx-plugin": {"locked": "1.8.12", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix:atlas-client"]}, "com.netflix.spectator:spectator-nflx-tagging": {"locked": "1.8.12", "transitive": ["com.netflix.spectator:spectator-nflx-plugin"]}, "com.netflix.spectator:spectator-reg-atlas": {"locked": "1.8.12", "transitive": ["com.netflix.spectator:spectator-nflx-plugin"]}, "com.netflix.spring:spring-boot-netflix-application-dependencies": {"locked": "2.7.226"}, "com.netflix.tracing:netflix-tracing-tags": {"locked": "0.660.0", "transitive": ["netflix:platform-core"]}, "com.netflix.zuul:zuul-core": {"locked": "2.5.0"}, "com.netflix.zuul:zuul-discovery": {"locked": "2.5.0", "transitive": ["com.netflix.zuul:zuul-core"]}, "com.sun.activation:jakarta.activation": {"locked": "1.2.2", "transitive": ["com.sun.xml.bind:jaxb-impl"]}, "com.sun.jersey.contribs:jersey-apache-client4": {"locked": "1.20.1", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.sun.jersey:jersey-client": {"locked": "1.20.1", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey.contribs:jersey-apache-client4", "netflix:platform-core", "netflix:platform-logimpl"]}, "com.sun.jersey:jersey-core": {"locked": "1.20.1", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey:jersey-client", "netflix:platform-jdk-compat"]}, "com.sun.xml.bind:jaxb-impl": {"locked": "2.3.6", "transitive": ["netflix:platform-jdk-compat"]}, "com.thoughtworks.xstream:xstream": {"locked": "1.4.21", "transitive": ["com.netflix.eureka:eureka-client", "netflix:netflix-config"]}, "com.typesafe:config": {"locked": "1.4.3", "transitive": ["com.netflix.spectator:spectator-ext-jvm", "netflix:atlas-client"]}, "commons-beanutils:commons-beanutils": {"locked": "1.9.4", "transitive": ["netflix:netflix-config"]}, "commons-codec:commons-codec": {"locked": "1.16.0", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.netflix.archaius:archaius2-core", "netflix:platform-core", "org.apache.httpcomponents:httpclient"]}, "commons-collections:commons-collections": {"locked": "3.2.2", "transitive": ["com.netflix.blitz4j:blitz4j", "commons-beanutils:commons-beanutils", "netflix:platform-core", "netflix:ua_parser"]}, "commons-configuration:commons-configuration": {"locked": "1.10", "transitive": ["com.netflix.archaius:archaius-core", "com.netflix.blitz4j:blitz4j", "com.netflix.eureka:eureka-client", "com.netflix.ribbon:ribbon-archaius", "netflix:netflix-config", "netflix:nfi18n-core", "netflix:nflibrary-slim", "netflix:platform-core", "netflix:platform-logimpl"]}, "commons-io:commons-io": {"locked": "2.7", "transitive": ["netflix:nfi18n-core"]}, "commons-jxpath:commons-jxpath": {"locked": "1.3", "transitive": ["com.netflix.netflix-commons:netflix-infix"]}, "commons-lang:commons-lang": {"locked": "2.6", "transitive": ["com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-core", "commons-configuration:commons-configuration", "netflix:geoip-common", "netflix:platform-core", "netflix:platform-utils"]}, "commons-logging:commons-logging": {"locked": "1.2", "transitive": ["com.amazonaws:aws-java-sdk-core", "commons-beanutils:commons-beanutils", "commons-configuration:commons-configuration", "netflix:platform-core", "org.apache.httpcomponents:httpclient"]}, "commons-validator:commons-validator": {"locked": "1.8.0", "transitive": ["build.buf.protoc-gen-validate:pgv-java-stub"]}, "io.github.x-stream:mxparser": {"locked": "1.2.2", "transitive": ["com.thoughtworks.xstream:xstream"]}, "io.netty.incubator:netty-incubator-transport-classes-io_uring": {"locked": "0.0.25.Final", "transitive": ["io.netty.incubator:netty-incubator-transport-native-io_uring"]}, "io.netty.incubator:netty-incubator-transport-native-io_uring": {"locked": "0.0.25.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-buffer": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty.incubator:netty-incubator-transport-classes-io_uring", "io.netty:netty-codec", "io.netty:netty-codec-haproxy", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler", "io.netty:netty-transport", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-classes-kqueue", "io.netty:netty-transport-native-epoll", "io.netty:netty-transport-native-kqueue", "io.netty:netty-transport-native-unix-common"]}, "io.netty:netty-codec": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-codec-haproxy", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler"]}, "io.netty:netty-codec-haproxy": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-codec-http": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-codec-http2"]}, "io.netty:netty-codec-http2": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-common": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty.incubator:netty-incubator-transport-classes-io_uring", "io.netty:netty-buffer", "io.netty:netty-codec", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler", "io.netty:netty-resolver", "io.netty:netty-transport", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-classes-kqueue", "io.netty:netty-transport-native-epoll", "io.netty:netty-transport-native-kqueue", "io.netty:netty-transport-native-unix-common"]}, "io.netty:netty-handler": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-codec-http", "io.netty:netty-codec-http2"]}, "io.netty:netty-resolver": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-handler", "io.netty:netty-transport"]}, "io.netty:netty-tcnative-boringssl-static": {"locked": "2.0.61.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-tcnative-classes": {"locked": "2.0.61.Final", "transitive": ["io.netty:netty-tcnative-boringssl-static"]}, "io.netty:netty-transport": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty.incubator:netty-incubator-transport-classes-io_uring", "io.netty:netty-codec", "io.netty:netty-codec-haproxy", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-classes-kqueue", "io.netty:netty-transport-native-epoll", "io.netty:netty-transport-native-kqueue", "io.netty:netty-transport-native-unix-common"]}, "io.netty:netty-transport-classes-epoll": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-transport-native-epoll"]}, "io.netty:netty-transport-classes-kqueue": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-transport-native-kqueue"]}, "io.netty:netty-transport-native-epoll": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-transport-native-kqueue": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-transport-native-unix-common": {"locked": "4.1.107.Final", "transitive": ["io.netty.incubator:netty-incubator-transport-classes-io_uring", "io.netty:netty-handler", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-classes-kqueue", "io.netty:netty-transport-native-epoll", "io.netty:netty-transport-native-kqueue"]}, "io.perfmark:perfmark-api": {"locked": "0.26.0", "transitive": ["com.netflix.zuul:zuul-core", "netflix.io.grpc:grpc-core-nflx"]}, "io.reactivex:rxjava": {"locked": "1.3.8", "transitive": ["com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.zuul:zuul-core"]}, "io.zipkin.brave:brave": {"locked": "5.13.3", "transitive": ["netflix:platform-core"]}, "io.zipkin.reporter2:zipkin-reporter": {"locked": "2.16.3", "transitive": ["io.zipkin.reporter2:zipkin-reporter-brave"]}, "io.zipkin.reporter2:zipkin-reporter-brave": {"locked": "2.16.3", "transitive": ["io.zipkin.brave:brave"]}, "io.zipkin.zipkin2:zipkin": {"locked": "2.23.2", "transitive": ["io.zipkin.reporter2:zipkin-reporter"]}, "jakarta.xml.bind:jakarta.xml.bind-api": {"locked": "2.3.3", "transitive": ["com.sun.xml.bind:jaxb-impl"]}, "javax.activation:javax.activation-api": {"locked": "1.2.0", "transitive": ["javax.xml.bind:jaxb-api"]}, "javax.annotation:javax.annotation-api": {"locked": "1.3.1", "transitive": ["netflix.grpc:netflix-grpc-common"]}, "javax.inject:javax.inject": {"locked": "1", "transitive": ["com.google.inject:guice", "com.netflix.archaius:archaius2-api", "com.netflix.governator:governator-api", "com.netflix.ksclient:ksclient-api", "com.netflix.netflix-commons:netflix-commons-util", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.zuul:zuul-core", "netflix.grpc:netflix-grpc-common", "netflix:dts-standalone-common", "netflix:nf-eventbus-core"]}, "javax.servlet:javax.servlet-api": {"locked": "3.1.0", "transitive": ["com.netflix.netflix-commons:netflix-infix", "netflix:server-context"]}, "javax.ws.rs:jsr311-api": {"locked": "1.1.1", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey:jersey-core", "netflix:platform-core"]}, "javax.xml.bind:jaxb-api": {"locked": "2.5.0", "transitive": ["netflix:platform-jdk-compat"]}, "joda-time:joda-time": {"locked": "2.12.7", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.netflix.netflix-commons:netflix-infix", "netflix:platform-utils"]}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["build.buf.protoc-gen-validate:pgv-java-stub", "com.google.api.grpc:proto-google-common-protos", "com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.mesh:mesh-api-java", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:basicTypes-proto", "netflix:dts-standalone-common", "netflix:metatron-ipc-common", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["build.buf.protoc-gen-validate:pgv-java-stub", "com.netflix.mesh:mesh-integration-java", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-services-nflx", "netflix:dts-standalone-common"]}, "netflix.grpc:netflix-grpc-common": {"locked": "1.63.20", "transitive": ["netflix:dts-standalone-common"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.mesh:mesh-api-java", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-common", "netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-context-nflx", "netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-inprocess-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx", "netflix.io.grpc:grpc-util-nflx"]}, "netflix.io.grpc:grpc-context-nflx": {"locked": "1.63.0", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.io.grpc:grpc-core-nflx"]}, "netflix.io.grpc:grpc-core-nflx": {"locked": "1.63.0", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.io.grpc:grpc-inprocess-nflx", "netflix.io.grpc:grpc-services-nflx", "netflix.io.grpc:grpc-util-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-inprocess-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-services-nflx", "netflix.io.grpc:grpc-stub-nflx", "netflix.io.grpc:grpc-util-nflx"]}, "netflix.io.grpc:grpc-inprocess-nflx": {"locked": "1.63.0", "transitive": ["netflix.grpc:netflix-grpc-common"]}, "netflix.io.grpc:grpc-protobuf-lite-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.mesh:mesh-api-java", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-services-nflx", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-services-nflx": {"locked": "1.63.0", "transitive": ["netflix.grpc:netflix-grpc-common"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.mesh:mesh-api-java", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-services-nflx", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-util-nflx": {"locked": "1.63.0", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.io.grpc:grpc-services-nflx"]}, "netflix:atlas-client": {"locked": "2.29.10", "transitive": ["netflix:platform-utils"]}, "netflix:basicTypes": {"locked": "1.86.0", "transitive": ["netflix:basicTypes-proto-bridge", "netflix:geoip-common", "netflix:nfi18n-core", "netflix:server-context"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:basicTypes-proto-bridge", "netflix:passport"]}, "netflix:basicTypes-proto-bridge": {"locked": "1.86.0", "transitive": ["com.netflix.microcontext:microcontext-init"]}, "netflix:dts-standalone-common": {"locked": "603.257.0"}, "netflix:geoip-common": {"locked": "2.324", "transitive": ["com.netflix.microcontext:microcontext-init"]}, "netflix:i18n-dictionaries": {"locked": "0.205.0", "transitive": ["netflix:nfi18n-core"]}, "netflix:metatron-ipc-common": {"locked": "1.441.0", "transitive": ["netflix.grpc:netflix-grpc-common"]}, "netflix:moduleRegistry": {"locked": "1.50.0", "transitive": ["netflix:nflibrary-slim"]}, "netflix:netflix-config": {"locked": "4.7.633", "transitive": ["netflix:geoip-common", "netflix:nflibrary-slim", "netflix:platform-core", "netflix:platform-logimpl", "netflix:platform-utils"]}, "netflix:nf-eventbus-core": {"locked": "1.32.0", "transitive": ["netflix:platform-core", "netflix:platform-logimpl"]}, "netflix:nfi18n-core": {"locked": "2.248", "transitive": ["com.netflix.microcontext:microcontext-init"]}, "netflix:nflibrary-slim": {"locked": "4.7.633", "transitive": ["netflix:nfi18n-core"]}, "netflix:ngl": {"locked": "0.49.0", "transitive": ["netflix:nfi18n-core"]}, "netflix:passport": {"locked": "4.6.0", "transitive": ["netflix:subscriberservice-proto-definition"]}, "netflix:platform-core": {"locked": "4.7.633", "transitive": ["netflix:platform-logimpl", "netflix:platform-utils"]}, "netflix:platform-jdk-compat": {"locked": "4.7.633", "transitive": ["netflix:platform-core"]}, "netflix:platform-logimpl": {"locked": "4.7.633", "transitive": ["netflix:platform-utils"]}, "netflix:platform-utils": {"locked": "4.7.633", "transitive": ["netflix:nfi18n-core"]}, "netflix:server-context": {"locked": "4.7.633", "transitive": ["com.netflix.microcontext:microcontext-init", "netflix:geoip-common", "netflix:netflix-config", "netflix:platform-core", "netflix:platform-utils"]}, "netflix:statistics": {"locked": "1.7.0", "transitive": ["com.netflix.ribbon:ribbon-loadbalancer", "netflix:platform-utils"]}, "netflix:subscriberservice-common": {"locked": "5.66.0", "transitive": ["com.netflix.microcontext:microcontext-init"]}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:subscriberservice-common"]}, "netflix:ua_parser": {"locked": "0.6.0"}, "nl.basjes.collections:prefixmap": {"locked": "1.0", "transitive": ["netflix:dts-standalone-common"]}, "org.antlr:antlr-runtime": {"locked": "3.4", "transitive": ["com.netflix.netflix-commons:netflix-infix"]}, "org.antlr:stringtemplate": {"locked": "3.2.1", "transitive": ["org.antlr:antlr-runtime"]}, "org.apache.commons:commons-lang3": {"locked": "3.12.0", "transitive": ["com.netflix.archaius:archaius2-core", "netflix.grpc:netflix-grpc-common"]}, "org.apache.commons:commons-math": {"locked": "2.2", "transitive": ["com.netflix.netflix-commons:netflix-eventbus", "netflix:platform-utils"]}, "org.apache.httpcomponents:httpclient": {"locked": "4.5.13", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.netflix.eureka:eureka-client", "com.sun.jersey.contribs:jersey-apache-client4"]}, "org.apache.httpcomponents:httpcore": {"locked": "4.4.13", "transitive": ["org.apache.httpcomponents:httpclient"]}, "org.aspectj:aspectjweaver": {"locked": "1.9.5", "transitive": ["netflix:platform-core"]}, "org.bouncycastle:bcpkix-jdk18on": {"locked": "1.76", "transitive": ["com.netflix.zuul:zuul-core"]}, "org.bouncycastle:bcprov-jdk18on": {"locked": "1.76", "transitive": ["com.netflix.zuul:zuul-core", "org.bouncycastle:bcpkix-jdk18on", "org.bouncycastle:bcutil-jdk18on"]}, "org.bouncycastle:bcutil-jdk18on": {"locked": "1.76", "transitive": ["org.bouncycastle:bcpkix-jdk18on"]}, "org.checkerframework:checker-qual": {"locked": "3.12.0", "transitive": ["com.google.guava:guava", "netflix.io.grpc:grpc-guava-shaded-nflx"]}, "org.codehaus.jackson:jackson-core-asl": {"locked": "1.10.3", "transitive": ["netflix:platform-jdk-compat", "org.codehaus.jackson:jackson-mapper-asl"]}, "org.codehaus.jackson:jackson-mapper-asl": {"locked": "1.10.3", "transitive": ["netflix:netflix-config"]}, "org.codehaus.jettison:jettison": {"locked": "1.4.0", "transitive": ["com.netflix.eureka:eureka-client"]}, "org.codehaus.mojo:animal-sniffer-annotations": {"locked": "1.23", "transitive": ["netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-util-nflx"]}, "org.lz4:lz4-java": {"locked": "1.8.0", "transitive": ["netflix:dts-standalone-common"]}, "org.slf4j:slf4j-api": {"locked": "1.7.36", "transitive": ["com.netflix.archaius:archaius-core", "com.netflix.archaius:archaius2-api", "com.netflix.archaius:archaius2-core", "com.netflix.blitz4j:blitz4j", "com.netflix.concurrency-limits:concurrency-limits-core", "com.netflix.concurrency-limits:concurrency-limits-grpc", "com.netflix.microcontext:microcontext-init", "com.netflix.netflix-commons:netflix-commons-util", "com.netflix.netflix-commons:netflix-eventbus", "com.netflix.netflix-commons:netflix-infix", "com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-core", "com.netflix.ribbon:ribbon-eureka", "com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "com.netflix.spectator:spectator-api", "com.netflix.spectator:spectator-ext-gc", "com.netflix.spectator:spectator-ext-ipc", "com.netflix.spectator:spectator-ext-jvm", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spectator:spectator-nflx-tagging", "com.netflix.spectator:spectator-reg-atlas", "com.netflix.zuul:zuul-core", "com.netflix.zuul:zuul-discovery", "netflix.grpc:netflix-grpc-common", "netflix:atlas-client", "netflix:dts-standalone-common", "netflix:geoip-common", "netflix:metatron-ipc-common", "netflix:nf-eventbus-core", "netflix:nflibrary-slim", "netflix:server-context", "netflix:subscriberservice-common"]}, "org.yaml:snakeyaml": {"locked": "1.23", "transitive": ["netflix:ua_parser"]}, "xmlpull:xmlpull": {"locked": "*******", "transitive": ["io.github.x-stream:mxparser"]}}, "testAnnotationProcessor": {"com.netflix.spring:spring-boot-netflix-application-dependencies": {"locked": "2.7.226"}}, "testCompileClasspath": {"aopalliance:aopalliance": {"locked": "1.0", "transitive": ["com.google.inject:guice"]}, "com.fasterxml.jackson.core:jackson-annotations": {"locked": "2.16.1", "transitive": ["com.fasterxml.jackson.core:jackson-databind", "com.netflix.eureka:eureka-client"]}, "com.fasterxml.jackson.core:jackson-core": {"locked": "2.16.1", "transitive": ["com.fasterxml.jackson.core:jackson-databind", "com.netflix.eureka:eureka-client"]}, "com.fasterxml.jackson.core:jackson-databind": {"locked": "2.16.1", "transitive": ["com.netflix.eureka:eureka-client", "com.netflix.zuul:zuul-core"]}, "com.github.andrewoma.dexx:dexx-collections": {"locked": "0.2", "transitive": ["com.github.vlsi.compactmap:compactmap"]}, "com.github.vlsi.compactmap:compactmap": {"locked": "2.0", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.netflix.eureka:eureka-client", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx"]}, "com.google.guava:guava": {"locked": "20.0", "transitive": ["com.google.inject:guice", "com.netflix.servo:servo-core"]}, "com.google.inject:guice": {"locked": "4.1.0", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}, "com.jayway.jsonpath:json-path": {"locked": "2.7.0", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["com.netflix.microcontext:microcontext-model", "com.netflix.passport.test:passport-test-core", "netflix:passport"]}, "com.netflix.archaius:archaius-core": {"locked": "0.7.7", "transitive": ["com.netflix.eureka:eureka-client", "com.netflix.zuul:zuul-core"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0", "transitive": ["com.netflix.microcontext:microcontext-model"]}, "com.netflix.eureka:eureka-client": {"locked": "1.10.18", "transitive": ["com.netflix.zuul:zuul-core"]}, "com.netflix.microcontext:microcontext-init": {"project": true}, "com.netflix.microcontext:microcontext-model": {"project": true, "transitive": ["com.netflix.microcontext:microcontext-init"]}, "com.netflix.netflix-commons:netflix-commons-util": {"locked": "0.3.0", "transitive": ["com.netflix.zuul:zuul-core"]}, "com.netflix.netflix-commons:netflix-eventbus": {"locked": "0.3.0", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.passport.test:passport-test-core": {"locked": "0.41.0"}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "com.netflix.ribbon:ribbon-archaius": {"locked": "2.4.4", "transitive": ["com.netflix.zuul:zuul-core"]}, "com.netflix.ribbon:ribbon-core": {"locked": "2.4.4", "transitive": ["com.netflix.zuul:zuul-core"]}, "com.netflix.ribbon:ribbon-loadbalancer": {"locked": "2.4.4", "transitive": ["com.netflix.zuul:zuul-discovery"]}, "com.netflix.servo:servo-core": {"locked": "0.12.21", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.netflix.spectator:spectator-api": {"locked": "1.7.7", "transitive": ["com.netflix.zuul:zuul-core", "netflix:server-context"]}, "com.netflix.spring.devagent:sbn-dev-agent-client": {"locked": "0.0.13", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "com.netflix.spring:spring-boot-netflix": {"locked": "2.7.226", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "com.netflix.spring:spring-boot-netflix-application-dependencies": {"locked": "2.7.226"}, "com.netflix.spring:spring-boot-netflix-starter-test": {"locked": "2.7.226"}, "com.netflix.spring:spring-boot-netflix-test": {"locked": "2.7.226", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "com.netflix.zuul:zuul-core": {"locked": "2.5.0"}, "com.netflix.zuul:zuul-discovery": {"locked": "2.5.0", "transitive": ["com.netflix.zuul:zuul-core"]}, "com.sun.jersey.contribs:jersey-apache-client4": {"locked": "1.19.1", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.sun.jersey:jersey-client": {"locked": "1.19.1", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey.contribs:jersey-apache-client4"]}, "com.sun.jersey:jersey-core": {"locked": "1.19.1", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey:jersey-client"]}, "com.thoughtworks.xstream:xstream": {"locked": "1.4.19", "transitive": ["com.netflix.eureka:eureka-client"]}, "commons-codec:commons-codec": {"locked": "1.9", "transitive": ["org.apache.httpcomponents:httpclient"]}, "commons-collections:commons-collections": {"locked": "3.2.1", "transitive": ["netflix:ua_parser"]}, "commons-configuration:commons-configuration": {"locked": "1.10", "transitive": ["com.netflix.eureka:eureka-client"]}, "commons-lang:commons-lang": {"locked": "2.6", "transitive": ["commons-configuration:commons-configuration"]}, "io.github.x-stream:mxparser": {"locked": "1.2.2", "transitive": ["com.thoughtworks.xstream:xstream"]}, "io.netty:netty-buffer": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-codec", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler", "io.netty:netty-transport", "io.netty:netty-transport-native-unix-common"]}, "io.netty:netty-codec": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler"]}, "io.netty:netty-codec-http": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-codec-http2"]}, "io.netty:netty-codec-http2": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-common": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-buffer", "io.netty:netty-codec", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler", "io.netty:netty-resolver", "io.netty:netty-transport", "io.netty:netty-transport-native-unix-common"]}, "io.netty:netty-handler": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-codec-http", "io.netty:netty-codec-http2"]}, "io.netty:netty-resolver": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-handler", "io.netty:netty-transport"]}, "io.netty:netty-transport": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-codec", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler", "io.netty:netty-transport-native-unix-common"]}, "io.netty:netty-transport-native-unix-common": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-handler"]}, "io.reactivex:rxjava": {"locked": "1.3.8", "transitive": ["com.netflix.zuul:zuul-core"]}, "jakarta.activation:jakarta.activation-api": {"locked": "1.2.2", "transitive": ["jakarta.xml.bind:jakarta.xml.bind-api"]}, "jakarta.annotation:jakarta.annotation-api": {"locked": "1.3.5", "transitive": ["org.springframework.boot:spring-boot-starter"]}, "jakarta.xml.bind:jakarta.xml.bind-api": {"locked": "2.3.3", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "javax.inject:javax.inject": {"locked": "1", "transitive": ["com.google.inject:guice"]}, "javax.servlet:javax.servlet-api": {"locked": "3.1.0", "transitive": ["netflix:server-context"]}, "javax.ws.rs:jsr311-api": {"locked": "1.1.1", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey:jersey-core"]}, "junit:junit": {"locked": "4.13.2", "transitive": ["org.junit.vintage:junit-vintage-engine"]}, "net.bytebuddy:byte-buddy": {"locked": "1.12.9", "transitive": ["org.mockito:mockito-core"]}, "net.bytebuddy:byte-buddy-agent": {"locked": "1.12.9", "transitive": ["org.mockito:mockito-core"]}, "net.minidev:accessors-smart": {"locked": "2.4.7", "transitive": ["net.minidev:json-smart"]}, "net.minidev:json-smart": {"locked": "2.4.7", "transitive": ["com.jayway.jsonpath:json-path"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:dts-standalone-common", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition", "netflix:dts-standalone-common"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix:basicTypes": {"locked": "1.86.0", "transitive": ["netflix:server-context"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:passport"]}, "netflix:dts-standalone-common": {"locked": "603.257.0"}, "netflix:passport": {"locked": "4.6.0", "transitive": ["netflix:subscriberservice-proto-definition"]}, "netflix:server-context": {"locked": "4.7.633"}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0", "transitive": ["com.netflix.microcontext:microcontext-model"]}, "netflix:ua_parser": {"locked": "0.6.0"}, "org.apache.httpcomponents:httpclient": {"locked": "4.5.3", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey.contribs:jersey-apache-client4"]}, "org.apache.httpcomponents:httpcore": {"locked": "4.4.6", "transitive": ["org.apache.httpcomponents:httpclient"]}, "org.apiguardian:apiguardian-api": {"locked": "1.1.2", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.jupiter:junit-jupiter-params", "org.junit.platform:junit-platform-commons", "org.junit.platform:junit-platform-engine", "org.junit.vintage:junit-vintage-engine"]}, "org.assertj:assertj-core": {"locked": "3.22.0", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}, "org.hamcrest:hamcrest": {"locked": "2.2", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.junit.jupiter:junit-jupiter": {"locked": "5.12.2", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.junit.jupiter:junit-jupiter-api": {"locked": "5.12.2", "transitive": ["org.junit.jupiter:junit-jupiter", "org.junit.jupiter:junit-jupiter-params"]}, "org.junit.jupiter:junit-jupiter-params": {"locked": "5.12.2", "transitive": ["org.junit.jupiter:junit-jupiter"]}, "org.junit.platform:junit-platform-commons": {"locked": "1.12.2", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.platform:junit-platform-engine"]}, "org.junit.platform:junit-platform-engine": {"locked": "1.12.2", "transitive": ["org.junit.vintage:junit-vintage-engine"]}, "org.junit.vintage:junit-vintage-engine": {"locked": "5.12.2", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "org.mockito:mockito-core": {"locked": "4.5.1", "transitive": ["org.mockito:mockito-junit-jupiter", "org.springframework.boot:spring-boot-starter-test"]}, "org.mockito:mockito-junit-jupiter": {"locked": "4.5.1", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.opentest4j:opentest4j": {"locked": "1.3.0", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.platform:junit-platform-engine"]}, "org.ow2.asm:asm": {"locked": "9.1", "transitive": ["net.minidev:accessors-smart"]}, "org.skyscreamer:jsonassert": {"locked": "1.5.1", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.slf4j:slf4j-api": {"locked": "1.7.36", "transitive": ["com.jayway.jsonpath:json-path", "com.netflix.servo:servo-core", "com.netflix.spring:spring-boot-netflix", "com.netflix.zuul:zuul-core", "netflix:server-context"]}, "org.springframework.boot:spring-boot": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot-autoconfigure", "org.springframework.boot:spring-boot-starter", "org.springframework.boot:spring-boot-test", "org.springframework.boot:spring-boot-test-autoconfigure"]}, "org.springframework.boot:spring-boot-autoconfigure": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot-starter", "org.springframework.boot:spring-boot-test-autoconfigure"]}, "org.springframework.boot:spring-boot-starter": {"locked": "2.7.18", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.springframework.boot:spring-boot-starter-test": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "org.springframework.boot:spring-boot-test": {"locked": "2.7.18", "transitive": ["org.springframework.boot:spring-boot-starter-test", "org.springframework.boot:spring-boot-test-autoconfigure"]}, "org.springframework.boot:spring-boot-test-autoconfigure": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix-test", "org.springframework.boot:spring-boot-starter-test"]}, "org.springframework.security:spring-security-core": {"locked": "5.8.13", "transitive": ["org.springframework.security:spring-security-test", "org.springframework.security:spring-security-web"]}, "org.springframework.security:spring-security-crypto": {"locked": "5.8.13", "transitive": ["org.springframework.security:spring-security-core"]}, "org.springframework.security:spring-security-test": {"locked": "5.8.13", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test", "com.netflix.spring:spring-boot-netflix-test"]}, "org.springframework.security:spring-security-web": {"locked": "5.8.13", "transitive": ["org.springframework.security:spring-security-test"]}, "org.springframework:spring-aop": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-context"]}, "org.springframework:spring-beans": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-aop", "org.springframework:spring-context", "org.springframework:spring-web"]}, "org.springframework:spring-context": {"locked": "5.3.37", "transitive": ["org.springframework.boot:spring-boot", "org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web"]}, "org.springframework:spring-core": {"locked": "5.3.37", "transitive": ["com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot", "org.springframework.boot:spring-boot-starter", "org.springframework.boot:spring-boot-starter-test", "org.springframework.security:spring-security-core", "org.springframework.security:spring-security-test", "org.springframework.security:spring-security-web", "org.springframework:spring-aop", "org.springframework:spring-beans", "org.springframework:spring-context", "org.springframework:spring-expression", "org.springframework:spring-test", "org.springframework:spring-web"]}, "org.springframework:spring-expression": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-context"]}, "org.springframework:spring-jcl": {"locked": "5.3.37", "transitive": ["commons-configuration:commons-configuration", "org.apache.httpcomponents:httpclient", "org.springframework:spring-core"]}, "org.springframework:spring-test": {"locked": "5.3.37", "transitive": ["com.netflix.spring:spring-boot-netflix-test", "org.springframework.boot:spring-boot-starter-test", "org.springframework.security:spring-security-test"]}, "org.springframework:spring-web": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-web"]}, "org.xmlunit:xmlunit-core": {"locked": "2.9.1", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.yaml:snakeyaml": {"locked": "1.29", "transitive": ["netflix:ua_parser", "org.springframework.boot:spring-boot-starter"]}, "xmlpull:xmlpull": {"locked": "*******", "transitive": ["io.github.x-stream:mxparser"]}}, "testRuntimeClasspath": {"antlr:antlr": {"locked": "2.7.7", "transitive": ["org.antlr:antlr-runtime", "org.antlr:stringtemplate"]}, "aopalliance:aopalliance": {"locked": "1.0", "transitive": ["com.google.inject:guice"]}, "build.buf.protoc-gen-validate:pgv-java-stub": {"locked": "1.1.0", "transitive": ["com.netflix.mesh:mesh-api-java"]}, "ch.qos.reload4j:reload4j": {"locked": "1.2.25", "transitive": ["com.netflix.blitz4j:blitz4j", "netflix:platform-jdk-compat"]}, "com.amazonaws:aws-java-sdk-core": {"locked": "1.12.783", "transitive": ["netflix:platform-core"]}, "com.atomicjar:testcontainers-cloud-provider-strategy": {"locked": "0.0.41", "transitive": ["com.netflix.nebula.testcontainers:testcontainers-cloud-configurer"]}, "com.fasterxml.jackson.core:jackson-annotations": {"locked": "2.17.3", "transitive": ["com.fasterxml.jackson.core:jackson-databind", "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", "com.netflix.archaius:archaius-core", "com.netflix.eureka:eureka-client"]}, "com.fasterxml.jackson.core:jackson-core": {"locked": "2.17.3", "transitive": ["com.fasterxml.jackson.core:jackson-databind", "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor", "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", "com.netflix.archaius:archaius-core", "com.netflix.eureka:eureka-client", "com.netflix.microcontext:microcontext-init", "com.netflix.zuul:zuul-core"]}, "com.fasterxml.jackson.core:jackson-databind": {"locked": "2.17.3", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor", "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", "com.netflix.archaius:archaius-core", "com.netflix.eureka:eureka-client", "com.netflix.ksclient:ksclient-api", "com.netflix.microcontext:microcontext-init", "com.netflix.spring:spring-boot-netflix", "com.netflix.zuul:zuul-core", "netflix.grpc:netflix-grpc-common", "netflix:platform-core"]}, "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor": {"locked": "2.17.3", "transitive": ["com.amazonaws:aws-java-sdk-core"]}, "com.fasterxml.jackson.datatype:jackson-datatype-jsr310": {"locked": "2.17.3", "transitive": ["com.netflix.spring:spring-boot-netflix"]}, "com.github.andrewoma.dexx:dexx-collections": {"locked": "0.2", "transitive": ["com.github.vlsi.compactmap:compactmap"]}, "com.github.vlsi.compactmap:compactmap": {"locked": "2.0", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.google.android:annotations": {"locked": "*******", "transitive": ["netflix.io.grpc:grpc-core-nflx"]}, "com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1", "transitive": ["com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-core", "com.netflix.ribbon:ribbon-eureka", "netflix:basicTypes", "netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge", "netflix:geoip-common"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "com.google.guava:guava", "com.netflix.archaius:archaius-core", "com.netflix.eureka:eureka-client", "com.netflix.netflix-commons:netflix-infix", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:dts-standalone-common", "netflix:netflix-config"]}, "com.google.code.gson:gson": {"locked": "2.10.1", "transitive": ["com.netflix.netflix-commons:netflix-infix", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-core-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["com.google.guava:guava", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-services-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "com.google.guava:failureaccess": {"locked": "1.0.1", "transitive": ["com.google.guava:guava"]}, "com.google.guava:guava": {"locked": "31.1-jre", "transitive": ["com.google.inject:guice", "com.netflix.archaius:archaius-core", "com.netflix.microcontext:microcontext-init", "com.netflix.netflix-commons:netflix-infix", "com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-core", "com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "com.netflix.zuul:zuul-core", "com.netflix.zuul:zuul-discovery", "netflix.grpc:netflix-grpc-common", "netflix:platform-core", "netflix:platform-logimpl", "netflix:platform-utils"]}, "com.google.guava:listenablefuture": {"locked": "9999.0-empty-to-avoid-conflict-with-guava", "transitive": ["com.google.guava:guava"]}, "com.google.inject.extensions:guice-multibindings": {"locked": "4.1.0", "transitive": ["netflix:atlas-client"]}, "com.google.inject:guice": {"locked": "4.1.0", "transitive": ["com.google.inject.extensions:guice-multibindings", "com.netflix.eureka:eureka-client", "com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client"]}, "com.google.j2objc:j2objc-annotations": {"locked": "1.3", "transitive": ["com.google.guava:guava"]}, "com.google.re2j:re2j": {"locked": "1.7", "transitive": ["build.buf.protoc-gen-validate:pgv-java-stub"]}, "com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}, "com.ibm.icu:icu4j": {"locked": "73.2", "transitive": ["netflix:nfi18n-core"]}, "com.jayway.jsonpath:json-path": {"locked": "2.7.0", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["com.netflix.microcontext:microcontext-model", "com.netflix.passport.test:passport-test-core", "netflix:passport"]}, "com.netflix.archaius:archaius-core": {"locked": "0.7.12", "transitive": ["com.netflix.blitz4j:blitz4j", "com.netflix.eureka:eureka-client", "com.netflix.netflix-commons:netflix-eventbus", "com.netflix.ribbon:ribbon-archaius", "com.netflix.zuul:zuul-core", "netflix:netflix-config", "netflix:platform-core"]}, "com.netflix.archaius:archaius2-api": {"locked": "2.8.5", "transitive": ["com.netflix.archaius:archaius2-core", "com.netflix.mesh:mesh-integration-java"]}, "com.netflix.archaius:archaius2-core": {"locked": "2.8.5", "transitive": ["com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client"]}, "com.netflix.blitz4j:blitz4j": {"locked": "1.42.0", "transitive": ["netflix:platform-core"]}, "com.netflix.concurrency-limits:concurrency-limits-core": {"locked": "0.5.3", "transitive": ["com.netflix.concurrency-limits:concurrency-limits-grpc", "netflix.grpc:netflix-grpc-common"]}, "com.netflix.concurrency-limits:concurrency-limits-grpc": {"locked": "0.5.3", "transitive": ["netflix.grpc:netflix-grpc-common"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0", "transitive": ["com.netflix.microcontext:microcontext-model"]}, "com.netflix.eureka:eureka-client": {"locked": "1.10.18", "transitive": ["com.netflix.ribbon:ribbon-eureka", "com.netflix.zuul:zuul-core", "com.netflix.zuul:zuul-discovery"]}, "com.netflix.frigga:frigga": {"locked": "0.26.0", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix:platform-core"]}, "com.netflix.governator:governator-api": {"locked": "1.17.13", "transitive": ["netflix:nfi18n-core"]}, "com.netflix.ksclient:ksclient-api": {"locked": "3.11.0", "transitive": ["netflix:platform-core", "netflix:platform-logimpl"]}, "com.netflix.mesh:mesh-api-java": {"locked": "0.60.0", "transitive": ["com.netflix.mesh:mesh-integration-java"]}, "com.netflix.mesh:mesh-integration-java": {"locked": "0.5.93", "transitive": ["netflix.grpc:netflix-grpc-common"]}, "com.netflix.microcontext:microcontext-init": {"project": true}, "com.netflix.microcontext:microcontext-model": {"project": true, "transitive": ["com.netflix.microcontext:microcontext-init"]}, "com.netflix.nebula.testcontainers:testcontainers-cloud-configurer": {"locked": "1.16.0"}, "com.netflix.netflix-commons:netflix-commons-util": {"locked": "0.3.0", "transitive": ["com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.zuul:zuul-core"]}, "com.netflix.netflix-commons:netflix-eventbus": {"locked": "0.3.0", "transitive": ["com.netflix.eureka:eureka-client", "netflix:nf-eventbus-core", "netflix:platform-core"]}, "com.netflix.netflix-commons:netflix-infix": {"locked": "0.3.0", "transitive": ["com.netflix.netflix-commons:netflix-eventbus"]}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.passport.test:passport-test-core": {"locked": "0.41.0"}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "com.netflix.ribbon:ribbon-archaius": {"locked": "2.4.4", "transitive": ["com.netflix.zuul:zuul-core", "com.netflix.zuul:zuul-discovery"]}, "com.netflix.ribbon:ribbon-core": {"locked": "2.4.4", "transitive": ["com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-eureka", "com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.zuul:zuul-core", "com.netflix.zuul:zuul-discovery"]}, "com.netflix.ribbon:ribbon-eureka": {"locked": "2.4.4", "transitive": ["com.netflix.zuul:zuul-discovery"]}, "com.netflix.ribbon:ribbon-loadbalancer": {"locked": "2.4.4", "transitive": ["com.netflix.ribbon:ribbon-eureka", "com.netflix.zuul:zuul-discovery"]}, "com.netflix.servo:servo-apache": {"locked": "0.13.2", "transitive": ["netflix:atlas-client"]}, "com.netflix.servo:servo-core": {"locked": "0.13.2", "transitive": ["com.netflix.blitz4j:blitz4j", "com.netflix.eureka:eureka-client", "com.netflix.netflix-commons:netflix-eventbus", "com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.servo:servo-apache", "com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client", "netflix:geoip-common", "netflix:netflix-config"]}, "com.netflix.spectator:spectator-api": {"locked": "1.8.12", "transitive": ["com.netflix.ksclient:ksclient-api", "com.netflix.passport.test:passport-test-core", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "com.netflix.spectator:spectator-ext-gc", "com.netflix.spectator:spectator-ext-ipc", "com.netflix.spectator:spectator-ext-jvm", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spectator:spectator-reg-atlas", "com.netflix.zuul:zuul-core", "netflix.grpc:netflix-grpc-common", "netflix:atlas-client", "netflix:platform-core", "netflix:server-context"]}, "com.netflix.spectator:spectator-ext-gc": {"locked": "1.8.12", "transitive": ["com.netflix.spectator:spectator-nflx-plugin"]}, "com.netflix.spectator:spectator-ext-ipc": {"locked": "1.8.12", "transitive": ["com.netflix.spectator:spectator-reg-atlas", "netflix.grpc:netflix-grpc-common", "netflix:atlas-client"]}, "com.netflix.spectator:spectator-ext-jvm": {"locked": "1.8.12", "transitive": ["com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client"]}, "com.netflix.spectator:spectator-nflx-plugin": {"locked": "1.8.12", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix:atlas-client"]}, "com.netflix.spectator:spectator-nflx-tagging": {"locked": "1.8.12", "transitive": ["com.netflix.spectator:spectator-nflx-plugin"]}, "com.netflix.spectator:spectator-reg-atlas": {"locked": "1.8.12", "transitive": ["com.netflix.spectator:spectator-nflx-plugin"]}, "com.netflix.spring.devagent:sbn-dev-agent-client": {"locked": "0.0.13", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "com.netflix.spring:spring-boot-netflix": {"locked": "2.7.226", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "com.netflix.spring:spring-boot-netflix-application-dependencies": {"locked": "2.7.226"}, "com.netflix.spring:spring-boot-netflix-starter-test": {"locked": "2.7.226"}, "com.netflix.spring:spring-boot-netflix-test": {"locked": "2.7.226", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "com.netflix.tracing:netflix-tracing-tags": {"locked": "0.660.0", "transitive": ["netflix:platform-core"]}, "com.netflix.zuul:zuul-core": {"locked": "2.5.0"}, "com.netflix.zuul:zuul-discovery": {"locked": "2.5.0", "transitive": ["com.netflix.zuul:zuul-core"]}, "com.sun.activation:jakarta.activation": {"locked": "1.2.2", "transitive": ["com.sun.xml.bind:jaxb-impl"]}, "com.sun.jersey.contribs:jersey-apache-client4": {"locked": "1.20.1", "transitive": ["com.netflix.eureka:eureka-client"]}, "com.sun.jersey:jersey-client": {"locked": "1.20.1", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey.contribs:jersey-apache-client4", "netflix:platform-core", "netflix:platform-logimpl"]}, "com.sun.jersey:jersey-core": {"locked": "1.20.1", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey:jersey-client", "netflix:platform-jdk-compat"]}, "com.sun.xml.bind:jaxb-impl": {"locked": "2.3.6", "transitive": ["netflix:platform-jdk-compat"]}, "com.thoughtworks.xstream:xstream": {"locked": "1.4.21", "transitive": ["com.netflix.eureka:eureka-client", "netflix:netflix-config"]}, "com.typesafe:config": {"locked": "1.4.3", "transitive": ["com.netflix.spectator:spectator-ext-jvm", "netflix:atlas-client"]}, "commons-beanutils:commons-beanutils": {"locked": "1.9.4", "transitive": ["netflix:netflix-config"]}, "commons-codec:commons-codec": {"locked": "1.16.0", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.netflix.archaius:archaius2-core", "netflix:platform-core", "org.apache.httpcomponents:httpclient"]}, "commons-collections:commons-collections": {"locked": "3.2.2", "transitive": ["com.netflix.blitz4j:blitz4j", "commons-beanutils:commons-beanutils", "netflix:platform-core", "netflix:ua_parser"]}, "commons-configuration:commons-configuration": {"locked": "1.10", "transitive": ["com.netflix.archaius:archaius-core", "com.netflix.blitz4j:blitz4j", "com.netflix.eureka:eureka-client", "com.netflix.ribbon:ribbon-archaius", "netflix:netflix-config", "netflix:nfi18n-core", "netflix:nflibrary-slim", "netflix:platform-core", "netflix:platform-logimpl"]}, "commons-io:commons-io": {"locked": "2.7", "transitive": ["netflix:nfi18n-core"]}, "commons-jxpath:commons-jxpath": {"locked": "1.3", "transitive": ["com.netflix.netflix-commons:netflix-infix"]}, "commons-lang:commons-lang": {"locked": "2.6", "transitive": ["com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-core", "commons-configuration:commons-configuration", "netflix:geoip-common", "netflix:platform-core", "netflix:platform-utils"]}, "commons-validator:commons-validator": {"locked": "1.8.0", "transitive": ["build.buf.protoc-gen-validate:pgv-java-stub"]}, "io.github.x-stream:mxparser": {"locked": "1.2.2", "transitive": ["com.thoughtworks.xstream:xstream"]}, "io.netty.incubator:netty-incubator-transport-classes-io_uring": {"locked": "0.0.25.Final", "transitive": ["io.netty.incubator:netty-incubator-transport-native-io_uring"]}, "io.netty.incubator:netty-incubator-transport-native-io_uring": {"locked": "0.0.25.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-buffer": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty.incubator:netty-incubator-transport-classes-io_uring", "io.netty:netty-codec", "io.netty:netty-codec-haproxy", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler", "io.netty:netty-transport", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-classes-kqueue", "io.netty:netty-transport-native-epoll", "io.netty:netty-transport-native-kqueue", "io.netty:netty-transport-native-unix-common"]}, "io.netty:netty-codec": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-codec-haproxy", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler"]}, "io.netty:netty-codec-haproxy": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-codec-http": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-codec-http2"]}, "io.netty:netty-codec-http2": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-common": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty.incubator:netty-incubator-transport-classes-io_uring", "io.netty:netty-buffer", "io.netty:netty-codec", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler", "io.netty:netty-resolver", "io.netty:netty-transport", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-classes-kqueue", "io.netty:netty-transport-native-epoll", "io.netty:netty-transport-native-kqueue", "io.netty:netty-transport-native-unix-common"]}, "io.netty:netty-handler": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty:netty-codec-http", "io.netty:netty-codec-http2"]}, "io.netty:netty-resolver": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-handler", "io.netty:netty-transport"]}, "io.netty:netty-tcnative-boringssl-static": {"locked": "2.0.61.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-tcnative-classes": {"locked": "2.0.61.Final", "transitive": ["io.netty:netty-tcnative-boringssl-static"]}, "io.netty:netty-transport": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core", "io.netty.incubator:netty-incubator-transport-classes-io_uring", "io.netty:netty-codec", "io.netty:netty-codec-haproxy", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-handler", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-classes-kqueue", "io.netty:netty-transport-native-epoll", "io.netty:netty-transport-native-kqueue", "io.netty:netty-transport-native-unix-common"]}, "io.netty:netty-transport-classes-epoll": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-transport-native-epoll"]}, "io.netty:netty-transport-classes-kqueue": {"locked": "4.1.107.Final", "transitive": ["io.netty:netty-transport-native-kqueue"]}, "io.netty:netty-transport-native-epoll": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-transport-native-kqueue": {"locked": "4.1.107.Final", "transitive": ["com.netflix.zuul:zuul-core"]}, "io.netty:netty-transport-native-unix-common": {"locked": "4.1.107.Final", "transitive": ["io.netty.incubator:netty-incubator-transport-classes-io_uring", "io.netty:netty-handler", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-classes-kqueue", "io.netty:netty-transport-native-epoll", "io.netty:netty-transport-native-kqueue"]}, "io.perfmark:perfmark-api": {"locked": "0.26.0", "transitive": ["com.netflix.zuul:zuul-core", "netflix.io.grpc:grpc-core-nflx"]}, "io.reactivex:rxjava": {"locked": "1.3.8", "transitive": ["com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.zuul:zuul-core"]}, "io.zipkin.brave:brave": {"locked": "5.13.3", "transitive": ["netflix:platform-core"]}, "io.zipkin.reporter2:zipkin-reporter": {"locked": "2.16.3", "transitive": ["io.zipkin.reporter2:zipkin-reporter-brave"]}, "io.zipkin.reporter2:zipkin-reporter-brave": {"locked": "2.16.3", "transitive": ["io.zipkin.brave:brave"]}, "io.zipkin.zipkin2:zipkin": {"locked": "2.23.2", "transitive": ["io.zipkin.reporter2:zipkin-reporter"]}, "jakarta.activation:jakarta.activation-api": {"locked": "1.2.2", "transitive": ["jakarta.xml.bind:jakarta.xml.bind-api"]}, "jakarta.annotation:jakarta.annotation-api": {"locked": "1.3.5", "transitive": ["org.springframework.boot:spring-boot-starter"]}, "jakarta.xml.bind:jakarta.xml.bind-api": {"locked": "2.3.3", "transitive": ["com.sun.xml.bind:jaxb-impl", "org.springframework.boot:spring-boot-starter-test"]}, "javax.activation:javax.activation-api": {"locked": "1.2.0", "transitive": ["javax.xml.bind:jaxb-api"]}, "javax.annotation:javax.annotation-api": {"locked": "1.3.1", "transitive": ["netflix.grpc:netflix-grpc-common"]}, "javax.inject:javax.inject": {"locked": "1", "transitive": ["com.google.inject:guice", "com.netflix.archaius:archaius2-api", "com.netflix.governator:governator-api", "com.netflix.ksclient:ksclient-api", "com.netflix.netflix-commons:netflix-commons-util", "com.netflix.passport.test:passport-test-core", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.zuul:zuul-core", "netflix.grpc:netflix-grpc-common", "netflix:dts-standalone-common", "netflix:nf-eventbus-core"]}, "javax.servlet:javax.servlet-api": {"locked": "3.1.0", "transitive": ["com.netflix.netflix-commons:netflix-infix", "netflix:server-context"]}, "javax.ws.rs:jsr311-api": {"locked": "1.1.1", "transitive": ["com.netflix.eureka:eureka-client", "com.sun.jersey:jersey-core", "netflix:platform-core"]}, "javax.xml.bind:jaxb-api": {"locked": "2.5.0", "transitive": ["netflix:platform-jdk-compat"]}, "joda-time:joda-time": {"locked": "2.12.7", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.netflix.netflix-commons:netflix-infix", "netflix:platform-utils"]}, "junit:junit": {"locked": "4.13.2", "transitive": ["org.junit.vintage:junit-vintage-engine"]}, "net.bytebuddy:byte-buddy": {"locked": "1.12.9", "transitive": ["org.mockito:mockito-core"]}, "net.bytebuddy:byte-buddy-agent": {"locked": "1.12.9", "transitive": ["org.mockito:mockito-core"]}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "net.minidev:accessors-smart": {"locked": "2.4.7", "transitive": ["net.minidev:json-smart"]}, "net.minidev:json-smart": {"locked": "2.4.7", "transitive": ["com.jayway.jsonpath:json-path"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["build.buf.protoc-gen-validate:pgv-java-stub", "com.google.api.grpc:proto-google-common-protos", "com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.mesh:mesh-api-java", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:basicTypes-proto", "netflix:dts-standalone-common", "netflix:metatron-ipc-common", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["build.buf.protoc-gen-validate:pgv-java-stub", "com.netflix.mesh:mesh-integration-java", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-services-nflx", "netflix:dts-standalone-common"]}, "netflix.grpc:netflix-grpc-common": {"locked": "1.63.20", "transitive": ["netflix:dts-standalone-common"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.mesh:mesh-api-java", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-common", "netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-context-nflx", "netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-inprocess-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx", "netflix.io.grpc:grpc-util-nflx"]}, "netflix.io.grpc:grpc-context-nflx": {"locked": "1.63.0", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.io.grpc:grpc-core-nflx"]}, "netflix.io.grpc:grpc-core-nflx": {"locked": "1.63.0", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.io.grpc:grpc-inprocess-nflx", "netflix.io.grpc:grpc-services-nflx", "netflix.io.grpc:grpc-util-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-inprocess-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-services-nflx", "netflix.io.grpc:grpc-stub-nflx", "netflix.io.grpc:grpc-util-nflx"]}, "netflix.io.grpc:grpc-inprocess-nflx": {"locked": "1.63.0", "transitive": ["netflix.grpc:netflix-grpc-common"]}, "netflix.io.grpc:grpc-protobuf-lite-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.mesh:mesh-api-java", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-services-nflx", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-services-nflx": {"locked": "1.63.0", "transitive": ["netflix.grpc:netflix-grpc-common"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.mesh:mesh-api-java", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-common", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-services-nflx", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-util-nflx": {"locked": "1.63.0", "transitive": ["netflix.grpc:netflix-grpc-common", "netflix.io.grpc:grpc-services-nflx"]}, "netflix:atlas-client": {"locked": "2.29.10", "transitive": ["netflix:platform-utils"]}, "netflix:basicTypes": {"locked": "1.86.0", "transitive": ["netflix:basicTypes-proto-bridge", "netflix:geoip-common", "netflix:nfi18n-core", "netflix:server-context"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:basicTypes-proto-bridge", "netflix:passport"]}, "netflix:basicTypes-proto-bridge": {"locked": "1.86.0", "transitive": ["com.netflix.microcontext:microcontext-init"]}, "netflix:dts-standalone-common": {"locked": "603.257.0"}, "netflix:geoip-common": {"locked": "2.324", "transitive": ["com.netflix.microcontext:microcontext-init"]}, "netflix:i18n-dictionaries": {"locked": "0.205.0", "transitive": ["netflix:nfi18n-core"]}, "netflix:metatron-ipc-common": {"locked": "1.441.0", "transitive": ["netflix.grpc:netflix-grpc-common"]}, "netflix:moduleRegistry": {"locked": "1.50.0", "transitive": ["netflix:nflibrary-slim"]}, "netflix:netflix-config": {"locked": "4.7.633", "transitive": ["netflix:geoip-common", "netflix:nflibrary-slim", "netflix:platform-core", "netflix:platform-logimpl", "netflix:platform-utils"]}, "netflix:nf-eventbus-core": {"locked": "1.32.0", "transitive": ["netflix:platform-core", "netflix:platform-logimpl"]}, "netflix:nfi18n-core": {"locked": "2.248", "transitive": ["com.netflix.microcontext:microcontext-init"]}, "netflix:nflibrary-slim": {"locked": "4.7.633", "transitive": ["netflix:nfi18n-core"]}, "netflix:ngl": {"locked": "0.49.0", "transitive": ["netflix:nfi18n-core"]}, "netflix:passport": {"locked": "4.6.0", "transitive": ["com.netflix.passport.test:passport-test-core", "netflix:subscriberservice-proto-definition"]}, "netflix:platform-core": {"locked": "4.7.633", "transitive": ["netflix:platform-logimpl", "netflix:platform-utils"]}, "netflix:platform-jdk-compat": {"locked": "4.7.633", "transitive": ["netflix:platform-core"]}, "netflix:platform-logimpl": {"locked": "4.7.633", "transitive": ["netflix:platform-utils"]}, "netflix:platform-utils": {"locked": "4.7.633", "transitive": ["netflix:nfi18n-core"]}, "netflix:server-context": {"locked": "4.7.633", "transitive": ["com.netflix.microcontext:microcontext-init", "com.netflix.passport.test:passport-test-core", "netflix:geoip-common", "netflix:netflix-config", "netflix:platform-core", "netflix:platform-utils"]}, "netflix:statistics": {"locked": "1.7.0", "transitive": ["com.netflix.ribbon:ribbon-loadbalancer", "netflix:platform-utils"]}, "netflix:subscriberservice-common": {"locked": "5.66.0", "transitive": ["com.netflix.microcontext:microcontext-init"]}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:subscriberservice-common"]}, "netflix:ua_parser": {"locked": "0.6.0"}, "nl.basjes.collections:prefixmap": {"locked": "1.0", "transitive": ["netflix:dts-standalone-common"]}, "org.antlr:antlr-runtime": {"locked": "3.4", "transitive": ["com.netflix.netflix-commons:netflix-infix"]}, "org.antlr:stringtemplate": {"locked": "3.2.1", "transitive": ["org.antlr:antlr-runtime"]}, "org.apache.commons:commons-lang3": {"locked": "3.12.0", "transitive": ["com.netflix.archaius:archaius2-core", "netflix.grpc:netflix-grpc-common"]}, "org.apache.commons:commons-math": {"locked": "2.2", "transitive": ["com.netflix.netflix-commons:netflix-eventbus", "netflix:platform-utils"]}, "org.apache.httpcomponents:httpclient": {"locked": "4.5.13", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.netflix.eureka:eureka-client", "com.sun.jersey.contribs:jersey-apache-client4"]}, "org.apache.httpcomponents:httpcore": {"locked": "4.4.13", "transitive": ["org.apache.httpcomponents:httpclient"]}, "org.apiguardian:apiguardian-api": {"locked": "1.1.2", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.jupiter:junit-jupiter-engine", "org.junit.jupiter:junit-jupiter-params", "org.junit.platform:junit-platform-commons", "org.junit.platform:junit-platform-engine", "org.junit.platform:junit-platform-launcher", "org.junit.vintage:junit-vintage-engine"]}, "org.aspectj:aspectjweaver": {"locked": "1.9.5", "transitive": ["netflix:platform-core"]}, "org.assertj:assertj-core": {"locked": "3.22.0", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.bouncycastle:bcpkix-jdk18on": {"locked": "1.76", "transitive": ["com.netflix.zuul:zuul-core"]}, "org.bouncycastle:bcprov-jdk18on": {"locked": "1.76", "transitive": ["com.netflix.zuul:zuul-core", "org.bouncycastle:bcpkix-jdk18on", "org.bouncycastle:bcutil-jdk18on"]}, "org.bouncycastle:bcutil-jdk18on": {"locked": "1.76", "transitive": ["org.bouncycastle:bcpkix-jdk18on"]}, "org.checkerframework:checker-qual": {"locked": "3.12.0", "transitive": ["com.google.guava:guava", "netflix.io.grpc:grpc-guava-shaded-nflx"]}, "org.codehaus.jackson:jackson-core-asl": {"locked": "1.10.3", "transitive": ["netflix:platform-jdk-compat", "org.codehaus.jackson:jackson-mapper-asl"]}, "org.codehaus.jackson:jackson-mapper-asl": {"locked": "1.10.3", "transitive": ["netflix:netflix-config"]}, "org.codehaus.jettison:jettison": {"locked": "1.4.0", "transitive": ["com.netflix.eureka:eureka-client"]}, "org.codehaus.mojo:animal-sniffer-annotations": {"locked": "1.23", "transitive": ["netflix.io.grpc:grpc-core-nflx", "netflix.io.grpc:grpc-util-nflx"]}, "org.hamcrest:hamcrest": {"locked": "2.2", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.junit.jupiter:junit-jupiter": {"locked": "5.12.2", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.junit.jupiter:junit-jupiter-api": {"locked": "5.12.2", "transitive": ["org.junit.jupiter:junit-jupiter", "org.junit.jupiter:junit-jupiter-engine", "org.junit.jupiter:junit-jupiter-params", "org.mockito:mockito-junit-jupiter"]}, "org.junit.jupiter:junit-jupiter-engine": {"locked": "5.12.2", "transitive": ["org.junit.jupiter:junit-jupiter"]}, "org.junit.jupiter:junit-jupiter-params": {"locked": "5.12.2", "transitive": ["org.junit.jupiter:junit-jupiter"]}, "org.junit.platform:junit-platform-commons": {"locked": "1.12.2", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.platform:junit-platform-engine"]}, "org.junit.platform:junit-platform-engine": {"locked": "1.12.2", "transitive": ["org.junit.jupiter:junit-jupiter-engine", "org.junit.platform:junit-platform-launcher", "org.junit.vintage:junit-vintage-engine"]}, "org.junit.platform:junit-platform-launcher": {"locked": "1.12.2"}, "org.junit.vintage:junit-vintage-engine": {"locked": "5.12.2", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "org.lz4:lz4-java": {"locked": "1.8.0", "transitive": ["netflix:dts-standalone-common"]}, "org.mockito:mockito-core": {"locked": "4.5.1", "transitive": ["org.mockito:mockito-junit-jupiter", "org.springframework.boot:spring-boot-starter-test"]}, "org.mockito:mockito-junit-jupiter": {"locked": "4.5.1", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.objenesis:objenesis": {"locked": "3.2", "transitive": ["org.mockito:mockito-core"]}, "org.opentest4j:opentest4j": {"locked": "1.3.0", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.platform:junit-platform-engine"]}, "org.ow2.asm:asm": {"locked": "9.1", "transitive": ["net.minidev:accessors-smart"]}, "org.skyscreamer:jsonassert": {"locked": "1.5.1", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.slf4j:slf4j-api": {"locked": "1.7.36", "transitive": ["com.jayway.jsonpath:json-path", "com.netflix.archaius:archaius-core", "com.netflix.archaius:archaius2-api", "com.netflix.archaius:archaius2-core", "com.netflix.blitz4j:blitz4j", "com.netflix.concurrency-limits:concurrency-limits-core", "com.netflix.concurrency-limits:concurrency-limits-grpc", "com.netflix.microcontext:microcontext-init", "com.netflix.netflix-commons:netflix-commons-util", "com.netflix.netflix-commons:netflix-eventbus", "com.netflix.netflix-commons:netflix-infix", "com.netflix.ribbon:ribbon-archaius", "com.netflix.ribbon:ribbon-core", "com.netflix.ribbon:ribbon-eureka", "com.netflix.ribbon:ribbon-loadbalancer", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "com.netflix.spectator:spectator-api", "com.netflix.spectator:spectator-ext-gc", "com.netflix.spectator:spectator-ext-ipc", "com.netflix.spectator:spectator-ext-jvm", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spectator:spectator-nflx-tagging", "com.netflix.spectator:spectator-reg-atlas", "com.netflix.spring:spring-boot-netflix", "com.netflix.zuul:zuul-core", "com.netflix.zuul:zuul-discovery", "netflix.grpc:netflix-grpc-common", "netflix:atlas-client", "netflix:dts-standalone-common", "netflix:geoip-common", "netflix:metatron-ipc-common", "netflix:nf-eventbus-core", "netflix:nflibrary-slim", "netflix:server-context", "netflix:subscriberservice-common"]}, "org.springframework.boot:spring-boot": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot-autoconfigure", "org.springframework.boot:spring-boot-starter", "org.springframework.boot:spring-boot-test", "org.springframework.boot:spring-boot-test-autoconfigure"]}, "org.springframework.boot:spring-boot-autoconfigure": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot-starter", "org.springframework.boot:spring-boot-test-autoconfigure"]}, "org.springframework.boot:spring-boot-starter": {"locked": "2.7.18", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.springframework.boot:spring-boot-starter-test": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "org.springframework.boot:spring-boot-test": {"locked": "2.7.18", "transitive": ["org.springframework.boot:spring-boot-starter-test", "org.springframework.boot:spring-boot-test-autoconfigure"]}, "org.springframework.boot:spring-boot-test-autoconfigure": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix-test", "org.springframework.boot:spring-boot-starter-test"]}, "org.springframework.security:spring-security-core": {"locked": "5.8.13", "transitive": ["org.springframework.security:spring-security-test", "org.springframework.security:spring-security-web"]}, "org.springframework.security:spring-security-crypto": {"locked": "5.8.13", "transitive": ["org.springframework.security:spring-security-core"]}, "org.springframework.security:spring-security-test": {"locked": "5.8.13", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test", "com.netflix.spring:spring-boot-netflix-test"]}, "org.springframework.security:spring-security-web": {"locked": "5.8.13", "transitive": ["org.springframework.security:spring-security-test"]}, "org.springframework:spring-aop": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-context"]}, "org.springframework:spring-beans": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-aop", "org.springframework:spring-context", "org.springframework:spring-web"]}, "org.springframework:spring-context": {"locked": "5.3.37", "transitive": ["org.springframework.boot:spring-boot", "org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web"]}, "org.springframework:spring-core": {"locked": "5.3.37", "transitive": ["com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot", "org.springframework.boot:spring-boot-starter", "org.springframework.boot:spring-boot-starter-test", "org.springframework.security:spring-security-core", "org.springframework.security:spring-security-test", "org.springframework.security:spring-security-web", "org.springframework:spring-aop", "org.springframework:spring-beans", "org.springframework:spring-context", "org.springframework:spring-expression", "org.springframework:spring-test", "org.springframework:spring-web"]}, "org.springframework:spring-expression": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-context"]}, "org.springframework:spring-jcl": {"locked": "5.3.37", "transitive": ["com.amazonaws:aws-java-sdk-core", "commons-beanutils:commons-beanutils", "commons-configuration:commons-configuration", "netflix:platform-core", "org.apache.httpcomponents:httpclient", "org.springframework:spring-core"]}, "org.springframework:spring-test": {"locked": "5.3.37", "transitive": ["com.netflix.spring:spring-boot-netflix-test", "org.springframework.boot:spring-boot-starter-test", "org.springframework.security:spring-security-test"]}, "org.springframework:spring-web": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-web"]}, "org.xmlunit:xmlunit-core": {"locked": "2.9.1", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.yaml:snakeyaml": {"locked": "1.29", "transitive": ["netflix:ua_parser", "org.springframework.boot:spring-boot-starter"]}, "tools.profiler:async-profiler": {"locked": "2.9", "transitive": ["com.netflix.spring:spring-boot-netflix"]}, "xmlpull:xmlpull": {"locked": "*******", "transitive": ["io.github.x-stream:mxparser"]}}}