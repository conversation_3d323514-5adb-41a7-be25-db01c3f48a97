package com.netflix.microcontext.gateway;

import static com.netflix.microcontext.gateway.Metrics.TagValue.HIT;
import static com.netflix.microcontext.gateway.Metrics.TagValue.MISS;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import com.netflix.grpc.shaded.com.google.common.collect.Lists;
import com.netflix.microcontext.init.headers.Headers;
import com.netflix.passport.test.TestPassport;
import com.netflix.server.context.ContextSerializationException;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.DefaultRegistry;
import com.netflix.spectator.api.Measurement;
import com.netflix.spectator.api.Meter;
import com.netflix.spectator.api.Registry;
import com.netflix.spectator.api.Tag;
import com.netflix.streaming.dts.client.DtsClient;
import com.netflix.streaming.dts.common.UnknownDeviceTypeException;
import com.netflix.streaming.dts.common.model.DeviceType;
import com.netflix.type.NFCountry;
import com.netflix.zuul.context.SessionContext;
import com.netflix.zuul.message.http.HttpQueryParams;
import com.netflix.zuul.message.http.HttpRequestMessage;
import com.netflix.zuul.message.http.HttpRequestMessageImpl;
import io.netty.handler.codec.http.HttpVersion;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import netflix.context.Context;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MetricsTest {

  RequestContext requestContext;
  TestPassport randomTestPassport;
  Registry registry;
  DeviceType deviceType;
  @Mock DtsClient dtsClient;

  @Before
  public void before() {
    registry = new DefaultRegistry();
    requestContext = new RequestContext(null, false);
    randomTestPassport = TestPassport.createRandomTestPassport();

    deviceType = DeviceType.builder().build();
  }

  @Test
  public void trackContextEmpty() throws ContextSerializationException {
    HttpRequestMessage request =
        new HttpRequestMessageImpl(
            new SessionContext(),
            HttpVersion.HTTP_1_1.protocolName(),
            "get",
            "/",
            new HttpQueryParams(),
            null,
            "127.0.0.1",
            "https",
            443,
            "api.netflix.com");

    final boolean init =
        MicrocontextGateway.init(
            MicrocontextGatewayInitParams.builder()
                .request(request)
                .requestContext(requestContext)
                .dtsClient(dtsClient)
                .registry(registry)
                .build());
    assertTrue("Init successful", init);
    final Context microcontext = requestContext.getContext("microcontext");
    assertNotNull(microcontext);
    final Map<String, Meter> meters =
        StreamSupport.stream(registry.spliterator(), false)
            .collect(Collectors.toMap(meter -> meter.id().name(), meter -> meter));
    assertEquals(2, meters.size());
    final Meter paramsMeter = meters.get(Metrics.PARAMS_NAME);
    assertNotNull(paramsMeter);
    final List<Measurement> paramsMeasures =
        StreamSupport.stream(paramsMeter.measure().spliterator(), false).toList();
    assertEquals(1, paramsMeasures.size());
    final Measurement paramsMeasurement = paramsMeasures.get(0);
    assertEquals(1.0, paramsMeasurement.value(), 0);
    Map<String, Tag> paramsTags =
        StreamSupport.stream(paramsMeasurement.id().tags().spliterator(), false)
            .collect(Collectors.toMap(Tag::key, tag -> tag));
    assertEquals(MISS, paramsTags.get(Metrics.GEO.getName()).value());
    assertEquals(MISS, paramsTags.get(Metrics.PASSPORT.getName()).value());
    assertEquals(MISS, paramsTags.get(Metrics.DEVICE_TYPE.getName()).value());
    assertEquals(MISS, paramsTags.get(Metrics.SIGNUP_COUNTRY.getName()).value());

    final Meter initMeter = meters.get(Metrics.INIT_NAME);
    assertNotNull(initMeter);
    final List<Measurement> initMeasurements = Lists.newArrayList(initMeter.measure());
    assertEquals(1, initMeasurements.size());
    final Measurement initMeasurement = initMeasurements.get(0);
    assertEquals(1.0, initMeasurement.value(), 0);
    Map<String, Tag> initTags =
        StreamSupport.stream(initMeasurement.id().tags().spliterator(), false)
            .collect(Collectors.toMap(Tag::key, tag -> tag));
    assertEquals(MISS, initTags.get(Metrics.CLIENT_CONTEXT.getName()).value());
    assertEquals(MISS, initTags.get(Metrics.DEVICE_CONTEXT.getName()).value());
    assertEquals(HIT, initTags.get(Metrics.LOCALE_CONTEXT.getName()).value());
  }

  @Test
  public void trackContextPresent()
      throws ContextSerializationException, UnknownDeviceTypeException {
    Mockito.when(dtsClient.getDeviceTypeFromId(Mockito.anyInt())).thenReturn(deviceType);
    HttpRequestMessage request =
        new HttpRequestMessageImpl(
            new SessionContext(),
            HttpVersion.HTTP_1_1.protocolName(),
            "get",
            "/",
            new HttpQueryParams(),
            null,
            "127.0.0.1",
            "https",
            443,
            "api.netflix.com");
    request.getHeaders().add(Headers.APP_VERSION, "1.2.3");
    request.getHeaders().add(Headers.LOCALES, "en");
    final boolean init =
        MicrocontextGateway.init(
            MicrocontextGatewayInitParams.builder()
                .request(request)
                .passportIdentity(randomTestPassport.toPassportIdentity())
                .dtsClient(dtsClient)
                .geoAttributes(Collections.singletonMap("foo", "bar"))
                .signupCountry(NFCountry.US)
                .requestContext(requestContext)
                .deviceType(deviceType)
                .registry(registry)
                .build());
    assertTrue("Init successful", init);
    final Context microcontext = requestContext.getContext("microcontext");
    assertNotNull(microcontext);
    final Map<String, Meter> meters =
        StreamSupport.stream(registry.spliterator(), false)
            .collect(Collectors.toMap(meter -> meter.id().name(), meter -> meter));
    assertEquals(2, meters.size());
    final Meter paramsMeter = meters.get(Metrics.PARAMS_NAME);
    assertNotNull(paramsMeter);
    final List<Measurement> paramsMeasures =
        StreamSupport.stream(paramsMeter.measure().spliterator(), false).toList();
    assertEquals(1, paramsMeasures.size());
    final Measurement paramsMeasurement = paramsMeasures.get(0);
    assertEquals(1.0, paramsMeasurement.value(), 0);
    Map<String, Tag> paramsTags =
        StreamSupport.stream(paramsMeasurement.id().tags().spliterator(), false)
            .collect(Collectors.toMap(Tag::key, tag -> tag));
    assertEquals(HIT, paramsTags.get(Metrics.GEO.getName()).value());
    assertEquals(HIT, paramsTags.get(Metrics.PASSPORT.getName()).value());
    assertEquals(HIT, paramsTags.get(Metrics.DEVICE_TYPE.getName()).value());
    assertEquals(HIT, paramsTags.get(Metrics.SIGNUP_COUNTRY.getName()).value());

    final Meter initMeter = meters.get(Metrics.INIT_NAME);
    assertNotNull(initMeter);
    final List<Measurement> initMeasurements = Lists.newArrayList(initMeter.measure());
    assertEquals(1, initMeasurements.size());
    final Measurement initMeasurement = initMeasurements.get(0);
    assertEquals(1.0, initMeasurement.value(), 0);
    Map<String, Tag> initTags =
        StreamSupport.stream(initMeasurement.id().tags().spliterator(), false)
            .collect(Collectors.toMap(Tag::key, tag -> tag));
    assertEquals(HIT, initTags.get(Metrics.CLIENT_CONTEXT.getName()).value());
    assertEquals(HIT, initTags.get(Metrics.DEVICE_CONTEXT.getName()).value());
    assertEquals(HIT, initTags.get(Metrics.LOCALE_CONTEXT.getName()).value());
    assertEquals(MISS, initTags.get(Metrics.USER_CONTEXT.getName()).value());
  }
}
