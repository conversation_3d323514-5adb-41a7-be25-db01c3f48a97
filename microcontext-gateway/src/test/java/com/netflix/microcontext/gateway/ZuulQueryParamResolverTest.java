package com.netflix.microcontext.gateway;

import static org.junit.Assert.*;

import com.google.common.collect.Lists;
import com.netflix.microcontext.init.params.ParamNames;
import com.netflix.microcontext.init.request.InputResolver;
import com.netflix.zuul.message.http.HttpQueryParams;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.Test;

public class ZuulQueryParamResolverTest {

  InputResolver headerResolver =
      new ZuulQueryParamsResolver(
          HttpQueryParams.parse("appVersion=5&osVersion=1.0&api=2.0&foo=bar"));

  @Test
  public void get() {
    final Optional<String> appVersion = headerResolver.get("appVersion");
    assertTrue(appVersion.isPresent());
    assertEquals("5", appVersion.get());
  }

  @Test
  public void getMissing() {
    final Optional<String> appVersion = headerResolver.get("random");
    assertFalse(appVersion.isPresent());
  }

  @Test
  public void getCase() {
    final Optional<String> appVersion = headerResolver.get(ParamNames.APP_VERSION);
    assertTrue(appVersion.isPresent());
    assertEquals("5", appVersion.get());
  }

  @Test
  public void getFirst() {
    final Optional<String> osVersion =
        headerResolver.getFirst(Lists.newArrayList("osVersion", "api"));
    assertTrue(osVersion.isPresent());
    assertEquals("1.0", osVersion.get());
  }

  @Test
  public void getFirstMissing() {
    final Optional<String> random = headerResolver.getFirst(Collections.singletonList("random"));
    assertFalse(random.isPresent());
  }

  @Test
  public void getFirstCase() {
    final Optional<String> osVersion = headerResolver.getFirst(ParamNames.ALL_OS_VERSIONS);
    assertTrue(osVersion.isPresent());
    assertEquals("1.0", osVersion.get());
  }

  @Test
  public void getAll() {
    final List<String> appVersions = headerResolver.getAll("appVersion");
    assertFalse(appVersions.isEmpty());
    assertEquals("5", appVersions.get(0));
  }

  @Test
  public void getAllMissing() {
    final List<String> appVersions = headerResolver.getAll("random");
    assertTrue(appVersions.isEmpty());
  }

  @Test
  public void getAllCase() {
    final List<String> appVersions = headerResolver.getAll(ParamNames.APP_VERSION);
    assertFalse(appVersions.isEmpty());
    assertEquals("5", appVersions.get(0));
  }

  @Test(expected = UnsupportedOperationException.class)
  public void getAllMutation() {
    final List<String> appVersions = headerResolver.getAll("appVersion");
    assertFalse(appVersions.isEmpty());
    assertEquals("5", appVersions.get(0));
    appVersions.add("foo");
  }

  @Test
  public void contains() {
    assertTrue(headerResolver.contains("appVersion"));
  }

  @Test
  public void containsCase() {
    assertTrue(headerResolver.contains(ParamNames.APP_VERSION));
  }

  @Test
  public void containsMissing() {
    assertFalse(headerResolver.contains("random"));
  }
}
