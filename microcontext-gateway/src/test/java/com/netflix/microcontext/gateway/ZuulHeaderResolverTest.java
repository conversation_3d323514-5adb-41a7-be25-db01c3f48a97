package com.netflix.microcontext.gateway;

import static org.junit.Assert.*;

import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.zuul.message.Headers;
import java.util.Optional;
import org.junit.Test;

public class ZuulHeaderResolverTest {

  @Test
  public void testCaseInsensitive() {
    final Headers headers = new Headers();
    headers.add("user-agent", "Mozilla/5.0");
    HeaderResolver headerResolver = new ZuulHeaderResolver(headers);
    final Optional<String> header = headerResolver.get("User-Agent");
    assertTrue(header.isPresent());
    assertEquals("Mozilla/5.0", header.get());
  }
}
