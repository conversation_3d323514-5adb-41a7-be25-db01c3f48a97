package com.netflix.microcontext.gateway;

import static org.junit.Assert.*;

import com.netflix.microcontext.init.request.CookieResolver;
import com.netflix.zuul.message.http.Cookies;
import io.netty.handler.codec.http.cookie.DefaultCookie;
import java.util.Optional;
import org.junit.Test;

public class ZuulCookieResolverTest {
  @Test
  public void testCaseInsensitive() {
    final Cookies cookies = new Cookies();
    cookies.add(new DefaultCookie("foo", "value"));
    CookieResolver cookieResolver = new ZuulCookieResolver(cookies);
    final Optional<String> cookie = cookieResolver.get("foo");
    assertTrue(cookie.isPresent());
    assertEquals("value", cookie.get());
  }
}
