package com.netflix.microcontext.gateway;

import com.netflix.microcontext.init.request.CookieResolver;
import com.netflix.zuul.message.http.Cookies;
import io.netty.handler.codec.http.cookie.Cookie;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;

public class ZuulCookieResolver implements CookieResolver {

  private final Cookies cookies;

  public ZuulCookieResolver(Cookies cookies) {
    this.cookies = cookies;
  }

  @Override
  public Optional<String> get(@Nonnull String name) {
    return Optional.ofNullable(cookies.getFirstValue(name));
  }

  @Nonnull
  @Override
  public List<String> getAll(@Nonnull String name) {
    return cookies.get(name).stream().map(Cookie::value).collect(Collectors.toList());
  }

  @Override
  public boolean contains(@Nonnull String name) {
    return cookies.getFirst(name) != null;
  }
}
