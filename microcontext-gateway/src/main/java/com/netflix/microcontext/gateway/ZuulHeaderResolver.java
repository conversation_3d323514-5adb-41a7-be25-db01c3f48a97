package com.netflix.microcontext.gateway;

import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.zuul.message.Headers;
import com.netflix.zuul.message.http.HttpRequestMessage;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nonnull;

public class ZuulHeaderResolver implements HeaderResolver {

  private final Headers headers;

  public ZuulHeaderResolver(HttpRequestMessage request) {
    this.headers = request.getHeaders();
  }

  public ZuulHeaderResolver(Headers headers) {
    this.headers = headers;
  }

  @Override
  public Optional<String> get(@Nonnull String name) {
    return Optional.ofNullable(headers.getFirst(name));
  }

  @Nonnull
  @Override
  public List<String> getAll(@Nonnull String name) {
    return headers.getAll(name);
  }

  @Override
  public boolean contains(@Nonnull String name) {
    return headers.contains(name);
  }
}
