package com.netflix.microcontext.gateway;

import com.google.protobuf.Message;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Registry;
import com.netflix.spectator.api.Tag;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.Context;
import netflix.context.user.UserContext;

public class Metrics {

  public static final String INIT_NAME = "microcontext.gateway.init";
  public static final String PARAMS_NAME = "microcontext.gateway.params";
  private static Id initId;
  private static Id paramsId;
  static final TagValue GEO = new TagValue("mc_geo");
  static final TagValue PASSPORT = new TagValue("mc_passport");
  static final TagValue DEVICE_TYPE = new TagValue("mc_deviceType");
  static final TagValue SIGNUP_COUNTRY = new TagValue("mc_signupCountry");
  static final APRTagValue APR = new APRTagValue("mc_apr");
  static final MessageTagValue CLIENT_CONTEXT = new MessageTagValue("mc_client");
  static final MessageTagValue DEVICE_CONTEXT = new MessageTagValue("mc_device");
  static final UserContextTagValue USER_CONTEXT = new UserContextTagValue("mc_user");
  static final MessageTagValue LOCALE_CONTEXT = new MessageTagValue("mc_locale");

  @Nonnull
  private static Id initId(Registry registry) {
    if (initId == null) {
      initId = registry.createId(INIT_NAME);
    }
    return initId;
  }

  @Nonnull
  private static Id paramsId(Registry registry) {
    if (paramsId == null) {
      paramsId = registry.createId(PARAMS_NAME);
    }
    return paramsId;
  }

  static void trackContext(@Nonnull Context context, Registry registry) {
    Id id =
        initId(registry)
            .withTag(CLIENT_CONTEXT.evaluate(context.getClient()))
            .withTag(DEVICE_CONTEXT.evaluate(context.getDevice()))
            .withTag(USER_CONTEXT.evaluate(context.getUser()))
            .withTag(context.getLocalesCount() > 0 ? LOCALE_CONTEXT.hit : LOCALE_CONTEXT.miss);
    registry.counter(id).increment();
  }

  static void trackParameters(MicrocontextGatewayInitParams params) {
    params
        .getRegistry()
        .counter(
            paramsId(params.getRegistry())
                .withTag(GEO.evaluate(params.getGeoAttributes()))
                .withTag(PASSPORT.evaluate(params.getPassportIdentity()))
                .withTag(DEVICE_TYPE.evaluate(params.getDeviceType()))
                .withTag(APR.evaluate(params.getAccountProfileRemote()))
                .withTag(SIGNUP_COUNTRY.evaluate(params.getSignupCountry())))
        .increment();
  }

  static class UserContextTagValue extends MessageTagValue {

    protected UserContextTagValue(String name) {
      super(name);
    }

    protected Tag evaluate(@Nonnull UserContext m) {
      if (m.getCurrentUser().getFallback()) {
        return fallback;
      }
      return super.evaluate(m);
    }
  }

  static class MessageTagValue extends TagValue {

    protected MessageTagValue(String name) {
      super(name);
    }

    protected Tag evaluate(@Nonnull Message m) {
      return m == m.getDefaultInstanceForType() ? miss : hit;
    }
  }

  static class APRTagValue extends MessageTagValue {

    protected APRTagValue(String name) {
      super(name);
    }

    protected Tag evaluate(@Nullable AccountProfileRemote m) {
      if (m == null) {
        return miss;
      } else if (m.getOptionalIsFallback().orElse(false)) {
        return fallback;
      }
      return super.evaluate(m);
    }
  }

  public static class TagValue {

    public static final String HIT = "hit";
    public static final String MISS = "miss";
    public static final String FALLBACK = "fallback";

    protected final Tag hit;
    protected final Tag miss;
    protected final Tag fallback;

    public String getName() {
      return hit.key();
    }

    protected TagValue(String name) {
      hit = Tag.of(name, HIT);
      miss = Tag.of(name, MISS);
      fallback = Tag.of(name, FALLBACK);
    }

    protected Tag evaluate(Object o) {
      return o == null ? miss : hit;
    }
  }
}
