package com.netflix.microcontext.gateway;

import static com.netflix.microcontext.init.device.DeviceResolvers.ALL_PROPERTIES;

import com.netflix.microcontext.init.device.DeviceResolvers;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.streaming.dts.client.DtsClient;
import com.netflix.streaming.dts.common.UnknownDeviceTypeException;
import com.netflix.streaming.dts.common.model.DeviceType;
import com.netflix.streaming.dts.common.model.DeviceTypeProperties;
import java.util.*;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.device.DeviceContext;

public class GatewayDeviceResolvers {

  public static DeviceContext resolve(
      @Nullable DtsClient dtsClient,
      @Nullable PassportIdentity passport,
      @Nullable DeviceType deviceType,
      HeaderResolver headerResolver,
      ParamResolver paramResolver) {
    try {
      final Optional<String> esn =
          DeviceResolvers.resolveEsn(passport, headerResolver, paramResolver);

      if (passport == null && deviceType == null && esn.isEmpty()) {
        return DeviceContext.getDefaultInstance();
      }

      DeviceType resolvedDeviceType =
          resolveDeviceType(dtsClient, passport, deviceType, esn.orElse(null));

      if (resolvedDeviceType != null) {
        return DeviceResolvers.resolve(
            esn.orElse(null), resolvedDeviceType.getDeviceTypeId(), properties(resolvedDeviceType));
      }

      return esn.map(s -> DeviceContext.newBuilder().setEsn(s).build())
          .orElseGet(DeviceContext::getDefaultInstance);
    } catch (Exception e) {
      DynamicCounter.increment("microcontext.gatewayresolver.devicetype", "result", "exception");
      return DeviceContext.getDefaultInstance();
    }
  }

  @Deprecated
  public static DeviceContext resolve(
      @Nullable PassportIdentity passport,
      @Nullable DeviceType deviceType,
      HeaderResolver headerResolver,
      ParamResolver paramResolver) {

    return resolve(null, passport, deviceType, headerResolver, paramResolver);
  }

  @Nullable
  private static DeviceType resolveDeviceType(
      @Nullable DtsClient dtsClient,
      @Nullable PassportIdentity passport,
      @Nullable DeviceType deviceType,
      @Nullable String esn) {

    if (deviceType != null) {
      if (passport != null) {
        Optional<Integer> deviceTypeIdOpt = passport.getDeviceTypeId();
        if (deviceTypeIdOpt.isPresent()) {
          try {
            // TODO: remove once old interface without DtsClient is removed
            if (dtsClient != null) {
              return dtsClient.getDeviceTypeFromId(deviceTypeIdOpt.get());
            }
            return DeviceType.builder().deviceTypeId(deviceTypeIdOpt.get()).build();
          } catch (UnknownDeviceTypeException e) {
            DynamicCounter.increment(
                "microcontext.resolver.devicetype", "result", "unknownDeviceType");
          }
        }
      }
      return deviceType;
    } else if (esn != null) {
      try {
        // TODO: remove once old interface without DtsClient is removed
        if (dtsClient != null) {
          return dtsClient.getDeviceTypeFromESN(esn);
        }
      } catch (UnknownDeviceTypeException | NoSuchElementException e) {
        DynamicCounter.increment(
            "microcontext.resolver.devicetype", "result", "unknownDeviceTypeForEsn");
      }
    } else {
      DynamicCounter.increment("microcontext.resolver.devicetype", "result", "noEsn");
    }
    return null;
  }

  @Nonnull
  private static Map<String, String> properties(DeviceType deviceType) {
    final DeviceTypeProperties<?> properties = deviceType.getProperties();
    if (properties == null) {
      return Collections.emptyMap();
    }
    Map<String, String> map = new HashMap<>(7);
    ALL_PROPERTIES.forEach(
        property -> {
          final String value = properties.getString(property);
          if (value != null) {
            map.put(property, value);
          }
        });

    return map;
  }
}
