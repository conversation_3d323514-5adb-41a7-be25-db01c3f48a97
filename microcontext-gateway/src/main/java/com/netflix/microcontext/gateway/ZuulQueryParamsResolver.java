package com.netflix.microcontext.gateway;

import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.ImmutableListMultimap.Builder;
import com.google.common.collect.ListMultimap;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.zuul.message.http.HttpQueryParams;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nonnull;

public class ZuulQueryParamsResolver implements ParamResolver {

  private final ListMultimap<String, String> params;

  public ZuulQueryParamsResolver(HttpQueryParams queryParams) {
    this.params = paramsListLowerCase(queryParams);
  }

  @Override
  public Optional<String> get(@Nonnull String name) {
    final List<String> all = params.get(key(name));
    return all == null || all.isEmpty() ? Optional.empty() : Optional.of(all.get(0));
  }

  @Nonnull
  @Override
  public List<String> getAll(@Nonnull String name) {
    final List<String> list = params.get(key(name));
    return list == null ? Collections.emptyList() : list;
  }

  @Override
  public boolean contains(@Nonnull String name) {
    return params.containsKey(key(name));
  }

  private static String key(String name) {
    return name.toLowerCase();
  }

  /** Zuuls params implementation is case-sensitive, create a multimap with all lower case values */
  private static ListMultimap<String, String> paramsListLowerCase(HttpQueryParams params) {
    final Builder<String, String> builder = ImmutableListMultimap.builder();
    for (Map.Entry<String, String> param : params.entries()) {
      builder.put(key(param.getKey()), param.getValue());
    }
    return builder.build();
  }
}
