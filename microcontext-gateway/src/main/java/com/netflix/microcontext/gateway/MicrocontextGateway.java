package com.netflix.microcontext.gateway;

import com.github.ua_parser.Client;
import com.netflix.microcontext.MicrocontextInitializer;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.requestcontext.ContextContextSerializer;
import com.netflix.microcontext.init.resolvers.VisitResolvers;
import com.netflix.microcontext.init.utils.ComparableVersion;
import com.netflix.microcontext.resolver.user.UserConverter;
import com.netflix.passport.introspect.PassportIdentity;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.Context;
import netflix.context.visit.DeviceDetails;
import netflix.context.visit.OSDetails;
import netflix.context.visit.SemVer;
import netflix.context.visit.UserAgent;
import netflix.context.visit.UserAgentDetails;
import netflix.context.visit.WebClientDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MicrocontextGateway {

  private static final Logger logger = LoggerFactory.getLogger(MicrocontextGateway.class);
  private static final String CONTEXT_KEY = "microcontext";

  public static Optional<Context> initContext(MicrocontextGatewayInitParams params) {
    try {
      Metrics.trackParameters(params);
      final ZuulHeaderResolver zuulHeaderResolver = new ZuulHeaderResolver(params.getRequest());
      final ZuulQueryParamsResolver zuulQueryParamResolver =
          new ZuulQueryParamsResolver(params.getRequest().getQueryParams());
      final ZuulCookieResolver zuulCookieResolver =
          new ZuulCookieResolver(params.getRequest().parseCookies());
      PassportIdentity passportIdentity = params.getPassportIdentity();
      final UserAgent userAgent =
          params.getUserAgent() != null
              ? params.getUserAgent()
              : parseUserAgent(zuulHeaderResolver, params.getClient());
      Context context =
          MicrocontextInitializer.init(
              zuulHeaderResolver,
              zuulQueryParamResolver,
              zuulCookieResolver,
              passportIdentity,
              params.getGeoAttributes(),
              params.getSignupCountry(),
              params.getRequestContext(),
              GatewayDeviceResolvers.resolve(
                  params.getDtsClient(),
                  passportIdentity,
                  params.getDeviceType(),
                  zuulHeaderResolver,
                  zuulQueryParamResolver),
              VisitResolvers.resolve(
                  zuulHeaderResolver,
                  params.getRequestContext(),
                  params.getRequestPriority(),
                  userAgent),
              params.getAccountProfileRemote() != null
                  ? UserConverter.userContext(params.getAccountProfileRemote())
                  : null,
              params.getHostGroup(),
              params.isSetUser(),
              params.isDisableWebTier());
      Metrics.trackContext(context, params.getRegistry());
      params
          .getRequestContext()
          .addContext(CONTEXT_KEY, context, ContextContextSerializer.INSTANCE);
      return Optional.of(context);
    } catch (Throwable t) {
      logger.info("Could not set context", t);
      return Optional.empty();
    }
  }

  private static UserAgent parseUserAgent(
      @Nonnull HeaderResolver headerResolver, @Nullable Client client) {
    String header = headerResolver.get(VisitResolvers.USER_AGENT).orElse(null);
    if (header == null) {
      return null;
    }
    final UserAgent.Builder builder = UserAgent.newBuilder().setValue(header);
    final WebClientDetails.Builder webClient;
    try {
      if (client != null) {
        webClient = WebClientDetails.newBuilder();
        if (client.userAgent != null) {
          final com.github.ua_parser.UserAgent userAgent = client.userAgent;
          webClient.setUserAgentDetails(
              UserAgentDetails.newBuilder()
                  .setFamily(userAgent.family)
                  .setSemVer(semver(userAgent.major, userAgent.minor, userAgent.patch))
                  .build());
        }
        if (client.os != null) {
          webClient.setOsDetails(
              OSDetails.newBuilder()
                  .setFamily(client.os.family)
                  .setSemVer(semver(client.os.major, client.os.minor, client.os.patch))
                  .build());
        }
        if (client.device != null) {
          webClient.setDeviceDetails(DeviceDetails.newBuilder().setFamily(client.device.family));
        }

        builder.setClientDetails(webClient.build());
      }
    } catch (Throwable t) {
      logger.info("Could not parse user agent", t);
    }
    return builder.build();
  }

  private static SemVer semver(String major, String minor, String patch) {
    final ComparableVersion comparableVersion = ComparableVersion.of(major, minor, patch);
    return SemVer.newBuilder()
        .setMajor(comparableVersion.getMajor())
        .setMinor(comparableVersion.getMinor())
        .setPatch(comparableVersion.getPatch())
        .build();
  }

  public static boolean init(MicrocontextGatewayInitParams params) {
    return initContext(params).isPresent();
  }
}
