package com.netflix.microcontext.gateway;

import com.github.ua_parser.Client;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Registry;
import com.netflix.spectator.api.Spectator;
import com.netflix.streaming.dts.client.DtsClient;
import com.netflix.streaming.dts.common.model.DeviceType;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.type.ISOCountry;
import com.netflix.zuul.message.http.HttpRequestMessage;
import java.util.Map;
import java.util.Objects;
import netflix.context.visit.RequestPriority;
import netflix.context.visit.UserAgent;

public class MicrocontextGatewayInitParams {
  private final HttpRequestMessage request;
  private final PassportIdentity passportIdentity;
  private final DtsClient dtsClient;
  private final Map<String, String> geoAttributes;
  private final ISOCountry signupCountry;
  private final UserAgent userAgent;
  private final Client client;
  private final RequestContext requestContext;
  private final DeviceType deviceType;
  private final AccountProfileRemote accountProfileRemote;
  private final RequestPriority requestPriority;
  private final Registry registry;
  private final String hostGroup;
  private final boolean setUser;
  private final boolean disableWebTier;

  private MicrocontextGatewayInitParams(
      HttpRequestMessage request,
      PassportIdentity passportIdentity,
      DtsClient dtsClient,
      Map<String, String> geoAttributes,
      ISOCountry signupCountry,
      UserAgent userAgent,
      Client client,
      RequestContext requestContext,
      DeviceType deviceType,
      AccountProfileRemote accountProfileRemote,
      RequestPriority requestPriority,
      Registry registry,
      String hostGroup,
      boolean setUser,
      boolean disableWebTier) {
    this.request = Objects.requireNonNull(request, "Must provide request");
    this.passportIdentity = passportIdentity;
    this.geoAttributes = geoAttributes;
    this.signupCountry = signupCountry;
    this.requestContext = requestContext == null ? CurrentRequestContext.get() : requestContext;
    this.deviceType = deviceType;
    this.accountProfileRemote = accountProfileRemote;
    this.requestPriority = requestPriority;
    this.registry = registry == null ? Spectator.globalRegistry() : registry;
    this.userAgent = userAgent;
    this.client = client;
    this.hostGroup = hostGroup;
    this.setUser = setUser;
    this.dtsClient = dtsClient;
    this.disableWebTier = disableWebTier;
  }

  public HttpRequestMessage getRequest() {
    return request;
  }

  public Map<String, String> getGeoAttributes() {
    return geoAttributes;
  }

  public DtsClient getDtsClient() {
    return dtsClient;
  }

  public ISOCountry getSignupCountry() {
    return signupCountry;
  }

  public UserAgent getUserAgent() {
    return userAgent;
  }

  public Client getClient() {
    return client;
  }

  public RequestContext getRequestContext() {
    return requestContext;
  }

  public DeviceType getDeviceType() {
    return deviceType;
  }

  public AccountProfileRemote getAccountProfileRemote() {
    return accountProfileRemote;
  }

  public RequestPriority getRequestPriority() {
    return requestPriority;
  }

  public Registry getRegistry() {
    return registry;
  }

  public String getHostGroup() {
    return hostGroup;
  }

  public static Builder builder() {
    return new Builder();
  }

  public PassportIdentity getPassportIdentity() {
    return passportIdentity;
  }

  public boolean isSetUser() {
    return setUser;
  }

  public boolean isDisableWebTier() {
    return disableWebTier;
  }

  public static class Builder {

    private HttpRequestMessage request;
    private PassportIdentity passportIdentity;
    private DtsClient dtsClient;
    private Map<String, String> geoAttributes;
    private ISOCountry signupCountry;
    private RequestContext requestContext;
    private DeviceType deviceType;
    private AccountProfileRemote accountProfileRemote;
    private RequestPriority requestPriority;
    private Registry registry;
    @Deprecated private UserAgent userAgent;
    private Client client;
    private String hostGroup;
    private boolean setUser;
    private boolean disableWebTier;

    Builder() {}

    public Builder request(HttpRequestMessage request) {
      this.request = request;
      return this;
    }

    public Builder passportIdentity(PassportIdentity passportIdentity) {
      this.passportIdentity = passportIdentity;
      return this;
    }

    public Builder geoAttributes(Map<String, String> geoAttributes) {
      this.geoAttributes = geoAttributes;
      return this;
    }

    public Builder dtsClient(DtsClient dtsClient) {
      this.dtsClient = dtsClient;
      return this;
    }

    public Builder signupCountry(ISOCountry signupCountry) {
      this.signupCountry = signupCountry;
      return this;
    }

    public Builder requestContext(RequestContext requestContext) {
      this.requestContext = requestContext;
      return this;
    }

    public Builder deviceType(DeviceType deviceType) {
      this.deviceType = deviceType;
      return this;
    }

    public Builder accountProfileRemote(AccountProfileRemote accountProfileRemote) {
      this.accountProfileRemote = accountProfileRemote;
      return this;
    }

    public Builder requestPriority(RequestPriority requestPriority) {
      this.requestPriority = requestPriority;
      return this;
    }

    public Builder registry(Registry registry) {
      this.registry = registry;
      return this;
    }

    /**
     * @deprecated Use {@link #client(Client)} instead.
     */
    @Deprecated
    public Builder userAgent(UserAgent userAgent) {
      this.userAgent = userAgent;
      return this;
    }

    public Builder client(Client client) {
      this.client = client;
      return this;
    }

    public Builder hostGroup(String hostGroup) {
      this.hostGroup = hostGroup;
      return this;
    }

    public Builder setUser(boolean setUser) {
      this.setUser = setUser;
      return this;
    }

    public Builder disableWebTier() {
      this.disableWebTier = true;
      return this;
    }

    public MicrocontextGatewayInitParams build() {
      return new MicrocontextGatewayInitParams(
          this.request,
          this.passportIdentity,
          this.dtsClient,
          this.geoAttributes,
          this.signupCountry,
          this.userAgent,
          client,
          this.requestContext,
          this.deviceType,
          this.accountProfileRemote,
          this.requestPriority,
          this.registry,
          this.hostGroup,
          this.setUser,
          disableWebTier);
    }

    public String toString() {
      return "MicrocontextGatewayInitParams.Builder(request="
          + this.request
          + ", passportIdentity="
          + this.passportIdentity
          + ", dtsClient="
          + this.dtsClient
          + ", geoAttributes="
          + this.geoAttributes
          + ", signupCountry="
          + this.signupCountry
          + ", requestContext="
          + this.requestContext
          + ", deviceType="
          + this.deviceType
          + ", accountProfileRemote="
          + this.accountProfileRemote
          + ", requestPriority="
          + this.requestPriority
          + ", registry="
          + this.registry
          + ")";
    }
  }
}
