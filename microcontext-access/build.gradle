apply plugin: 'netflix.jvm-library'
apply plugin: 'netflix.spring-boot-netflix-library'

sourceSets {
    jmh {
        compileClasspath += sourceSets.main.compileClasspath + sourceSets.test.compileClasspath + sourceSets.main.runtimeClasspath
        runtimeClasspath += sourceSets.main.runtimeClasspath + sourceSets.test.runtimeClasspath
    }
}

dependencies {
    api project(':microcontext-model')
    api project(':microcontext-init')
    api 'netflix:server-context'
    api 'netflix.grpc:netflix-grpc-common'
    api 'commons-codec:commons-codec:1.15' 
    api 'com.netflix.archaius:archaius2-api'
    
    implementation "netflix:basicTypes-proto-bridge:latest.release"
    implementation('commons-io:commons-io')
    implementation("org.apache.maven:maven-artifact:3.+")
    compileOnly 'com.google.inject:guice'
    compileOnly 'org.springframework.boot:spring-boot'
    compileOnly 'org.springframework.boot:spring-boot-autoconfigure'
    compileOnly "com.netflix.spring:spring-boot-netflix-starter-library"
    testImplementation "com.netflix.spring:spring-boot-netflix-starter-test"
    testImplementation("junit:junit")
    testImplementation("org.mockito:mockito-core")
    testImplementation("org.springframework:spring-test")
    testImplementation 'com.netflix.archaius:archaius2-core'
    testImplementation project(":microcontext-test")
    testRuntimeOnly 'org.junit.vintage:junit-vintage-engine'
    testImplementation 'netflix:geoip-common:latest.release'

    jmhImplementation project
    jmhImplementation 'org.openjdk.jmh:jmh-core:1.37'
    jmhImplementation 'org.openjdk.jmh:jmh-generator-annprocess:1.37'
    jmhAnnotationProcessor 'org.openjdk.jmh:jmh-generator-annprocess:1.37'
}
tasks.withType(Test).configureEach { 
    useJUnitPlatform()
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(8)
    }
}

task jmh(type: JavaExec, description: 'Executing JMH benchmarks') {
    classpath = sourceSets.jmh.runtimeClasspath + sourceSets.jmh.compileClasspath
    mainClass = 'org.openjdk.jmh.Main'
}

dependencyRecommendations {
    mavenBom module: 'com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations:latest.release'
}
