package com.netflix.microcontext.access.server;

import static org.junit.Assert.*;

import com.google.protobuf.FieldMask;
import com.google.protobuf.util.FieldMaskUtil;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.init.serializers.Serializers;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.junit.BindingContextRule;
import com.netflix.type.proto.Countries;
import netflix.context.Context;
import netflix.context.user.User;
import netflix.context.user.UserContext;
import org.junit.Rule;
import org.junit.Test;

public class CurrentMicrocontextTest {

  @Rule public BindingContextRule bindingContextRule = new BindingContextRule();

  @Test
  public void set() {
    CurrentRequestContext.get().setAppName("foo");
    final Context context = Context.newBuilder().setRequestId("123").build();
    CurrentMicrocontext.set(context);
  }

  @Test
  public void internalSet() {
    CurrentRequestContext.get().setAppName("foo");
    final Context context = Context.newBuilder().setRequestId("123").build();
    MicrocontextInternalUtils.internalSet(context);
  }

  @Test
  public void setSerialized() {
    CurrentRequestContext.get().setAppName("foo");
    final Context context = Context.newBuilder().setRequestId("123").build();
    CurrentMicrocontext.set(Serializers.toString(context));
  }

  @Test
  public void setTwice() {
    CurrentRequestContext.get().setAppName("foo");
    final Context context = Context.newBuilder().setRequestId("123").build();
    CurrentMicrocontext.set(context);
    CurrentRequestContext.get().setAppName("bar");
    CurrentMicrocontext.set(CurrentMicrocontext.get().toProto());
  }

  @Test
  public void setSame() {
    CurrentRequestContext.get().setAppName("foo");
    final Context context = Context.newBuilder().setRequestId("123").build();
    CurrentMicrocontext.set(context);
    CurrentMicrocontext.set(CurrentMicrocontext.get().toProto());
  }

  @Test
  public void merge() {
    final Context context = Context.newBuilder().setRequestId("123").build();
    CurrentMicrocontext.set(context);
    final Context updated =
        Context.newBuilder().setRequestId("345").setCountry(Countries.toProtobuf("US")).build();
    boolean success =
        CurrentMicrocontext.mergeAndSet(updated, FieldMaskUtil.fromFieldNumbers(Context.class, 1));
    Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("345", microcontext.getRequestId());
    assertEquals("", microcontext.getCountry().getId());
    assertTrue(success);
    final FieldMask fieldMask = FieldMaskUtil.fromString("request_id,country");
    success = CurrentMicrocontext.mergeAndSet(updated, fieldMask);
    microcontext = CurrentMicrocontext.get();
    assertEquals("345", microcontext.getRequestId());
    assertEquals("US", microcontext.getCountry().getId());
    assertTrue(success);
  }

  @Test
  public void clear() {
    final Context context =
        Context.newBuilder()
            .setRequestId("123")
            .setUser(
                UserContext.newBuilder()
                    .setCurrentUser(User.newBuilder().setId(1234L).setOwnerId(4567L)))
            .build();
    CurrentMicrocontext.set(context);
    Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
    final Context proto = microcontext.toProto();
    assertTrue(proto.hasUser()); // temporarily changing this to true until we can re-enable
    final User user = microcontext.getUser().getCurrentUser();
    assertEquals(1234L, user.getId());
    assertEquals(4567L, user.getOwnerId().getValue());
  }

  @Test
  public void testCompareAndSetSuccess() {
    final Context initialContext = Context.newBuilder().setRequestId("123").build();
    CurrentMicrocontext.set(initialContext);

    final Context newContext = Context.newBuilder().setRequestId("456").build();

    boolean result =
        CurrentMicrocontext.compareAndSet(
            CurrentMicrocontext.get(), new MicrocontextImpl(newContext));

    assertTrue(result);
    Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("456", microcontext.getRequestId());
  }

  @Test
  public void testCompareAndSetFailure() {
    final Context initialContext = Context.newBuilder().setRequestId("123").build();
    CurrentMicrocontext.set(initialContext);

    final Context newContext = Context.newBuilder().setRequestId("456").build();

    boolean result =
        CurrentMicrocontext.compareAndSet(
            new MicrocontextImpl(Context.getDefaultInstance()), new MicrocontextImpl(newContext));

    assertFalse(result);
    Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
  }

  @Test
  public void testClear() {
    final Context context =
        Context.newBuilder()
            .setRequestId("123")
            .setUser(
                UserContext.newBuilder()
                    .setCurrentUser(User.newBuilder().setId(1234L).setOwnerId(4567L)))
            .build();
    CurrentMicrocontext.set(context);

    Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
    assertTrue(microcontext.toProto().hasUser());

    CurrentMicrocontext.clear();

    microcontext = CurrentMicrocontext.get();
    assertEquals(Context.getDefaultInstance(), microcontext.toProto());
  }
}
