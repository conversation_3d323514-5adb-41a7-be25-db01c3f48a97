package com.netflix.microcontext.access.server.metrics;

import com.netflix.spectator.api.DefaultRegistry;
import netflix.context.Context;
import org.junit.Assert;
import org.junit.Test;

public class MicrocontextInitMetricsTest {

  @Test
  public void emitNull() {
    final DefaultRegistry registry = new DefaultRegistry();
    final MicrocontextInitMetrics microcontextInitMetrics = new MicrocontextInitMetrics(registry);
    microcontextInitMetrics.emit(null);
    Assert.assertEquals(
        1, registry.counter("microcontext.error", "missing_contexts", "all").count());
  }

  @Test
  public void emitDefault() {
    final DefaultRegistry registry = new DefaultRegistry();
    final MicrocontextInitMetrics microcontextInitMetrics = new MicrocontextInitMetrics(registry);
    microcontextInitMetrics.emit(Context.getDefaultInstance());
    Assert.assertEquals(
        1, registry.counter("microcontext.error", "missing_contexts", "all").count());
  }
}
