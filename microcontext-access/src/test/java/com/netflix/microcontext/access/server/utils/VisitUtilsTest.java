package com.netflix.microcontext.access.server.utils;

import static org.junit.Assert.*;

import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.access.server.ContextUtils;
import com.netflix.microcontext.test.utils.TestCurrentMicrocontext;
import com.netflix.microcontext.test.utils.TestMicrocontext;
import java.util.Optional;
import netflix.context.Context;
import netflix.context.geo.GeoContext;
import netflix.context.visit.VisitContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class VisitUtilsTest {

  @Before
  public void setUp() throws Exception {
    BindingContexts.push();
  }

  @After
  public void tearDown() throws Exception {
    BindingContexts.pop();
  }

  @Test
  public void getIanaTimezoneDefault() {
    final Optional<String> empty = VisitUtils.getIanaTimezone(ContextUtils.fromDefault());
    assertFalse(empty.isPresent());
  }

  @Test
  public void getIanaTimezoneGeo() {
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder().setGeo(GeoContext.newBuilder().setIanaTimezone("bar").build()));
    final Optional<String> geo = VisitUtils.getIanaTimezone();
    assertTrue(geo.isPresent());
    assertEquals("bar", geo.get());
  }

  @Test
  public void getIanaTimezoneOverride() {
    final Context context =
        Context.newBuilder()
            .setVisit(VisitContext.newBuilder().setOverrideIanaTimezone("foo"))
            .build();
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder(context)
            .setGeo(GeoContext.newBuilder().setIanaTimezone("bar").build()));
    final Optional<String> override = VisitUtils.getIanaTimezone();
    assertTrue(override.isPresent());
    assertEquals("foo", override.get());
  }
}
