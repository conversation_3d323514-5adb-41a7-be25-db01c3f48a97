package com.netflix.microcontext.access.server.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

import com.netflix.spectator.api.DefaultRegistry;
import com.netflix.spectator.api.Meter;
import java.util.HashSet;
import java.util.Set;
import netflix.context.Context;
import netflix.context.visit.RequestPriority;
import netflix.context.visit.VisitContext;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 12/6/23
 */
public class RequestPriorityMetricsTest {

  private DefaultRegistry registry;
  private RequestPriorityMetrics metrics;

  @Before
  public void setup() {
    registry = new DefaultRegistry();
    metrics = new RequestPriorityMetrics(registry);
  }

  @Test
  public void testEmitPriorityMetricsWhenContextNull() {
    metrics.emitPriorityMetrics((Context) null);
    assertNoMetricsRecorded();
  }

  @Test
  public void testEmitPriorityMetricsWhenContextHasNoVisit() {
    Context context = Context.newBuilder().build();
    metrics.emitPriorityMetrics(context);
    assertNoMetricsRecorded();
  }

  @Test
  public void testEmitPriorityMetricsWhenVisitContextNull() {
    metrics.emitPriorityMetrics((VisitContext) null);
    assertNoMetricsRecorded();
  }

  @Test
  public void testEmitPriorityMetricsWhenVisitContextHasNoPriority() {
    VisitContext visitContext = VisitContext.newBuilder().build();
    metrics.emitPriorityMetrics(visitContext);
    assertNoMetricsRecorded();
  }

  @Test
  public void testEmitPriorityMetricsValidContext() {
    RequestPriority priority = RequestPriority.newBuilder().setPriority(50).build();
    VisitContext visitContext = VisitContext.newBuilder().setPriority(priority).build();
    metrics.emitPriorityMetrics(visitContext);

    Meter meter = registry.iterator().next();
    assertEquals(1d, meter.measure().iterator().next().value(), .001);
  }

  @Test
  public void bucketed() {
    assertEquals("missing", RequestPriorityMetrics.getPriorityMetric(0));
    assertEquals("missing", RequestPriorityMetrics.getPriorityMetric(-1));
    assertEquals("unknown", RequestPriorityMetrics.getPriorityMetric(100));
    assertEquals("unknown", RequestPriorityMetrics.getPriorityMetric(200));

    Set<String> buckets = new HashSet<>();
    for (int i = 1; i < 100; ++i) {
      buckets.add(RequestPriorityMetrics.getPriorityMetric(i));
    }

    assertEquals(10, buckets.size());
  }

  private void assertNoMetricsRecorded() {
    assertFalse(registry.iterator().hasNext());
  }
}
