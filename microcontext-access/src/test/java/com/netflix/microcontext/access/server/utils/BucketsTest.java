package com.netflix.microcontext.access.server.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.netflix.lang.BindingContexts;
import java.util.UUID;
import org.junit.Test;

public class BucketsTest {

  @Test
  public void testGetESNBucket() {
    for (int i = 0; i < 1000; i++) {
      int esnBucket = Buckets.getESNBucket(UUID.randomUUID().toString(), 10);
      assertTrue(esnBucket > 0 && esnBucket <= 10);
    }
  }

  @Test
  public void testGetESNBucketNoESN() {
    BindingContexts.push();
    try {
      assertEquals(0, Buckets.getESNBucket(100));
    } finally {
      BindingContexts.pop();
    }
  }
}
