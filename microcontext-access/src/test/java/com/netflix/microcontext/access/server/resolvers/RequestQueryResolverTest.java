package com.netflix.microcontext.access.server.resolvers;

import static org.junit.Assert.*;

import com.google.common.collect.Lists;
import com.netflix.microcontext.init.params.ParamNames;
import com.netflix.microcontext.init.params.ParamResolver;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Test;

public class RequestQueryResolverTest {

  private static final Map<String, String[]> params = new HashMap<>();

  static {
    params.put("appVersion", new String[] {"5"});
    params.put("osVersion", new String[] {"1.0"});
    params.put("api", new String[] {"2.0"});
    params.put("foo", new String[] {"bar"});
  }

  ParamResolver paramResolver = RequestQueryResolver.of(params);

  @Test
  public void get() {
    final Optional<String> appVersion = paramResolver.get("appVersion");
    assertTrue(appVersion.isPresent());
    assertEquals("5", appVersion.get());
  }

  @Test
  public void getMissing() {
    final Optional<String> appVersion = paramResolver.get("random");
    assertFalse(appVersion.isPresent());
  }

  @Test
  public void getCase() {
    final Optional<String> appVersion = paramResolver.get(ParamNames.APP_VERSION);
    assertTrue(appVersion.isPresent());
    assertEquals("5", appVersion.get());
  }

  @Test
  public void getFirst() {
    final Optional<String> osVersion =
        paramResolver.getFirst(Lists.newArrayList("osVersion", "api"));
    assertTrue(osVersion.isPresent());
    assertEquals("1.0", osVersion.get());
  }

  @Test
  public void getFirstMissing() {
    final Optional<String> random = paramResolver.getFirst(Collections.singletonList("random"));
    assertFalse(random.isPresent());
  }

  @Test
  public void getFirstCase() {
    final Optional<String> osVersion = paramResolver.getFirst(ParamNames.ALL_OS_VERSIONS);
    assertTrue(osVersion.isPresent());
    assertEquals("1.0", osVersion.get());
  }

  @Test
  public void getAll() {
    final List<String> appVersions = paramResolver.getAll("appVersion");
    assertFalse(appVersions.isEmpty());
    assertEquals("5", appVersions.get(0));
  }

  @Test
  public void getAllMissing() {
    final List<String> appVersions = paramResolver.getAll("random");
    assertTrue(appVersions.isEmpty());
  }

  @Test
  public void getAllCase() {
    final List<String> appVersions = paramResolver.getAll(ParamNames.APP_VERSION);
    assertFalse(appVersions.isEmpty());
    assertEquals("5", appVersions.get(0));
  }

  @Test(expected = UnsupportedOperationException.class)
  public void getAllMutation() {
    final List<String> appVersions = paramResolver.getAll("appVersion");
    assertFalse(appVersions.isEmpty());
    assertEquals("5", appVersions.get(0));
    appVersions.add("foo");
  }

  @Test
  public void contains() {
    assertTrue(paramResolver.contains("appVersion"));
  }

  @Test
  public void containsCase() {
    assertTrue(paramResolver.contains(ParamNames.APP_VERSION));
  }

  @Test
  public void containsMissing() {
    assertFalse(paramResolver.contains("random"));
  }
}
