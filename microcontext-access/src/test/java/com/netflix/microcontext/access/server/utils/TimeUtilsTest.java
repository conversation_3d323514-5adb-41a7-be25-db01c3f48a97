package com.netflix.microcontext.access.server.utils;

import static org.junit.Assert.*;

import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Durations;
import com.google.protobuf.util.Timestamps;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.ContextUtils;
import java.util.Optional;
import netflix.context.Context;
import netflix.context.visit.VisitContext;
import org.junit.Test;

public class TimeUtilsTest {

  @Test
  public void currentRequestTime() {
    final Timestamp now = Timestamps.now();
    final Timestamp then = Timestamps.add(now, Durations.fromSeconds(5));
    final Microcontext microcontext =
        ContextUtils.fromProto(
            Context.newBuilder()
                .setVisit(VisitContext.newBuilder().setEdgeTime(now).setRequestTime(then))
                .build());
    final Optional<Timestamp> currentRequestTimeMillis =
        RequestTimeUtils.currentRequestTime(microcontext);
    assertTrue(currentRequestTimeMillis.isPresent());
    assertEquals(then, currentRequestTimeMillis.get());
  }

  @Test
  public void currentRequestTimeEdge() {
    final Timestamp now = Timestamps.now();
    final Microcontext microcontext =
        ContextUtils.fromProto(
            Context.newBuilder().setVisit(VisitContext.newBuilder().setEdgeTime(now)).build());
    final Optional<Timestamp> currentRequestTimeMillis =
        RequestTimeUtils.currentRequestTime(microcontext);
    assertTrue(currentRequestTimeMillis.isPresent());
    assertEquals(now, currentRequestTimeMillis.get());
  }

  @Test
  public void currentRequestTimeEmpty() {
    final Timestamp now = Timestamps.now();
    final Microcontext microcontext = ContextUtils.fromProto(Context.getDefaultInstance());
    final Optional<Timestamp> currentRequestTimeMillis =
        RequestTimeUtils.currentRequestTime(microcontext);
    assertFalse(currentRequestTimeMillis.isPresent());
  }

  @Test
  public void currentRequestTimeMillis() {
    final Timestamp now = Timestamps.now();
    final Timestamp then = Timestamps.add(now, Durations.fromSeconds(5));
    final Microcontext microcontext =
        ContextUtils.fromProto(
            Context.newBuilder()
                .setVisit(VisitContext.newBuilder().setEdgeTime(now).setRequestTime(then))
                .build());
    final Optional<Long> currentRequestTimeMillis =
        RequestTimeUtils.currentRequestTimeMillis(microcontext);
    assertTrue(currentRequestTimeMillis.isPresent());
    assertEquals(Timestamps.toMillis(then), currentRequestTimeMillis.get().longValue());
  }
}
