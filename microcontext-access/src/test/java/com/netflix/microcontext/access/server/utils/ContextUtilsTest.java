package com.netflix.microcontext.access.server.utils;

import static org.junit.jupiter.api.Assertions.*;

import com.google.protobuf.FieldMask;
import com.google.protobuf.util.FieldMaskUtil;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.ContextUtils;
import com.netflix.microcontext.access.server.MicrocontextImpl;
import com.netflix.type.protogen.BasicTypes;
import java.util.Collections;
import netflix.context.Context;
import netflix.context.Context.Builder;
import netflix.context.client.ClientContext;
import netflix.context.device.DeviceContext;
import netflix.context.video.VideoContext;
import netflix.context.visit.VisitContext;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class ContextUtilsTest {

  @Test
  public void testToBuilderWithNonMicrocontextImpl() {
    Microcontext microcontext = Mockito.mock(Microcontext.class);
    Mockito.when(microcontext.getRequestId()).thenReturn("test-request-id");
    Mockito.when(microcontext.toProto()).thenReturn(Context.getDefaultInstance());
    Mockito.when(microcontext.getCountry()).thenReturn(BasicTypes.Country.getDefaultInstance());
    Mockito.when(microcontext.getLocales()).thenReturn(Collections.emptyList());
    Mockito.when(microcontext.getDevice()).thenReturn(DeviceContext.getDefaultInstance());
    Mockito.when(microcontext.getClient()).thenReturn(ClientContext.getDefaultInstance());
    Mockito.when(microcontext.getVisit()).thenReturn(VisitContext.getDefaultInstance());
    Mockito.when(microcontext.getVideo()).thenReturn(VideoContext.getDefaultInstance());

    Builder builder = ContextUtils.toBuilder(microcontext);

    assertEquals("test-request-id", builder.getRequestId().getValue());
  }

  @Test
  public void testFromInstanceWithNullContext() {
    Microcontext microcontext = ContextUtils.fromInstance(null);

    assertNotNull(microcontext);
    assertInstanceOf(MicrocontextImpl.class, microcontext);
  }

  @Test
  public void testFromInstanceWithNonNullContext() {
    Context context = Context.newBuilder().setRequestId("test-request-id").build();
    Microcontext microcontext = ContextUtils.fromInstance(context);

    assertNotNull(microcontext);
    assertEquals("test-request-id", microcontext.getRequestId());
  }

  @Test
  public void testMerge() {
    Context updated = Context.newBuilder().setRequestId("updated-request-id").build();
    Context destination = Context.newBuilder().setRequestId("destination-request-id").build();
    FieldMask fieldMask = FieldMaskUtil.fromString("request_id");

    Builder mergedBuilder = ContextUtils.merge(fieldMask, updated, destination);
    Context mergedContext = mergedBuilder.build();

    assertEquals("updated-request-id", mergedContext.getRequestId().getValue());
  }

  @Test
  public void testIsDefault() {
    Context defaultContext = Context.getDefaultInstance();
    assertTrue(ContextUtils.isDefault(defaultContext));
  }

  @Test
  public void testIsNotDefault() {
    Context nonDefaultContext = Context.newBuilder().setRequestId("non-default").build();
    assertTrue(ContextUtils.isNotDefault(nonDefaultContext));
  }
}
