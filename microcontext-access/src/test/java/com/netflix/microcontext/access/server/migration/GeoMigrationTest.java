package com.netflix.microcontext.access.server.migration;

import static org.junit.Assert.*;

import com.netflix.geoclient.GeoDataImpl;
import com.netflix.geoclient.context.GeoRequestContextManager;
import com.netflix.lang.BindingContexts;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import java.util.Collections;
import java.util.Optional;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class GeoMigrationTest {

  @Before
  public void setUp() {
    BindingContexts.push();
  }

  @After
  public void tearDown() {
    BindingContexts.pop();
  }

  @Test
  public void getBlockedProxyDefault() {
    assertFalse(GeoMigration.getBlockedProxy());
  }

  @Test
  public void getBlockedProxyGeo() {
    final GeoRequestContextManager instance = GeoRequestContextManager.instance();
    GeoDataImpl geoData = new GeoDataImpl(Collections.singletonMap("blocked_proxy", "1"));
    instance.setIntoGeoRequestContext(geoData);
    assertTrue(GeoMigration.getBlockedProxy());
  }

  @Test
  public void getBlockedProxyRequestContextDefault() {
    GeoMigration.setBlockedProxyRequestContext(true);
    assertFalse(GeoMigration.getBlockedProxy());
  }

  @Test
  public void getBlockedProxyRequestContext() {
    GeoMigration.setBlockedProxyRequestContext(true);
    assertTrue(GeoMigration.getBlockedProxyAll());
  }

  @Test
  public void getBlockedProxyVideo() {
    assertTrue(GeoMigration.setBlockedProxy(true));
    assertTrue(GeoMigration.getBlockedProxy());
  }

  @Test
  public void getBlockedProxyOverrideVideo() {
    final GeoRequestContextManager instance = GeoRequestContextManager.instance();
    GeoDataImpl geoData = new GeoDataImpl(Collections.singletonMap("blocked_proxy", "1"));
    instance.setIntoGeoRequestContext(geoData);
    assertTrue(GeoMigration.getBlockedProxy());
    GeoMigration.setBlockedProxyRequestContext(true);
    assertTrue(GeoMigration.setBlockedProxy(false));
    assertFalse(GeoMigration.getBlockedProxy());

    // Test remove
    assertTrue(GeoMigration.clearBlockedProxy());
    assertTrue(GeoMigration.getBlockedProxy());
  }

  @Test
  public void getBlockedProxyOverrideRequestContextDefault() {
    final GeoRequestContextManager instance = GeoRequestContextManager.instance();
    GeoDataImpl geoData = new GeoDataImpl(Collections.singletonMap("blocked_proxy", "1"));
    instance.setIntoGeoRequestContext(geoData);
    CurrentRequestContext.get().addContext(GeoMigration.REQUEST_CONTEXT_KEY, "false");
    assertTrue(GeoMigration.getBlockedProxy());
  }

  @Test
  public void getBlockedProxyOverrideRequestContext() {
    final GeoRequestContextManager instance = GeoRequestContextManager.instance();
    GeoDataImpl geoData = new GeoDataImpl(Collections.singletonMap("blocked_proxy", "1"));
    instance.setIntoGeoRequestContext(geoData);
    assertTrue(GeoMigration.getBlockedProxyAll());
    GeoMigration.setBlockedProxyRequestContext(false);
    assertFalse(GeoMigration.getBlockedProxyAll());

    // Test remove
    assertTrue(GeoMigration.clearBlockedProxyAll());
    assertTrue(GeoMigration.getBlockedProxy());
  }

  @Test
  public void getBlockedProxyAll() {
    assertFalse(GeoMigration.getBlockedProxyAll());

    assertTrue(GeoMigration.setBlockedProxyAll(true));
    assertTrue(GeoMigration.getBlockedProxyAll());

    // Test remove
    assertTrue(GeoMigration.clearBlockedProxyAll());
    assertFalse(GeoMigration.getBlockedProxy());
  }

  @Test
  public void getBlockedProxyAllRequestContext() {
    RequestContext requestContext = new RequestContext("");
    assertFalse(GeoMigration.getBlockedProxyAll(requestContext));

    assertTrue(GeoMigration.setBlockedProxyAll(true, requestContext));
    assertTrue(GeoMigration.getBlockedProxyAll(requestContext));

    // Test remove
    assertTrue(GeoMigration.clearBlockedProxyAll(requestContext));
    assertFalse(GeoMigration.getBlockedProxy());
  }

  @Test
  public void getBlockedProxyOverride() {
    final Optional<Boolean> blockedProxyOverride =
        GeoMigration.getBlockedProxyOverride(CurrentRequestContext.get());
    assertFalse(blockedProxyOverride.isPresent());
  }

  @Test
  public void getBlockedProxyOverrideMicro() {
    GeoMigration.setBlockedProxy(false);
    final Optional<Boolean> blockedProxyOverride =
        GeoMigration.getBlockedProxyOverride(CurrentRequestContext.get());
    assertTrue(blockedProxyOverride.isPresent());
    assertFalse(blockedProxyOverride.get());
  }

  @Test
  public void getBlockedProxyOverrideMicroParam() {
    GeoMigration.setBlockedProxy(false);
    final Optional<Boolean> blockedProxyOverride =
        GeoMigration.getBlockedProxyOverride(CurrentRequestContext.get());
    assertTrue(blockedProxyOverride.isPresent());
    assertFalse(blockedProxyOverride.get());
  }

  @Test
  public void getBlockedProxyOverrideRequest() {
    GeoMigration.setBlockedProxy(false);
    final Optional<Boolean> blockedProxyOverride =
        GeoMigration.getBlockedProxyOverride(CurrentRequestContext.get());
    assertTrue(blockedProxyOverride.isPresent());
    assertFalse(blockedProxyOverride.get());
  }

  @Test
  public void getBlockedProxyOverrideRequestParam() {
    GeoMigration.setBlockedProxy(false);
    final Optional<Boolean> blockedProxyOverride = GeoMigration.getBlockedProxyOverride();
    assertTrue(blockedProxyOverride.isPresent());
    assertFalse(blockedProxyOverride.get());
  }
}
