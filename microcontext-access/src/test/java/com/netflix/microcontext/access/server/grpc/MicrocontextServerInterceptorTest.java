package com.netflix.microcontext.access.server.grpc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.verify;

import com.netflix.archaius.DefaultPropertyFactory;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.archaius.config.EmptyConfig;
import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.utils.RequestPriorityMetrics;
import com.netflix.microcontext.init.headers.Headers;
import com.netflix.microcontext.init.resolvers.AuthResolvers;
import com.netflix.passport.test.TestPassport;
import com.netflix.passport.test.TestUserInfo;
import com.netflix.spectator.api.DefaultRegistry;
import com.netflix.spectator.api.Registry;
import io.grpc.Metadata;
import io.grpc.Metadata.Key;
import io.grpc.ServerCall;
import io.grpc.ServerCall.Listener;
import io.grpc.ServerCallHandler;
import java.util.Optional;
import netflix.context.Context;
import netflix.context.auth.AuthContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MicrocontextServerInterceptorTest {

  @Mock private ServerCall<String, Integer> call;

  @Mock private RequestPriorityMetrics requestPriorityMetrics;

  private final Registry registry = new DefaultRegistry();
  private MicrocontextServerInterceptor interceptor;
  private final PropertyRepository propertyRepository =
      new DefaultPropertyFactory(EmptyConfig.INSTANCE);

  @Before
  public void setUp() {
    BindingContexts.push();
    interceptor =
        new MicrocontextServerInterceptor(registry, requestPriorityMetrics, propertyRepository);
  }

  @After
  public void tearDown() {
    BindingContexts.pop();
  }

  @Test
  public void testDefault() {
    final ServerCallHandler<String, Integer> realHandler =
        (serverCall, metadata) ->
            new Listener<String>() {
              @Override
              public void onMessage(String message) {
                assertTrue(CurrentMicrocontext.getRaw().isPresent());
                System.out.println("onMessage " + message);
              }

              @Override
              public void onHalfClose() {
                System.out.println("onHalfClose");
              }

              @Override
              public void onCancel() {
                System.out.println("onCancel");
              }

              @Override
              public void onComplete() {
                System.out.println("onComplete");
              }

              @Override
              public void onReady() {
                System.out.println("onReady");
              }
            };
    assertFalse(CurrentMicrocontext.getRaw().isPresent());
    final Listener<String> realListener =
        interceptor.interceptCall(call, new Metadata(), realHandler);
    assertFalse(CurrentMicrocontext.getRaw().isPresent());
    realListener.onMessage("foo");
    assertTrue(CurrentMicrocontext.getRaw().isPresent());
    verify(requestPriorityMetrics).emitPriorityMetrics(isA(Context.class));
  }

  @Test
  public void testHeader() {
    final ServerCallHandler<String, Integer> realHandler =
        (serverCall, metadata) ->
            new Listener<String>() {
              @Override
              public void onMessage(String message) {
                assertEquals("fooid", CurrentMicrocontext.get().getRequestId());
                System.out.println("onMessage " + message);
              }

              @Override
              public void onHalfClose() {
                System.out.println("onHalfClose");
              }

              @Override
              public void onCancel() {
                System.out.println("onCancel");
              }

              @Override
              public void onComplete() {
                System.out.println("onComplete");
              }

              @Override
              public void onReady() {
                System.out.println("onReady");
              }
            };
    final TestPassport randomTestPassport =
        TestPassport.builder().userInfo(TestUserInfo.builder().customerId(1234L).build()).build();
    assertFalse(CurrentMicrocontext.getRaw().isPresent());
    Metadata metadata = new Metadata();
    metadata.put(MicrocontextHeaders.MICROCONTEXT, "CgcKBWZvb2lk");
    final Key<String> passport = Key.of(Headers.PASSPORT, Metadata.ASCII_STRING_MARSHALLER);
    metadata.put(passport, randomTestPassport.toPassportSerialized());
    final Listener<String> realListener = interceptor.interceptCall(call, metadata, realHandler);
    assertFalse(CurrentMicrocontext.getRaw().isPresent());
    realListener.onMessage("foo");
    assertTrue(CurrentMicrocontext.getRaw().isPresent());
    final Optional<AuthContext> authContext = AuthResolvers.resolve();
    assertTrue(authContext.isPresent());
    assertEquals(1234L, authContext.get().getCurrentAuth().getCustomerId());
    verify(requestPriorityMetrics).emitPriorityMetrics(isA(Context.class));
  }
}
