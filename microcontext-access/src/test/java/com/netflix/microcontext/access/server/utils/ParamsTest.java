package com.netflix.microcontext.access.server.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.netflix.microcontext.init.headers.MapHeaderResolver;
import com.netflix.microcontext.init.utils.Params;
import com.netflix.protobuf.shaded.com.google.common.collect.Lists;
import com.netflix.protobuf.shaded.com.google.common.collect.Sets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import netflix.context.common.StringList;
import org.junit.Test;

public class ParamsTest {

  @Test
  public void split() {
    assertEquals(0, Params.split("").count());
    assertEquals(1, Params.split(" [\"en\"]").count());

    Stream<String> en = Params.split("en");
    Optional<String> first = en.findFirst();
    assertTrue(first.isPresent());
    assertEquals("en", first.get());

    Stream<String> en_ = Params.split("en ");
    Optional<String> first1 = en_.findFirst();
    assertTrue(first1.isPresent());
    assertEquals("en", first1.get());

    List<String> strings = Params.split("en , fr , , kr ,").collect(Collectors.toList());
    assertEquals(3, strings.size());
    assertEquals("en", strings.get(0));
    assertEquals("fr", strings.get(1));
    assertEquals("kr", strings.get(2));
  }

  @Test
  public void dejson() {
    final String s = Params.unJson("[\"en \",\"fr \",\"kr \"]");
    assertEquals("en ,fr ,kr ", s);
  }

  @Test
  public void flattenedStringsReturnsFlattenedSetOfStrings() {
    Map<String, StringList> stringsMap = new HashMap<>();
    stringsMap.put(
        "key",
        StringList.newBuilder().addAllValues(Lists.newArrayList("val1,val2", "val3")).build());
    MapHeaderResolver mapHeaderResolver = MapHeaderResolver.of(stringsMap);
    assertEquals(
        Sets.newHashSet("val1", "val2", "val3"), Params.flattenedStrings("key", mapHeaderResolver));
  }

  @Test
  public void flattenedStringsReturnsEmptySetIfKeyNotFound() {
    Map<String, StringList> emptyMap = new HashMap<>();
    assertEquals(
        Collections.emptySet(), Params.flattenedStrings("key", MapHeaderResolver.of(emptyMap)));
  }
}
