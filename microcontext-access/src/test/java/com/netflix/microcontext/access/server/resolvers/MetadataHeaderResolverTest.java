package com.netflix.microcontext.access.server.resolvers;

import static com.netflix.microcontext.access.server.resolvers.MetadataHeaderResolver.key;
import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.netflix.microcontext.init.headers.HeaderResolver;
import io.grpc.Metadata;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.Test;

public class MetadataHeaderResolverTest {

  @Test
  public void testEmpty() {
    final HeaderResolver resolver = MetadataHeaderResolver.of(new Metadata());
    final List<String> all = resolver.getAll("foo");
    assertNotNull(all);
    assertEquals(0, all.size());

    final Optional<String> getFirst = resolver.getFirst(Collections.singletonList("foo"));
    assertNotNull(getFirst);
    assertFalse(getFirst.isPresent());

    final Optional<String> get = resolver.get("foo");
    assertNotNull(get);
    assertFalse(get.isPresent());

    final boolean contains = resolver.contains("foo");
    assertFalse(contains);
  }

  @Test
  public void testHeader() {
    final Metadata metadata = new Metadata();
    metadata.put(key("foo"), "bar");
    final HeaderResolver resolver = MetadataHeaderResolver.of(metadata);
    final List<String> all = resolver.getAll("foo");
    assertNotNull(all);
    assertEquals(1, all.size());

    final Optional<String> getFirst = resolver.getFirst(Collections.singletonList("foo"));
    assertNotNull(getFirst);
    assertTrue(getFirst.isPresent());

    final Optional<String> get = resolver.get("foo");
    assertNotNull(get);
    assertTrue(get.isPresent());
    assertEquals("bar", get.get());

    final boolean contains = resolver.contains("foo");
    assertTrue(contains);
  }

  @Test
  public void testHeaderCase() {
    final Metadata metadata = new Metadata();
    metadata.put(key("Foo"), "bar");
    final HeaderResolver resolver = MetadataHeaderResolver.of(metadata);
    final List<String> all = resolver.getAll("foo");
    assertNotNull(all);
    assertEquals(1, all.size());

    final Optional<String> get = resolver.get("foo");
    assertNotNull(get);
    assertTrue(get.isPresent());
    assertEquals("bar", get.get());

    final boolean contains = resolver.contains("foo");
    assertTrue(contains);
  }
}
