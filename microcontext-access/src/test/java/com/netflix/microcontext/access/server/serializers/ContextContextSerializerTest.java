package com.netflix.microcontext.access.server.serializers;

import static org.junit.Assert.assertNotNull;

import com.netflix.microcontext.init.requestcontext.ContextContextSerializer;
import com.netflix.server.context.ContextSerializationException;
import netflix.context.Context;
import org.junit.Test;

/** Intended simply for testing ContextContextSerializer */
public class ContextContextSerializerTest {

  @Test
  public void deserializeFromString() throws ContextSerializationException {
    ContextContextSerializer serializer = ContextContextSerializer.INSTANCE;
    String serialized =
        "CiYKJDBCNEY0MUZELTM2RkUtNDQyQy04MURELTFBRjgyOEE3NTVGNxIECgJVUxoHCgVwdC1CUnoGCAMQAxgBggHGAQrDAQgBEgUIoDIQFBIFCMg1EAQSBQi0NhACEgUI2zgQBRIFCMw6EAYSBQigOxACEgUInj4QBRIFCO8+EAISBQjpPxADEgUIiEEQAxIFCPZHEAISBQjlSBACEgUIu00QAhIFCNpWEAISBQitYhAGEgYIsvYBEAQSBgi59wEQCRIGCLWaAhACEgYI1KMCEAISBgiwpwIQBxIGCO2qAhADEgYI4LACEAISBgjasQIQAhIGCMS1AhACEgYI37cCEAISBgixyAIQAooBfAoECgJVUxoECgJMQSIHCgVIT1VNQSoHCgU3MDM2NDoFCgNDU1RCDwoNMzcuMTIwLjIxNS43MFIGCgQ5MDA5cgoKCG0yNDcuY29teASSAxMKDWRyYWRpc1JlZ2lvbnMSAjQwkgMXCgpkcmFkaXNTaXRlEglueWMwMDEuaXiSAagBCn4IpcXl9ZWaqoADEhpLN0NJV1lCU1hKQjZURFdGR0VYTzdRWjNCWRoKCNbH4vWVmqqAAyIcChpTT0FTTFU3NlJaQzNCUEs1TUlHVEJNNkxTRTIECgJCUjoECgJVU0IHCgVwdC1CUkoECMCEPVIICgZpZGVsc2VaAwikGGABaAESJgokZjdlMzA3ZGMtMGVlYS00NTczLTlhMzItMGE1ZjhlNWNiMDE0ogExChMSEQoNMzcuMTIwLjIxNS43MBACKgwIq7P0kAYQgOWs6wI6DAirs/SQBhCA5azrAqoBcgoDCMwWIlcKVU5GQVBQTC0wMi1JUEhPTkUxMj01LUNFQUI2RkFBMURERTZCRjlBNDk1ODk3RkMxRENEQ0ZFMUM2QkJEM0I5NUY4OEIxQzg5NEI5M0JBNkQ0QzY5ODMqEgoCCCcSDAoKaU9TIE1vYmlsZQ==";
    Context context = serializer.deserialize(serialized, 0);
    assertNotNull(context);
    System.out.println("context: " + context);
  }
}
