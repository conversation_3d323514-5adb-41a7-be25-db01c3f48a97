package com.netflix.microcontext.access.server.utils;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;
import netflix.context.Context;
import netflix.context.ResolvedContexts;
import netflix.context.experimentation.Allocations;
import netflix.context.experimentation.ExperimentationContext;
import org.junit.jupiter.api.Test;

class ResolverContextUtilsTest {

  private final ExperimentationContext experimentationContext =
      ExperimentationContext.newBuilder()
          .setAccountAllocations(Allocations.newBuilder().putTestToCell(1, 5))
          .build();

  @Test
  void resolvedContextsEmpty() {
    // empty
    final Optional<ResolvedContexts> resolvedContexts =
        ResolverContextUtils.resolvedContexts(Context.getDefaultInstance());
    assertFalse(resolvedContexts.isPresent());
  }

  @Test
  void resolvedContextsExperimentation() {
    // experimentation
    final Context.Builder builder =
        Context.newBuilder()
            .setResolved(ResolverContextUtils.createResolvedContexts(experimentationContext));

    final ResolvedContexts.Builder resolvedContextsBuilder =
        ResolverContextUtils.resolvedContextsBuilder(builder);
    assertTrue(resolvedContextsBuilder.hasExperimentation());
    final ExperimentationContext experimentation = resolvedContextsBuilder.getExperimentation();
    assertEquals(1, experimentation.getAccountAllocations().getTestToCellCount());
    assertEquals(5, experimentation.getAccountAllocations().getTestToCellMap().get(1));
  }

  @Test
  void resolvedContextsBuilder() {
    // empty
    ResolvedContexts.Builder builder =
        ResolverContextUtils.resolvedContextsBuilder(Context.getDefaultInstance());
    assertFalse(builder.hasExperimentation());
  }
}
