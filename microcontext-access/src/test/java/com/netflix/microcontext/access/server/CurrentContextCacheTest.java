package com.netflix.microcontext.access.server;

import static org.junit.Assert.*;

import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.init.resolvers.AuthResolvers;
import com.netflix.microcontext.test.utils.TestCurrentMicrocontext;
import com.netflix.microcontext.test.utils.TestMicrocontext;
import netflix.context.auth.AuthContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class CurrentContextCacheTest {

  @Before
  public void before() {
    BindingContexts.push();
  }

  @After
  public void after() {
    BindingContexts.pop();
  }

  @Test
  public void getAuthContextPresent() {
    ContextCache contextCache = CurrentContextCache.get();
    final AuthContext authContext = contextCache.getAuthContext();
    assertSame(authContext, AuthContext.getDefaultInstance());
    final boolean safe =
        TestCurrentMicrocontext.setSafe(
            TestMicrocontext.builder()
                .setAuth(AuthContext.newBuilder().setVisitorDeviceId("foovdid").build())
                .build());
    assertTrue(safe);
    final AuthContext authContextUpdated = contextCache.getAuthContext();
    assertNotSame(authContext, authContextUpdated);
    assertEquals("foovdid", authContextUpdated.getVisitorDeviceId().getValue());
    AuthResolvers.clearPassport();
    final AuthContext authContextCleared = contextCache.getAuthContext();
    assertSame(authContextCleared, AuthContext.getDefaultInstance());
  }
}
