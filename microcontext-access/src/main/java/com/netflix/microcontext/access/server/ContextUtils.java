package com.netflix.microcontext.access.server;

import com.google.protobuf.FieldMask;
import com.google.protobuf.Message;
import com.google.protobuf.util.FieldMaskUtil;
import com.google.protobuf.util.FieldMaskUtil.MergeOptions;
import com.netflix.microcontext.access.Microcontext;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.Context;
import netflix.context.Context.Builder;

@SuppressWarnings("unused")
public class ContextUtils {

  private static final MergeOptions DEFAULT_MERGE_OPTIONS =
      new MergeOptions()
          .setReplaceRepeatedFields(true)
          .setReplaceMessageFields(true)
          .setReplacePrimitiveFields(true);

  public static Context.Builder toBuilder(@Nonnull Microcontext microcontext) {
    final Context context = microcontext.toProto();
    if (microcontext instanceof MicrocontextImpl) {
      return context.toBuilder();
    }
    Builder builder = Context.newBuilder().setRequestId(microcontext.getRequestId());
    if (microcontext.getCountry() != null) {
      builder.setCountry(microcontext.getCountry());
    }
    if (microcontext.getLocales() != null) {
      builder.addAllLocales(microcontext.getLocales());
    }
    if (isNotDefault(microcontext.getDevice())) {
      builder.setDevice(microcontext.getDevice());
    }
    if (isNotDefault(context.getUser())) {
      builder.setUser(context.getUser());
    }
    if (isNotDefault(microcontext.getClient())) {
      builder.setClient(microcontext.getClient());
    }
    if (isNotDefault(microcontext.getVisit())) {
      builder.setVisit(microcontext.getVisit());
    }
    if (isNotDefault(microcontext.getVideo())) {
      builder.setVideo(microcontext.getVideo());
    }
    return builder.setResolved(context.getResolved());
  }

  public static Microcontext fromInstance(@Nullable Context context) {
    return context != null ? fromProto(context) : fromDefault();
  }

  public static Microcontext fromDefault() {
    return fromProto(Context.getDefaultInstance());
  }

  public static Microcontext fromProto(@Nonnull Context context) {
    return new MicrocontextImpl(context);
  }

  public static Context.Builder merge(
      FieldMask fieldMask, @Nonnull Context updated, @Nonnull Context destination) {
    final Builder builder = destination.toBuilder();
    FieldMaskUtil.merge(fieldMask, updated, builder, DEFAULT_MERGE_OPTIONS);
    return builder;
  }

  public static boolean isDefault(@Nonnull Message m) {
    return m == m.getDefaultInstanceForType();
  }

  public static boolean isNotDefault(@Nonnull Message m) {
    return m != m.getDefaultInstanceForType();
  }
}
