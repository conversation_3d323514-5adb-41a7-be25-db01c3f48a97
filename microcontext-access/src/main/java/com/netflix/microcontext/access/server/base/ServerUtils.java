package com.netflix.microcontext.access.server.base;

import static com.netflix.microcontext.init.headers.Headers.MICROCONTEXT_HEADER;

import com.netflix.microcontext.init.serializers.Serializers;
import java.util.Optional;
import javax.servlet.http.HttpServletRequest;
import netflix.context.Context;

@SuppressWarnings("unused")
public class ServerUtils {

  public static Optional<Context> fromHeader(HttpServletRequest request) {
    return Optional.ofNullable(request.getHeader(MICROCONTEXT_HEADER))
        .flatMap(Serializers::fromString);
  }
}
