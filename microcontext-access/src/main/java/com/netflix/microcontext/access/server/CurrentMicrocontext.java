package com.netflix.microcontext.access.server;

import static com.netflix.microcontext.access.server.ContextUtils.fromDefault;
import static com.netflix.microcontext.access.server.ContextUtils.fromProto;

import com.google.protobuf.FieldMask;
import com.netflix.lang.BindingContexts;
import com.netflix.lang.RequestVariable;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.init.requestcontext.ContextContextSerializer;
import com.netflix.microcontext.init.serializers.Serializers;
import com.netflix.server.context.ContextSerializationException;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Spectator;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import javax.annotation.Nonnull;
import netflix.context.Context;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Holds the value of the microcontext for this scope */
@SuppressWarnings("unused")
public class CurrentMicrocontext {

  private static final Logger logger = LoggerFactory.getLogger(CurrentMicrocontext.class);
  public static final String CONTEXT_KEY = "microcontext";

  private static final RequestVariable<AtomicReference<Microcontext>> CURRENT =
      new RequestVariable<AtomicReference<Microcontext>>() {
        @Override
        public AtomicReference<Microcontext> initialValue() {
          return new AtomicReference<>();
        }
      };

  private CurrentMicrocontext() {}

  public static @Nonnull Microcontext get() {
    AtomicReference<Microcontext> ref = CURRENT.get();
    incrementget();
    Microcontext microcontext = ref.get();
    if (microcontext != null) {
      return microcontext;
    } else {
      Microcontext local = fromRequestContext();
      if (!ref.compareAndSet(null, local)) {
        // lost the race
        return ref.get();
      }
      return local;
    }
  }

  public static Optional<Microcontext> getRaw() {
    return Optional.ofNullable(CURRENT.get().get());
  }

  /**
   * Sets the microcontext using the serialized version of the Context
   *
   * @param serializedContext to set (serialized)
   * @return {@code true} if successful
   */
  public static boolean set(@Nonnull String serializedContext) {
    incrementset();
    if (notInContext()) {
      return false;
    }
    Optional<Context> context = Serializers.fromString(serializedContext);
    return context.filter(CurrentMicrocontext::set).isPresent();
  }

  /**
   * Sets the microcontext similar dynamics as {@link AtomicReference#set(Object)}. This should not
   * be necessary outside rare circumstances in the service gateway
   *
   * @param context to set
   * @return {@code true} if successful
   */
  public static boolean set(@Nonnull Context context) {
    return set(context, CurrentRequestContext.get());
  }

  public static boolean set(@Nonnull Context context, @Nonnull RequestContext requestContext) {
    incrementset();
    return MicrocontextInternalUtils.internalSet(context, requestContext);
  }

  /**
   * Package-private method to update the current microcontext. This is used by
   * MicrocontextInternalUtils to update the context without going through the public API.
   */
  static void updateCurrent(@Nonnull Context context) {
    CURRENT.get().set(fromProto(context));
  }

  /**
   * Merges the microcontext with the current value and performs a CAS similar dynamics as {@link
   * AtomicReference#compareAndSet(Object, Object)} .
   *
   * @param source to set
   * @return {@code true} if successful
   */
  public static boolean mergeAndSet(@Nonnull Context source, @Nonnull FieldMask fieldMask) {
    incrementmerge();
    if (notInContext()) {
      return false;
    }
    final Context merged = ContextUtils.merge(fieldMask, source, get().toProto()).build();
    CURRENT.get().set(fromProto(merged));
    propagate(merged);
    return true;
  }

  private static boolean notInContext() {
    if (!BindingContexts.isInContext()) {
      logger.warn(
          "Setting microcontext outside of thread scope {}",
          Thread.currentThread(),
          new Exception());
      return true;
    }
    return false;
  }

  /** Clears the current context, should only be used for testing */
  public static void clear() {
    CURRENT.get().set(null);
    clearRequestContext(CurrentRequestContext.get());
  }

  /**
   * Sets the microcontext similar dynamics as {@link AtomicReference#compareAndSet(Object,
   * Object)}. This should not be necessary outside rare circumstances in the service gateway
   *
   * @param expectedValue the expected value
   * @param newValue the new value
   * @return {@code true} if successful
   */
  public static boolean compareAndSet(Microcontext expectedValue, Microcontext newValue) {
    incrementcas();
    if (!BindingContexts.isInContext()) {
      return false;
    }
    boolean result = CURRENT.get().compareAndSet(expectedValue, newValue);
    if (result) {
      propagate(newValue);
    }
    return result;
  }

  private static Microcontext fromRequestContext() {
    return requestContext().map(ContextUtils::fromProto).orElse(fromDefault());
  }

  public static Optional<Context> requestContext() {
    return requestContext(CurrentRequestContext.get());
  }

  public static Optional<Context> requestContext(RequestContext requestContext) {
    try {
      return Optional.ofNullable(
          requestContext.getContext(CONTEXT_KEY, ContextContextSerializer.INSTANCE));
    } catch (ContextSerializationException e) {
      return Optional.empty();
    }
  }

  private static void propagate(Context context) {
    propagate(context, CurrentRequestContext.get());
  }

  private static void propagate(Context context, @Nonnull RequestContext requestContext) {
    if (ContextUtils.isNotDefault(context)) {
      requestContext.addContext(CONTEXT_KEY, context, ContextContextSerializer.INSTANCE);
    }
  }

  private static void clearRequestContext(RequestContext requestContext) {
    requestContext.removeContext(CONTEXT_KEY);
  }

  private static void propagate(Microcontext context) {
    propagate(context.toProto());
  }

  private static final class Counters {
    private static final Counter getcounter =
        Spectator.globalRegistry().counter("microcontext.get");
    private static final Counter setcounter =
        Spectator.globalRegistry().counter("microcontext.set");
    private static final Counter mergecounter =
        Spectator.globalRegistry().counter("microcontext.merge");
    private static final Counter cascounter =
        Spectator.globalRegistry().counter("microcontext.compareAndSet");
  }

  private static void incrementget() {
    // disable for now, this is costly under high load
    // Counters.getcounter.increment();
  }

  private static void incrementmerge() {
    Counters.mergecounter.increment();
  }

  private static void incrementset() {
    Counters.setcounter.increment();
  }

  private static void incrementcas() {
    Counters.cascounter.increment();
  }

  private static String appName() {
    final String appName = CurrentRequestContext.get().getAppName();
    return appName == null ? "unknown" : appName;
  }
}
