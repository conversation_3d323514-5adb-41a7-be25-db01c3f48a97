package com.netflix.microcontext.access.server.migration;

import com.google.protobuf.FieldMask;
import com.google.protobuf.util.FieldMaskUtil;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.ContextUtils;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.server.context.ContextSerializationException;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Spectator;
import java.util.Optional;
import netflix.context.Context;
import netflix.context.Context.Builder;
import netflix.context.video.VideoContext;

public class GeoMigration {

  private static final Counter RC_METRIC =
      Spectator.globalRegistry().counter("microcontext.migration.geo.blocked_proxy.rc");
  public static final String REQUEST_CONTEXT_KEY = "VMS_VPN";

  /** This reads from the standard Microcontext sources */
  public static boolean getBlockedProxy() {
    return getBlockedProxy(CurrentMicrocontext.get());
  }

  /** This reads from the standard Microcontext sources */
  public static boolean getBlockedProxy(Microcontext microcontext) {
    if (microcontext.getVideo().hasBlockedProxy()) {
      return microcontext.getVideo().getBlockedProxy();
    }
    return microcontext.getGeo().getBlockedProxy();
  }

  /**
   * In addition to reading from Microcontext sources this will also consult legacy paths such as
   * RequestContext
   */
  public static boolean getBlockedProxyAll() {
    return getBlockedProxyAll(CurrentMicrocontext.get(), CurrentRequestContext.get());
  }

  /**
   * In addition to reading from Microcontext sources this will also consult legacy paths such as
   * RequestContext
   */
  public static boolean getBlockedProxyAll(final RequestContext requestContext) {
    return getBlockedProxyAll(CurrentMicrocontext.get(), requestContext);
  }

  private static boolean getBlockedProxyAll(
      final Microcontext microcontext, final RequestContext requestContext) {
    if (microcontext.getVideo().hasBlockedProxy()) {
      return microcontext.getVideo().getBlockedProxy();
    }
    return getBlockedProxyRequestContext(requestContext)
        .orElseGet(() -> microcontext.getGeo().getBlockedProxy());
  }

  /** Sets the value in Microcontext as an override */
  public static boolean setBlockedProxy(boolean blockedProxy) {
    Context ctx =
        Context.newBuilder()
            .setVideo(VideoContext.newBuilder().setBlockedProxy(blockedProxy))
            .build();
    final FieldMask fieldMask = FieldMaskUtil.fromString("video.blocked_proxy");
    return CurrentMicrocontext.mergeAndSet(ctx, fieldMask);
  }

  /** Sets the value in Microcontext as an override in all possible locations */
  public static boolean setBlockedProxyAll(boolean blockedProxy) {
    return setBlockedProxyAll(blockedProxy, CurrentRequestContext.get());
  }

  public static boolean setBlockedProxyAll(boolean blockedProxy, RequestContext requestContext) {
    setBlockedProxyRequestContext(blockedProxy, requestContext);
    return setBlockedProxy(blockedProxy);
  }

  static void setBlockedProxyRequestContext(boolean blockedProxy) {
    setBlockedProxyRequestContext(blockedProxy, CurrentRequestContext.get());
  }

  static void setBlockedProxyRequestContext(boolean blockedProxy, RequestContext requestContext) {
    requestContext.addContext(REQUEST_CONTEXT_KEY, Boolean.toString(blockedProxy));
  }

  /** Removes the overriden value in Microcontext if it exists */
  public static boolean clearBlockedProxy() {
    final Microcontext microcontext = CurrentMicrocontext.get();
    final Builder builder = microcontext.toProto().toBuilder();
    builder.getVideoBuilder().clearBlockedProxy();
    // TODO look at a fine grained clear method on Microcontext
    return CurrentMicrocontext.compareAndSet(microcontext, ContextUtils.fromProto(builder.build()));
  }

  /** Removes the overriden value in Microcontext and RequestContext if it exists */
  public static boolean clearBlockedProxyAll() {
    return clearBlockedProxyAll(CurrentRequestContext.get());
  }

  /** Removes the overriden value in Microcontext and RequestContext if it exists */
  public static boolean clearBlockedProxyAll(RequestContext requestContext) {
    final boolean result = requestContext.removeContext(REQUEST_CONTEXT_KEY);
    return result && clearBlockedProxy();
  }

  /**
   * Returns the value of blocked proxy from microcontext and requestontext or empty if none exists
   */
  public static Optional<Boolean> getBlockedProxyOverride() {
    return getBlockedProxyOverride(CurrentRequestContext.get());
  }

  /**
   * Returns the value of blocked proxy from microcontext and requestontext or empty if none exists
   */
  public static Optional<Boolean> getBlockedProxyOverride(final RequestContext requestContext) {
    return getBlockedProxyOverride(CurrentMicrocontext.get(), requestContext);
  }

  /**
   * Returns the value of blocked proxy from microcontext and requestontext or empty if none exists
   */
  private static Optional<Boolean> getBlockedProxyOverride(
      final Microcontext microcontext, final RequestContext requestContext) {
    if (microcontext.getVideo().hasBlockedProxy()) {
      return Optional.of(microcontext.getVideo().getBlockedProxy());
    }
    return getBlockedProxyRequestContext(requestContext);
  }

  private static Optional<Boolean> getBlockedProxyRequestContext(RequestContext requestContext) {
    try {
      final String value = requestContext.getContext(REQUEST_CONTEXT_KEY);
      if (value != null) {
        RC_METRIC.increment();
        return Optional.of(Boolean.parseBoolean(value));
      }
    } catch (ContextSerializationException ignored) {
    }
    return Optional.empty();
  }
}
