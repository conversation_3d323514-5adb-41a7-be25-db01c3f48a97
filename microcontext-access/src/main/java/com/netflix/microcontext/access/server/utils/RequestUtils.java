package com.netflix.microcontext.access.server.utils;

import com.netflix.microcontext.init.utils.Params;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import netflix.context.common.StringList;

@SuppressWarnings("unused")
public class RequestUtils {

  public static Map<String, StringList> headerMap(HttpServletRequest request) {
    Map<String, StringList> map = new HashMap<>();
    Enumeration<String> headerNames = request.getHeaderNames();
    while (headerNames.hasMoreElements()) {
      String headerName = headerNames.nextElement().toLowerCase();
      // exclude request context
      if (!headerName.startsWith("x-netflix.request.sub.context")
          && !headerName.startsWith("x-netflix.request.context"))
        map.put(headerName, Params.stringList(request.getHeaders(headerName)));
    }
    return map;
  }
}
