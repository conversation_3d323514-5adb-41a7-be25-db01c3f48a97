package com.netflix.microcontext.access.server.grpc;

import static com.netflix.microcontext.access.server.grpc.MicrocontextHeaders.MICROCONTEXT_BINARY;

import com.netflix.microcontext.init.serializers.Serializers;
import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientInterceptor;
import io.grpc.ForwardingClientCall;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;
import javax.annotation.Nonnull;
import netflix.context.Context;

/** Passes the provided microcontext as a header mainly for testing */
@SuppressWarnings("unused")
public class MicrocontextF<PERSON>wardingInterceptor implements ClientInterceptor {

  private final Context context;

  public static MicrocontextForwardingInterceptor of(@Nonnull Context context) {
    return new MicrocontextForwardingInterceptor(context);
  }

  private MicrocontextForwardingInterceptor(@Nonnull Context context) {
    this.context = context;
  }

  @Override
  public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(
      MethodDescriptor<ReqT, RespT> methodDescriptor, CallOptions callOptions, Channel channel) {
    return new ForwardingClientCall.SimpleForwardingClientCall<ReqT, RespT>(
        channel.newCall(methodDescriptor, callOptions)) {
      @Override
      public void start(Listener<RespT> responseListener, Metadata headers) {
        headers.put(MICROCONTEXT_BINARY, Serializers.toBytes(context));
        super.start(responseListener, headers);
      }
    };
  }
}
