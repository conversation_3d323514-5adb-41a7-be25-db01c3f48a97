package com.netflix.microcontext.access;

import com.netflix.microcontext.access.server.ContextUtils;
import com.netflix.type.protogen.BasicTypes.Country;
import com.netflix.type.protogen.BasicTypes.DeviceType;
import com.netflix.type.protogen.BasicTypes.Locale;
import java.util.List;
import java.util.Optional;
import netflix.context.Context;
import netflix.context.auth.AuthContext;
import netflix.context.client.ClientContext;
import netflix.context.device.DeviceContext;
import netflix.context.geo.GeoContext;
import netflix.context.user.UserContext;
import netflix.context.video.VideoContext;
import netflix.context.visit.VisitContext;

public interface Microcontext {

  String getRequestId();

  Country getCountry();

  Optional<Locale> getLocale();

  List<Locale> getLocales();

  Optional<String> getEsn();

  Optional<DeviceType> getDeviceType();

  /**
   * @return the {@link ClientContext} or a default instance if not present
   */
  ClientContext getClient();

  /**
   * @return the {@link DeviceContext} or a default instance if not present
   */
  DeviceContext getDevice();

  GeoContext getGeo();

  VisitContext getVisit();

  /**
   * @return the {@link UserContext} or a default instance if not present
   */
  UserContext getUser();

  /**
   * @return the {@link VideoContext} or a default instance if not present
   */
  VideoContext getVideo();

  /**
   * prefer passport
   *
   * @return the {@link AuthContext} or a default instance if not present
   */
  AuthContext getAuth();

  /**
   * prefer passport
   *
   * @return the {@link AuthContext} as {@link Optional}
   */
  default Optional<AuthContext> getAuthOptional() {
    final AuthContext auth = getAuth();
    if (auth == null || ContextUtils.isDefault(auth)) {
      return Optional.empty();
    }
    return Optional.of(auth);
  }

  boolean isDefault();

  Context toProto();
}
