package com.netflix.microcontext.access.server;

import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.init.requestcontext.ContextContextSerializer;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import javax.annotation.Nonnull;
import netflix.context.Context;
import netflix.context.Context.Builder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Internal utility methods for Microcontext operations. These methods are not intended to be used
 * by consumers.
 */
public class MicrocontextInternalUtils {
  private static final Logger logger = LoggerFactory.getLogger(MicrocontextInternalUtils.class);

  private MicrocontextInternalUtils() {}

  /**
   * Sets microcontext without incrementing the set counter. This is meant to be used only
   * internally by Microcontext classes.
   *
   * @param context to set
   * @param requestContext the request context
   * @return {@code true} if successful
   */
  public static boolean internalSet(
      @Nonnull Context context, @Nonnull RequestContext requestContext) {
    if (notInContext()) {
      return false;
    }
    final Context updated = clearFields(context);

    // Update the current context and the request context
    CurrentMicrocontext.updateCurrent(updated);

    // Add to request context if not default
    if (ContextUtils.isNotDefault(updated)) {
      requestContext.addContext(
          CurrentMicrocontext.CONTEXT_KEY, updated, ContextContextSerializer.INSTANCE);
    }

    return true;
  }

  /**
   * Sets microcontext without incrementing the set counter. This is meant to be used only
   * internally by Microcontext classes.
   *
   * @param context to set
   * @return {@code true} if successful
   */
  public static boolean internalSet(@Nonnull Context context) {
    return internalSet(context, CurrentRequestContext.get());
  }

  @SuppressWarnings("deprecation")
  @Nonnull
  private static Context clearFields(@Nonnull Context context) {
    if (context.hasGeo()) {
      final Builder builder = context.toBuilder();
      if (context.hasGeo()) {
        builder.clearGeo();
      }
      return builder.build();
    }
    return context;
  }

  private static boolean notInContext() {
    if (!BindingContexts.isInContext()) {
      logger.warn(
          "Setting microcontext outside of thread scope {}",
          Thread.currentThread(),
          new Exception());
      return true;
    }
    return false;
  }
}
