package com.netflix.microcontext.access.server.utils;

import com.google.protobuf.Any;
import com.google.protobuf.InvalidProtocolBufferException;
import com.netflix.microcontext.access.Microcontext;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.Context;
import netflix.context.ContextOrBuilder;
import netflix.context.ResolvedContexts;
import netflix.context.experimentation.ExperimentationContext;

@SuppressWarnings("unused")
public class ResolverContextUtils {

  private ResolverContextUtils() {}

  public static Optional<ResolvedContexts> resolvedContexts(@Nonnull Microcontext microcontext) {
    return resolvedContexts(microcontext.toProto());
  }

  public static Optional<ResolvedContexts> resolvedContexts(@Nonnull ContextOrBuilder context) {
    final Any resolved = context.getResolved();
    if (resolved.is(ResolvedContexts.class)) {
      try {
        return Optional.ofNullable(resolved.unpack(ResolvedContexts.class));
      } catch (InvalidProtocolBufferException ignored) {
        // ignore
      }
    }
    return Optional.empty();
  }

  public static ResolvedContexts.Builder resolvedContextsBuilder(
      @Nonnull ContextOrBuilder context) {
    return resolvedContexts(context)
        .map(ResolvedContexts::toBuilder)
        .orElse(ResolvedContexts.newBuilder());
  }

  public static boolean hasExperimentation(@Nullable ContextOrBuilder context) {
    if (context == null) {
      return false;
    }
    return resolvedExperimentation(context).isPresent();
  }

  public static Optional<ExperimentationContext> resolvedExperimentation(
      @Nonnull ContextOrBuilder context) {
    return ResolverContextUtils.resolvedContexts(context)
        .flatMap(
            resolvedContexts -> {
              if (resolvedContexts.hasExperimentation()) {
                return Optional.of(resolvedContexts.getExperimentation());
              }
              return Optional.empty();
            });
  }

  public static Context.Builder setExperimentation(
      Context.Builder builder, ExperimentationContext experimentationContext) {
    return builder.setResolved(
        Any.pack(
            resolvedContextsBuilder(builder).setExperimentation(experimentationContext).build()));
  }

  public static Any createResolvedContexts(@Nonnull ExperimentationContext experimentationContext) {
    return Any.pack(
        ResolvedContexts.newBuilder().setExperimentation(experimentationContext).build());
  }
}
