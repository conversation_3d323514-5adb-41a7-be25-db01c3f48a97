package com.netflix.microcontext.access.server.utils;

import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.MicrocontextManager;
import com.netflix.microcontext.access.server.ContextUtils;
import java.util.concurrent.atomic.AtomicReference;
import netflix.context.Context;

/** Testing friendly version of the MicrocontextManager that avoids complications of threadlocals */
public class LocalMicrocontextManager implements MicrocontextManager {

  private final AtomicReference<Microcontext> REF;

  public LocalMicrocontextManager() {
    REF = new AtomicReference<>(ContextUtils.fromProto(Context.getDefaultInstance()));
  }

  public LocalMicrocontextManager(Microcontext microcontext) {
    REF = new AtomicReference<>(microcontext);
  }

  @Override
  public Microcontext getContext() {
    return REF.get();
  }

  @Override
  public boolean compareAndSetContext(Microcontext expected, Microcontext microcontext) {
    return REF.compareAndSet(expected, microcontext);
  }
}
