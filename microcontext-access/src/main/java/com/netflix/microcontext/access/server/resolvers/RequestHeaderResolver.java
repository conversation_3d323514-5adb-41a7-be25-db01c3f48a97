package com.netflix.microcontext.access.server.resolvers;

import com.netflix.microcontext.init.headers.HeaderResolver;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.servlet.http.HttpServletRequest;

public class RequestHeaderResolver implements HeaderResolver {

  private final HttpServletRequest servletRequest;

  public static HeaderResolver of(HttpServletRequest request) {
    return new RequestHeaderResolver(request);
  }

  private RequestHeaderResolver(HttpServletRequest servletRequest) {
    this.servletRequest = servletRequest;
  }

  @Override
  public Optional<String> get(@Nonnull String name) {
    return Optional.ofNullable(servletRequest.getHeader(name));
  }

  @Nonnull
  @Override
  public List<String> getAll(@Nonnull String name) {
    return Collections.list(servletRequest.getHeaders(name));
  }

  @Override
  public boolean contains(@Nonnull String name) {
    return servletRequest.getHeader(name) != null;
  }
}
