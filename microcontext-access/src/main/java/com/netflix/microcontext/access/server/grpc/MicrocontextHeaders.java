package com.netflix.microcontext.access.server.grpc;

import static com.netflix.microcontext.init.headers.Headers.MICROCONTEXT_HEADER;

import io.grpc.Metadata;

public class MicrocontextHeaders {
  public static final Metadata.Key<String> MICROCONTEXT =
      Metadata.Key.of(MICROCONTEXT_HEADER, Metadata.ASCII_STRING_MARSHALLER);
  public static final Metadata.Key<byte[]> MICROCONTEXT_BINARY =
      Metadata.Key.of(
          MICROCONTEXT_HEADER + Metadata.BINARY_HEADER_SUFFIX, Metadata.BINARY_BYTE_MARSHALLER);
}
