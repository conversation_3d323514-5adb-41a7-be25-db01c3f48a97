package com.netflix.microcontext.access.server;

import com.netflix.lang.RequestVariable;
import com.netflix.microcontext.init.resolvers.UserResolvers;
import com.netflix.passport.introspect.PassportIdentityFactory;
import com.netflix.server.context.ContextUpdateListener;
import com.netflix.server.context.ContextUpdateManager;
import com.netflix.server.context.RequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CurrentContextCache {

  private static final Logger logger = LoggerFactory.getLogger(CurrentContextCache.class);

  private static final String PASSPORT_LOWERCASE =
      PassportIdentityFactory.REQUEST_CONTEXT_PASSPORT_CONTEXT_NAME.toLowerCase();
  private static final String GEO_DATA_LOWERCASE = "geodata";

  private static final RequestVariable<ContextCache> CACHE =
      new RequestVariable<ContextCache>() {
        @Override
        protected ContextCache initialValue() {
          return new ContextCache();
        }
      };

  static {
    try {
      final boolean registered =
          ContextUpdateManager.register(
              "microcontext",
              new ContextUpdateListener() {

                @Override
                public <T> void contextUpdated(
                    RequestContext requestContext, String name, T previous, T updated) {
                  if (PASSPORT_LOWERCASE.equals(name)) {
                    CACHE.get().clearAuthContext();
                  }
                  if (GEO_DATA_LOWERCASE.equals(name)) {
                    CACHE.get().clearGeoContext();
                  }
                  if (UserResolvers.CACHE_KEY.equals(name)) {
                    CACHE.get().clearUserContext();
                  }
                }

                @Override
                public <T> void contextRemoved(
                    RequestContext requestContext, String name, T previous) {
                  if (PASSPORT_LOWERCASE.equals(name)) {
                    CACHE.get().clearAuthContext();
                  }
                  if (GEO_DATA_LOWERCASE.equals(name)) {
                    CACHE.get().clearGeoContext();
                  }
                  if (UserResolvers.CACHE_KEY.equals(name)) {
                    CACHE.get().clearUserContext();
                  }
                }
              });
      if (!registered) {
        logger.error("Failed to register context listener");
      }
    } catch (Throwable t) {
      logger.error("Failed to register context listener", t);
    }
  }

  public static ContextCache get() {
    return CACHE.get();
  }
}
