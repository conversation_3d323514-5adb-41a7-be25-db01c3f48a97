package com.netflix.microcontext.access.server;

import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.migration.DeviceMigration;
import com.netflix.microcontext.init.requestcontext.ContextContextSerializer;
import com.netflix.spectator.api.Spectator;
import com.netflix.type.protogen.BasicTypes.Country;
import com.netflix.type.protogen.BasicTypes.DeviceType;
import com.netflix.type.protogen.BasicTypes.Locale;
import java.util.List;
import java.util.Optional;
import netflix.context.Context;
import netflix.context.auth.AuthContext;
import netflix.context.client.ClientContext;
import netflix.context.device.DeviceContext;
import netflix.context.geo.GeoContext;
import netflix.context.user.UserContext;
import netflix.context.video.VideoContext;
import netflix.context.visit.VisitContext;

public final class MicrocontextImpl implements Microcontext {

  private final Context context;
  private static MicrocontextAccessMetrics METRICS;
  private static final ContextContextSerializer contextSerializer =
      ContextContextSerializer.INSTANCE;

  MicrocontextImpl(Context context) {
    this.context = context != null ? context : Context.getDefaultInstance();
  }

  @Override
  @Deprecated
  public String getRequestId() {
    metrics().requestId();
    return context.getBoxedRequestId();
  }

  @Override
  public Country getCountry() {
    if (context.hasCountry()) {
      return context.getCountry();
    }
    if (getGeo().hasCountry()) {
      return getGeo().getCountry();
    }
    return Country.getDefaultInstance();
  }

  @Override
  public Optional<Locale> getLocale() {
    return context.getLocalesCount() > 0 ? Optional.of(context.getLocales(0)) : Optional.empty();
  }

  public static String toSerialized(Context context) {
    return contextSerializer.serialize(context);
  }

  @Override
  public List<Locale> getLocales() {
    return context.getLocalesList();
  }

  @Override
  public Optional<String> getEsn() {
    return context.getDevice().getOptionalEsn();
  }

  @Override
  public Optional<DeviceType> getDeviceType() {
    return DeviceMigration.deviceType(context);
  }

  @Override
  public GeoContext getGeo() {
    metrics().geo();
    return CurrentContextCache.get().getGeoContext();
  }

  @Override
  public UserContext getUser() {
    metrics().user();
    return CurrentContextCache.get().getUserContext(context);
  }

  @Override
  public ClientContext getClient() {
    return context.getClient();
  }

  @Override
  public DeviceContext getDevice() {
    return context.getDevice();
  }

  @Override
  public VisitContext getVisit() {
    return context.getVisit();
  }

  @Override
  @Deprecated
  public VideoContext getVideo() {
    return context.getVideo();
  }

  @Override
  public AuthContext getAuth() {
    metrics().auth();
    return CurrentContextCache.get().getAuthContext();
  }

  @Override
  public Optional<AuthContext> getAuthOptional() {
    metrics().auth();
    return CurrentContextCache.get().getAuthContextOptional();
  }

  @Override
  public boolean isDefault() {
    return ContextUtils.isDefault(context);
  }

  @Override
  public Context toProto() {
    metrics().proto();
    return context;
  }

  private static MicrocontextAccessMetrics metrics() {
    if (METRICS == null) {
      METRICS = new MicrocontextAccessMetrics(Spectator.globalRegistry());
    }
    return METRICS;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (!(o instanceof MicrocontextImpl)) {
      return false;
    }
    MicrocontextImpl that = (MicrocontextImpl) o;
    return context.equals(that.context);
  }

  @Override
  public int hashCode() {
    return context.hashCode();
  }

  @Override
  public String toString() {
    return context.toString();
  }
}
