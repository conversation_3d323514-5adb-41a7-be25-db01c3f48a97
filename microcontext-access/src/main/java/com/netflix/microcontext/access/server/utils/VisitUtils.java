package com.netflix.microcontext.access.server.utils;

import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import java.time.ZoneId;
import java.util.Optional;
import javax.annotation.Nonnull;
import netflix.context.visit.AppState;

@SuppressWarnings("unused")
public class VisitUtils {

  public static boolean isBackgroundOrIdle(Microcontext microcontext) {
    final AppState appState = microcontext.getVisit().getPriority().getAppState();
    return appState == AppState.APP_STATE_BACKGROUND || appState == AppState.APP_STATE_IDLE;
  }

  public static Optional<String> getIanaTimezone() {
    return getIanaTimezone(CurrentMicrocontext.get());
  }

  public static Optional<String> getIanaTimezone(@Nonnull Microcontext microcontext) {
    final Optional<String> override = microcontext.getVisit().getOptionalOverrideIanaTimezone();
    if (override.isPresent()) {
      return override;
    }

    return microcontext.getGeo().getOptionalIanaTimezone();
  }

  public static Optional<ZoneId> getIanaZoneId(@Nonnull Microcontext microcontext) {
    return getIanaTimezone(microcontext).map(ZoneId::of);
  }
}
