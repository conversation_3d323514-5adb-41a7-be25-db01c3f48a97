package com.netflix.microcontext.access.server.migration;

import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.init.resolvers.UserResolvers;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Registry;
import com.netflix.spectator.api.Spectator;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Nullable;
import netflix.context.video.VideoContext;

public class ContentPreviewMigration {

  private static final Registry registry = Spectator.globalRegistry();
  private static final Counter rc =
      registry.counter("microcontext.migration.contentpreview", "result", "rc");
  private static final Counter mc =
      registry.counter("microcontext.migration.contentpreview", "result", "mc");
  private static final Counter empty =
      registry.counter("microcontext.migration.contentpreview", "result", "empty");

  public static boolean isCurrentUserContentPreview() {
    final Microcontext microcontext = CurrentMicrocontext.get();
    return isCurrentUserContentPreview(
        microcontext.getAuth().getCurrentAuth().getCustomerId(), microcontext.getVideo());
  }

  public static boolean isCurrentUserContentPreview(@Nullable Long profileId) {
    return isCurrentUserContentPreview(profileId, CurrentMicrocontext.get().getVideo());
  }

  public static boolean isCurrentUserContentPreview(
      @Nullable Long profileId, VideoContext videoContext) {
    if (profileId == null || profileId == 0L) {
      return false;
    }
    return getContentPreviewId(videoContext).filter(l -> Objects.equals(profileId, l)).isPresent();
  }

  public static Optional<Long> getContentPreviewId() {
    return getContentPreviewId(CurrentMicrocontext.get().getVideo());
  }

  public static Optional<Long> getContentPreviewId(@Nullable VideoContext videoContext) {
    final Optional<Long> requestContext = UserResolvers.getContextPreviewId().map(Long::parseLong);
    if (requestContext.isPresent()) {
      rc.increment();
      return requestContext;
    }

    if (videoContext != null && videoContext.hasContentPreviewAccount()) {
      mc.increment();
      return Optional.ofNullable(videoContext.getContentPreviewAccount().getId());
    }
    empty.increment();
    return Optional.empty();
  }
}
