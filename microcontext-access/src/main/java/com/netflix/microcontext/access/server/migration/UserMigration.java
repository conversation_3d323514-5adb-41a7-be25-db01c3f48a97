package com.netflix.microcontext.access.server.migration;

import com.netflix.microcontext.access.server.ContextUtils;
import com.netflix.microcontext.init.resolvers.UserResolvers;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Registry;
import com.netflix.spectator.api.Spectator;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Nonnull;
import netflix.context.ContextOrBuilder;
import netflix.context.user.UserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UserMigration {
  private static final Logger logger = LoggerFactory.getLogger(UserMigration.class);

  private static final Registry registry = Spectator.globalRegistry();
  private static final Counter rc = registry.counter("microcontext.migration.user", "result", "rc");
  private static final Counter mc = registry.counter("microcontext.migration.user", "result", "mc");
  private static final Counter empty =
      registry.counter("microcontext.migration.user", "result", "empty");

  public static Optional<UserContext> getUser(ContextOrBuilder context) {
    final Optional<UserContext> userRC = UserResolvers.requestContext();
    final Optional<UserContext> userMC = getUserFromContext(context);

    // try and catch mismatches
    final boolean match;
    if (userRC.isPresent()
        && userMC.isPresent()
        && Objects.equals(
            userRC.get().getCurrentUser().getId(), userMC.get().getCurrentUser().getId())) {
      match = true;
    } else if (!userRC.isPresent() && !userMC.isPresent()) {
      match = true;
    } else {
      logger.debug("userRC mismatch rc {} mc {}", userRC, userMC);
      match = false;
    }

    registry
        .counter(
            "microcontext.migration.user.mismatch",
            "result",
            Boolean.toString(match),
            "rc",
            Boolean.toString(userRC.isPresent()),
            "mc",
            Boolean.toString(userMC.isPresent()))
        .increment();
    if (userRC.isPresent()) {
      rc.increment();
      return userRC;
    }

    if (userMC.isPresent()) {
      mc.increment();
      return userMC;
    }

    empty.increment();
    return Optional.empty();
  }

  private static Optional<UserContext> getUserFromContext(@Nonnull ContextOrBuilder context) {
    if (context.hasUser() && ContextUtils.isNotDefault(context.getUser())) {
      return Optional.of(context.getUser());
    }
    return Optional.empty();
  }
}
