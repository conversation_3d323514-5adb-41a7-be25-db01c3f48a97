package com.netflix.microcontext.access.server.migration;

import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Timestamps;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.init.resolvers.VisitResolvers;
import com.netflix.microcontext.init.utils.NumberUtils;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Registry;
import com.netflix.spectator.api.Spectator;
import java.util.Optional;
import javax.annotation.Nonnull;
import netflix.context.visit.VisitContext;

@SuppressWarnings("unused")
public class RequestTimeMigration {

  private static final Registry registry = Spectator.globalRegistry();
  private static final Counter mc =
      registry.counter("microcontext.migration.requestTime", "result", "mc");
  private static final Counter rc =
      registry.counter("microcontext.migration.requestTime", "result", "rc");
  private static final Counter empty =
      registry.counter("microcontext.migration.requestTime", "result", "empty");

  public static Optional<Long> getRequestTimeMillis() {
    return getRequestTimeMillis(CurrentMicrocontext.get());
  }

  public static Optional<Long> getRequestTimeMillis(@Nonnull Microcontext microcontext) {
    return getRequestTime(CurrentRequestContext.get(), microcontext).map(Timestamps::toMillis);
  }

  public static Optional<Timestamp> getRequestTime() {
    return getRequestTime(CurrentRequestContext.get(), CurrentMicrocontext.get());
  }

  public static Optional<Timestamp> getRequestTime(@Nonnull Microcontext microcontext) {
    return getRequestTime(CurrentRequestContext.get(), microcontext);
  }

  public static Optional<Timestamp> getRequestTime(
      @Nonnull RequestContext requestContext, @Nonnull Microcontext microcontext) {
    final Optional<Timestamp> requestTimeMC = getRequestTimeMicrocontext(microcontext);
    if (requestTimeMC.isPresent()) {
      mc.increment();
      return requestTimeMC;
    }
    final Optional<Timestamp> requestTimeRC = getRequestTimeRequestContext(requestContext);
    if (requestTimeRC.isPresent()) {
      rc.increment();
      return requestTimeRC;
    }
    empty.increment();
    return Optional.empty();
  }

  private static Optional<Timestamp> getRequestTimeMicrocontext(
      @Nonnull Microcontext microcontext) {
    final VisitContext visit = microcontext.getVisit();
    if (visit.hasRequestTime()) {
      return Optional.of(visit.getRequestTime());
    }
    return Optional.empty();
  }

  private static Optional<Timestamp> getRequestTimeRequestContext(
      @Nonnull RequestContext requestContext) {
    return VisitResolvers.getRequestTime(requestContext)
        .flatMap(NumberUtils::parseLong)
        .map(Timestamps::fromMillis);
  }
}
