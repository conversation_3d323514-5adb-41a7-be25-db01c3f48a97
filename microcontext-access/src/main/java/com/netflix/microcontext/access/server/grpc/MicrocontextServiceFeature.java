package com.netflix.microcontext.access.server.grpc;

import static com.netflix.grpc.ServiceFeaturePriorities.REQUEST_CONTEXT_BRIDGE_SERVICE_FEATURE;

import com.netflix.archaius.api.PropertyRepository;
import com.netflix.grpc.configuration.Configuration;
import com.netflix.grpc.server.DynamicServerInterceptor;
import com.netflix.grpc.service.ServiceFeature;
import com.netflix.grpc.service.ServiceFeatureContext;
import com.netflix.spectator.api.Registry;
import java.util.Optional;

public class MicrocontextServiceFeature implements ServiceFeature {

  private static final Configuration.Key<Boolean> KEY_ENABLED =
      Configuration.Key.of("interceptor.microcontext.enabled", Boolean.class);

  private final Registry registry;
  private final PropertyRepository propertyRepository;

  public MicrocontextServiceFeature(Registry registry, PropertyRepository propertyRepository) {
    this.registry = registry;
    this.propertyRepository = propertyRepository;
  }

  @Override
  public boolean configure(final ServiceFeatureContext context) {
    final Configuration config = context.getConfiguration();
    context.addInterceptor(
        DynamicServerInterceptor.wrap(
            config,
            "microcontext",
            () ->
                config.getProperty(KEY_ENABLED).orElse(true)
                    ? Optional.of(new MicrocontextServerInterceptor(registry, propertyRepository))
                    : Optional.empty()));
    return true;
  }

  @Override
  public boolean isGlobal() {
    return true;
  }

  @Override
  public int getPriority() {
    // needs to run after the request context bridge
    return REQUEST_CONTEXT_BRIDGE_SERVICE_FEATURE + 3;
  }

  @Override
  public String toString() {
    return "MicrocontextServerFeature [priority=" + getPriority() + "]";
  }
}
