package com.netflix.microcontext.access.server.base;

import com.google.inject.AbstractModule;
import com.netflix.microcontext.access.MicrocontextManager;
import com.netflix.microcontext.access.server.CurrentMicrocontextManager;

public class MicrocontextManagerModule extends AbstractModule {

  @Override
  protected void configure() {
    bind(MicrocontextManager.class).to(CurrentMicrocontextManager.class);
  }

  @Override
  public boolean equals(final Object obj) {
    return obj != null && getClass().equals(obj.getClass());
  }

  @Override
  public int hashCode() {
    return getClass().hashCode();
  }
}
