package com.netflix.microcontext.access.server.resolvers;

import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.ImmutableListMultimap.Builder;
import com.google.common.collect.ListMultimap;
import com.netflix.microcontext.init.params.ParamResolver;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nonnull;

public class RequestQueryResolver implements ParamResolver {

  private final ListMultimap<String, String> params;

  public static ParamResolver of(Map<String, String[]> params) {
    return new RequestQueryResolver(params);
  }

  private RequestQueryResolver(Map<String, String[]> params) {
    this.params = paramsListLowerCase(params);
  }

  @Override
  public Optional<String> get(@Nonnull String name) {
    final List<String> all = params.get(key(name));
    return all == null || all.isEmpty() ? Optional.empty() : Optional.of(all.get(0));
  }

  @Nonnull
  @Override
  public List<String> getAll(@Nonnull String name) {
    final List<String> list = params.get(key(name));
    return list == null ? Collections.emptyList() : list;
  }

  @Override
  public boolean contains(@Nonnull String name) {
    return params.containsKey(key(name));
  }

  private static String key(String name) {
    return name.toLowerCase();
  }

  private static ListMultimap<String, String> paramsListLowerCase(Map<String, String[]> params) {
    final Builder<String, String> builder = ImmutableListMultimap.builder();
    for (Map.Entry<String, String[]> param : params.entrySet()) {
      final String key = key(param.getKey());
      for (String value : param.getValue()) {
        builder.put(key, value);
      }
    }
    return builder.build();
  }
}
