package com.netflix.microcontext.access.server.grpc;

import com.netflix.archaius.api.PropertyRepository;
import com.netflix.microcontext.access.MicrocontextManager;
import com.netflix.microcontext.access.server.CurrentMicrocontextManager;
import com.netflix.spectator.api.Registry;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
@ConditionalOnProperty(name = "microcontext.configuration.enabled", matchIfMissing = true)
public class MicrocontextAutoConfiguration {

  @Bean
  @ConditionalOnMissingBean
  public MicrocontextServiceFeature microcontext_accessMicrocontextServiceFeature(
      Registry registry, PropertyRepository propertyRepository) {
    return new MicrocontextServiceFeature(registry, propertyRepository);
  }

  @Bean
  @ConditionalOnMissingBean
  public MicrocontextManager microcontext_accessMicrocontextManager() {
    return new CurrentMicrocontextManager();
  }
}
