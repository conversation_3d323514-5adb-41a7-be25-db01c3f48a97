package com.netflix.microcontext.access.server;

import com.netflix.microcontext.access.server.migration.UserMigration;
import com.netflix.microcontext.init.resolvers.AuthResolvers;
import com.netflix.microcontext.init.resolvers.GeoResolvers;
import com.netflix.server.context.CurrentRequestContext;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import javax.annotation.Nullable;
import netflix.context.Context;
import netflix.context.auth.AuthContext;
import netflix.context.geo.GeoContext;
import netflix.context.user.UserContext;

public class ContextCache {

  private final AtomicReference<AuthContext> authContext = new AtomicReference<>();
  private final AtomicReference<GeoContext> geoContext = new AtomicReference<>();
  private final AtomicReference<UserContext> userContext = new AtomicReference<>();

  ContextCache() {}

  AuthContext getAuthContext() {
    return getAuthContextOptional().orElse(AuthContext.getDefaultInstance());
  }

  Optional<AuthContext> getAuthContextOptional() {
    final AuthContext current = authContext.get();
    if (current != null) {
      return ContextUtils.isDefault(current) ? Optional.empty() : Optional.of(current);
    }
    // could end up resolving multiple times but should be relatively low cost
    final Optional<AuthContext> resolved = AuthResolvers.resolve();
    if (resolved.isPresent()) {
      authContext.set(resolved.get());
    } else {
      authContext.set(AuthContext.getDefaultInstance());
    }
    return resolved;
  }

  GeoContext getGeoContext() {
    final GeoContext current = geoContext.get();
    if (current != null) {
      return current;
    }
    final GeoContext resolved =
        GeoResolvers.requestContext(CurrentRequestContext.get())
            .orElse(GeoContext.getDefaultInstance());
    geoContext.set(resolved);
    return resolved;
  }

  @Nullable
  public UserContext getUserContextCache() {
    return userContext.get();
  }

  public UserContext getUserContext(Context context) {
    final UserContext current = getUserContextCache();
    if (current != null) {
      return current;
    }
    final UserContext resolved =
        UserMigration.getUser(context).orElse(UserContext.getDefaultInstance());
    setUserContext(resolved);
    return resolved;
  }

  public void setUserContext(UserContext userContext) {
    this.userContext.set(userContext);
  }

  void clearAuthContext() {
    authContext.set(null);
  }

  void clearGeoContext() {
    geoContext.set(null);
  }

  void clearUserContext() {
    userContext.set(null);
  }
}
