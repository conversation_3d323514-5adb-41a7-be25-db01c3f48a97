package com.netflix.microcontext.access.server.migration;

import com.netflix.type.protogen.BasicTypes.DeviceType;
import java.util.Optional;
import javax.annotation.Nullable;
import netflix.context.ContextOrBuilder;
import netflix.context.auth.AuthContext;

public class DeviceMigration {

  public static Optional<String> esn(ContextOrBuilder context) {
    return context.getDevice().getOptionalEsn();
  }

  @SuppressWarnings("unused")
  @Deprecated
  public static Optional<String> esn(ContextOrBuilder context, @Nullable AuthContext authContext) {
    return esn(context);
  }

  @SuppressWarnings("unused")
  @Deprecated
  public static Optional<DeviceType> deviceType(
      ContextOrBuilder context, @Nullable AuthContext authContext) {
    return deviceType(context);
  }

  public static Optional<DeviceType> deviceType(ContextOrBuilder context) {
    if (context.getDevice().hasType()) {
      return Optional.of(context.getDevice().getType());
    }
    return Optional.empty();
  }
}
