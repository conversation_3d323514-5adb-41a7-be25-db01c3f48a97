package com.netflix.microcontext.access;

public interface MicrocontextManager {

  /**
   * @return the current microcontext
   */
  Microcontext getContext();

  /**
   * Atomically sets the microcontext. see {@link
   * java.util.concurrent.atomic.AtomicReference#compareAndSet(Object, Object)}
   *
   * @param expected value to override
   * @param microcontext to set
   * @return true if the CAS succeeded, false indicates the expected value did not match the current
   *     value
   */
  boolean compareAndSetContext(Microcontext expected, Microcontext microcontext);
}
