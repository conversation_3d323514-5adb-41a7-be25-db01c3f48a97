package com.netflix.microcontext.access.server.utils;

import com.netflix.microcontext.access.server.CurrentMicrocontext;
import java.nio.charset.StandardCharsets;
import javax.annotation.Nonnull;
import org.apache.commons.codec.digest.DigestUtils;

public class Buckets {

  /**
   * @return a value between 1 and 100, 0 if no esn present
   */
  public static int getESNBucket() {
    return getESNBucket(100);
  }

  public static int getESNBucket(@Nonnull String esn) {
    return getESNBucket(esn, 100);
  }

  public static int getESNBucket(@Nonnull String esn, int bucketSize) {
    if (esn.isEmpty()) {
      return 0;
    }
    int result = 0;
    byte[] baDigest = DigestUtils.sha1(esn.getBytes(StandardCharsets.ISO_8859_1));
    for (byte aBaDigest : baDigest) {
      result += (aBaDigest & 0xff) + 0x100;
    }
    return Math.abs(result % bucketSize) + 1;
  }

  /**
   * @return a value between 1 and the bucketSize, 0 if no esn present
   */
  public static int getESNBucket(int bucketSize) {
    return CurrentMicrocontext.get().getEsn().map(esn -> getESNBucket(esn, bucketSize)).orElse(0);
  }
}
