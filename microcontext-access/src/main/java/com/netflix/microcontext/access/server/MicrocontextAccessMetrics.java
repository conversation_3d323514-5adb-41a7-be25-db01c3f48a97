package com.netflix.microcontext.access.server;

import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Registry;

public class MicrocontextAccessMetrics {

  public static final String MICROCONTEXT_ACCESS = "microcontext.access";
  public static final String CONTEXT = "context";

  private final Counter requestId;
  private final Counter user;
  private final Counter geo;
  private final Counter proto;
  private final Counter auth;

  public MicrocontextAccessMetrics(Registry registry) {
    this.user = registry.counter(MICROCONTEXT_ACCESS, CONTEXT, "user");
    this.geo = registry.counter(MICROCONTEXT_ACCESS, CONTEXT, "geo");
    this.proto = registry.counter(MICROCONTEXT_ACCESS, CONTEXT, "proto");
    this.auth = registry.counter(MICROCONTEXT_ACCESS, CONTEXT, "auth");
    this.requestId = registry.counter(MICROCONTEXT_ACCESS, CONTEXT, "requestId");
  }

  public void requestId() {
    requestId.increment();
  }

  public void user() {
    user.increment();
  }

  public void geo() {
    geo.increment();
  }

  public void proto() {
    proto.increment();
  }

  public void auth() {
    auth.increment();
  }
}
