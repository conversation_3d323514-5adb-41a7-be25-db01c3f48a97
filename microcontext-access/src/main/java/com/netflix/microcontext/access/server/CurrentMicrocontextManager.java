package com.netflix.microcontext.access.server;

import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.MicrocontextManager;

public class CurrentMicrocontextManager implements MicrocontextManager {

  @Override
  public Microcontext getContext() {
    return CurrentMicrocontext.get();
  }

  @Override
  public boolean compareAndSetContext(Microcontext expected, Microcontext microcontext) {
    return CurrentMicrocontext.compareAndSet(expected, microcontext);
  }
}
