package com.netflix.microcontext.access.server.grpc;

import static com.netflix.microcontext.access.server.grpc.MicrocontextHeaders.MICROCONTEXT;
import static com.netflix.microcontext.access.server.grpc.MicrocontextHeaders.MICROCONTEXT_BINARY;
import static com.netflix.passport.introspect.PassportIdentityFactory.REQUEST_CONTEXT_PASSPORT_CONTEXT_NAME;

import com.google.protobuf.Descriptors.FieldDescriptor;
import com.google.protobuf.Descriptors.FieldDescriptor.Type;
import com.google.protobuf.GeneratedMessageV3;
import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.access.server.CurrentContextCache;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.MicrocontextInternalUtils;
import com.netflix.microcontext.access.server.resolvers.MetadataHeaderResolver;
import com.netflix.microcontext.access.server.utils.RequestPriorityMetrics;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.resolvers.GeoResolvers;
import com.netflix.microcontext.init.serializers.Serializers;
import com.netflix.microcontext.init.utils.Passports;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Registry;
import com.netflix.spectator.api.Tag;
import com.netflix.spectator.api.histogram.PercentileTimer;
import io.grpc.ForwardingServerCallListener;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCall.Listener;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.Nonnull;
import netflix.context.Context;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MicrocontextServerInterceptor implements ServerInterceptor {

  private static final Logger logger = LoggerFactory.getLogger(MicrocontextServerInterceptor.class);
  private static final Map<Class<? extends GeneratedMessageV3>, Optional<FieldDescriptor>>
      contextDescriptorCache = new ConcurrentHashMap<>();

  private final Id headerId;
  private final Id requestContextId;
  private final Id defaultId;
  private final Counter contextCounter;
  private final Counter errorCounter;
  private final Counter geoHeader;
  private final Counter passportHeaderMismatch;
  private final Counter clearUserCounter;
  private final Registry registry;
  private final PercentileTimer timer;
  private final RequestPriorityMetrics requestPriorityMetrics;
  private final Property<Boolean> fixPassport;
  private final Property<Boolean> clearUser;

  public MicrocontextServerInterceptor(Registry registry, PropertyRepository propertyRepository) {
    this(registry, new RequestPriorityMetrics(registry), propertyRepository);
  }

  MicrocontextServerInterceptor(
      Registry registry,
      RequestPriorityMetrics requestPriorityMetrics,
      PropertyRepository propertyRepository) {
    this.registry = registry;
    this.requestPriorityMetrics = requestPriorityMetrics;
    headerId = registry.createId("microcontext.interceptor.call", "source", "header");
    requestContextId =
        registry.createId("microcontext.interceptor.call", "source", "requestContext");
    defaultId = registry.createId("microcontext.interceptor.call", "source", "default");
    contextCounter = registry.counter("microcontext.interceptor.call", "source", "nocontext");
    errorCounter = registry.counter("microcontext.interceptor.call", "source", "error");
    passportHeaderMismatch = registry.counter("microcontext.server.interceptor.passport.fix");
    clearUserCounter = registry.counter("microcontext.server.interceptor.clearuser");
    timer =
        PercentileTimer.builder(registry)
            .withName("microcontext.interceptor.call.duration")
            .build();
    geoHeader = registry.counter("microcontext.interceptor.geo.header");
    fixPassport =
        propertyRepository
            .get("microcontext.server.interceptor.fix.passport", Boolean.class)
            .orElse(true);
    clearUser =
        propertyRepository
            .get("microcontext.server.interceptor.clear.user", Boolean.class)
            .orElse(true);
  }

  @Override
  public <ReqT, RespT> Listener<ReqT> interceptCall(
      ServerCall<ReqT, RespT> serverCall,
      Metadata metadata,
      ServerCallHandler<ReqT, RespT> serverCallHandler) {
    return new ForwardingServerCallListener.SimpleForwardingServerCallListener<ReqT>(
        serverCallHandler.startCall(serverCall, metadata)) {
      @Override
      public void onMessage(ReqT message) {
        timer.recordRunnable(
            () -> {
              try {
                if (BindingContexts.isInContext()) {
                  if (fixPassport.get()) {
                    fixPassport(MetadataHeaderResolver.of(metadata), passportHeaderMismatch);
                  }

                  final Context value;
                  Optional<Context> header = fromHeader(metadata);
                  final Id id;
                  final List<Tag> tagList = new ArrayList<>();
                  // get the value from header, request context or default
                  if (header.isPresent()) {
                    id = headerId;
                    value = header.get();

                    // if the header contains geo and rc does not, set geodata in rc
                    final RequestContext requestContext = CurrentRequestContext.get();
                    if (!GeoResolvers.hasGeo(requestContext) && value.hasGeo()) {
                      geoHeader.increment();
                      GeoResolvers.setGeo(value.getGeo());
                    }
                  } else {
                    Optional<Context> requestContext = CurrentMicrocontext.requestContext();
                    if (requestContext.isPresent()) {
                      id = requestContextId;
                      value = requestContext.get();
                    } else {
                      id = defaultId;
                      value = Context.getDefaultInstance();
                    }
                  }

                  final Context possiblyOverridden;
                  // finds an override Context in the request message
                  Optional<Context> override = getContextOverride(message);
                  // merge the override if present
                  if (override.isPresent()) {
                    tagList.add(Tag.of("merge", "true"));
                    possiblyOverridden = override.get().toBuilder().mergeFrom(value).build();
                  } else {
                    tagList.add(Tag.of("merge", "false"));
                    possiblyOverridden = value;
                  }

                  /*
                    If the user context is passed in, set it in the current request cache and
                    optionally remove it from the provided context, this default to true (clear) but
                    can be possiblyOverridden with a property
                  */
                  final Context finalContext;
                  if (possiblyOverridden.hasUser()) {
                    clearUserCounter.increment();
                    CurrentContextCache.get().setUserContext(possiblyOverridden.getUser());
                    if (clearUser.get()) {
                      finalContext = possiblyOverridden.toBuilder().clearUser().build();
                    } else {
                      finalContext = possiblyOverridden;
                    }
                  } else {
                    finalContext = possiblyOverridden;
                  }

                  MicrocontextInternalUtils.internalSet(finalContext);
                  registry.counter(id.withTags(tagList)).increment();
                  requestPriorityMetrics.emitPriorityMetrics(possiblyOverridden);
                } else {
                  contextCounter.increment();
                }
              } catch (Throwable t) {
                errorCounter.increment();
              }
            });
        super.onMessage(message);
      }

      @Override
      public void onCancel() {
        super.onCancel();
      }
    };
  }

  private static void fixPassport(HeaderResolver resolver, Counter passportFix) {
    final Optional<String> headerPassport = Passports.passport(resolver);
    if (headerPassport.isPresent()) {
      final RequestContext currentRequestContext = CurrentRequestContext.get();
      final Optional<String> requestContextPassport = Passports.passport(currentRequestContext);
      if (!Objects.equals(headerPassport, requestContextPassport)) {
        passportFix.increment();
        currentRequestContext.addContext(
            REQUEST_CONTEXT_PASSPORT_CONTEXT_NAME, headerPassport.get());
      }
    }
  }

  private static Optional<Context> fromHeader(Metadata metadata) {
    byte[] microcontextBinary = metadata.get(MICROCONTEXT_BINARY);
    if (microcontextBinary != null) {
      return Serializers.fromBytes(microcontextBinary);
    }
    String microcontext = metadata.get(MICROCONTEXT);
    if (microcontext != null) {
      return Serializers.fromString(microcontext);
    }
    return Optional.empty();
  }

  public static <ReqT> Optional<Context> getContextOverride(ReqT message) {
    if (message instanceof GeneratedMessageV3) {
      return getContextFromMessage((GeneratedMessageV3) message);
    }
    return Optional.empty();
  }

  private static Optional<Context> getContextFromMessage(@Nonnull GeneratedMessageV3 message) {
    return getFieldDescriptorFromMessage(message)
        .map(message::getField)
        .map(o -> (Context) o)
        .flatMap(c -> c == Context.getDefaultInstance() ? Optional.empty() : Optional.of(c));
  }

  static Optional<FieldDescriptor> getFieldDescriptorFromMessage(
      @Nonnull GeneratedMessageV3 message) {
    Optional<FieldDescriptor> fieldDescriptor = contextDescriptorCache.get(message.getClass());
    // null optional means cache miss
    if (fieldDescriptor == null) {
      fieldDescriptor = computeFieldDescriptor(message);
      contextDescriptorCache.put(message.getClass(), fieldDescriptor);
    }
    return fieldDescriptor;
  }

  private static Optional<FieldDescriptor> computeFieldDescriptor(
      @Nonnull GeneratedMessageV3 message) {
    try {
      for (FieldDescriptor fd : message.getDescriptorForType().getFields()) {
        if (isMessage(fd) && isContext(fd)) {
          return Optional.of(fd);
        }
      }
    } catch (Throwable t) {
      logger.error("Could not get context", t);
    }
    return Optional.empty();
  }

  private static boolean isMessage(@Nonnull FieldDescriptor fd) {
    return fd.getType() == Type.MESSAGE;
  }

  private static boolean isContext(@Nonnull FieldDescriptor fd) {
    return fd.getMessageType() == Context.getDescriptor();
  }
}
