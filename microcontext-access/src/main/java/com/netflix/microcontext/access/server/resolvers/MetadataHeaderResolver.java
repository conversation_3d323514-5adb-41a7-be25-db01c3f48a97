package com.netflix.microcontext.access.server.resolvers;

import com.netflix.microcontext.init.headers.HeaderResolver;
import io.grpc.Metadata;
import io.grpc.Metadata.Key;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nonnull;

public class MetadataHeaderResolver implements HeaderResolver {

  private final Metadata metadata;

  public static HeaderResolver of(Metadata metadata) {
    return new MetadataHeaderResolver(metadata);
  }

  public MetadataHeaderResolver(Metadata metadata) {
    this.metadata = metadata;
  }

  @Override
  public Optional<String> getFirst(@Nonnull List<String> names) {
    for (String name : names) {
      final String value = metadata.get(key(name));
      if (value != null) {
        return Optional.of(value);
      }
    }
    return Optional.empty();
  }

  @Override
  public Optional<String> get(@Nonnull String name) {
    return Optional.ofNullable(metadata.get(key(name)));
  }

  @Nonnull
  @Override
  public List<String> getAll(@Nonnull String name) {
    Iterable<String> all = metadata.getAll(key(name));
    if (all == null) {
      return Collections.emptyList();
    }

    List<String> l = new ArrayList<>();
    all.forEach(l::add);
    return l;
  }

  @Override
  public boolean contains(@Nonnull String name) {
    return metadata.containsKey(key(name));
  }

  static Key<String> key(String name) {
    // TODO binary support?
    return Key.of(name, Metadata.ASCII_STRING_MARSHALLER);
  }
}
