package com.netflix.microcontext.access.server.utils;

import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Registry;
import netflix.context.Context;
import netflix.context.visit.RequestPriority;
import netflix.context.visit.VisitContext;

/**
 * Responsible for publishing metrics related to request priority
 *
 * <AUTHOR>
 * @since 12/6/23
 */
public class RequestPriorityMetrics {

  private final Registry registry;
  private final Id id;

  public RequestPriorityMetrics(Registry registry) {
    this.registry = registry;
    this.id = registry.createId("microcontext.request.priority");
  }

  public void emitPriorityMetrics(Context value) {
    if (value == null || !value.hasVisit()) {
      return;
    }

    emitPriorityMetrics(value.getVisit());
  }

  public void emitPriorityMetrics(VisitContext visit) {
    if (visit == null || !visit.hasPriority()) {
      return;
    }

    RequestPriority priority = visit.getPriority();
    registry.counter(id.withTag("priority", getPriorityMetric(priority.getPriority()))).increment();
  }

  static String getPriorityMetric(int priority) {
    if (priority <= 0) {
      return "missing";
    }

    int bucketed = priority / 10;
    switch (bucketed) {
      case 0:
        return "1-9";
      case 1:
        return "10-19";
      case 2:
        return "20-29";
      case 3:
        return "30-39";
      case 4:
        return "40-49";
      case 5:
        return "50-59";
      case 6:
        return "60-69";
      case 7:
        return "70-79";
      case 8:
        return "80-89";
      case 9:
        return "90-99";
      default:
        return "unknown";
    }
  }
}
