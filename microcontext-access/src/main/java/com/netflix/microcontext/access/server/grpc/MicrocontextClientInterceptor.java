package com.netflix.microcontext.access.server.grpc;

import static com.netflix.microcontext.access.server.grpc.MicrocontextHeaders.MICROCONTEXT_BINARY;

import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.init.serializers.Serializers;
import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientInterceptor;
import io.grpc.ForwardingClientCall;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;

@SuppressWarnings("unused")
public class MicrocontextClientInterceptor implements ClientInterceptor {

  public MicrocontextClientInterceptor() {}

  @Override
  public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(
      MethodDescriptor<ReqT, RespT> methodDescriptor, CallOptions callOptions, Channel channel) {
    return new ForwardingClientCall.SimpleForwardingClientCall<ReqT, RespT>(
        channel.newCall(methodDescriptor, callOptions)) {
      @Override
      public void start(Listener<RespT> responseListener, Metadata headers) {
        headers.put(MICROCONTEXT_BINARY, Serializers.toBytes(CurrentMicrocontext.get().toProto()));
        super.start(responseListener, headers);
      }
    };
  }
}
