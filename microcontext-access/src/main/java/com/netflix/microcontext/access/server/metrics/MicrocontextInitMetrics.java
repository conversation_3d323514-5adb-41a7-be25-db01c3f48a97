package com.netflix.microcontext.access.server.metrics;

import com.netflix.microcontext.access.server.ContextUtils;
import com.netflix.microcontext.access.server.utils.RequestPriorityMetrics;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Registry;
import com.netflix.type.protogen.BasicTypes.Country;
import com.netflix.type.protogen.BasicTypes.Locale;
import java.util.List;
import javax.annotation.Nullable;
import netflix.context.Context;
import netflix.context.client.ClientContext;
import netflix.context.device.DeviceContext;
import netflix.context.user.UserContext;
import netflix.context.visit.VisitContext;

public class MicrocontextInitMetrics {

  private final Counter missingAll;
  private final Counter missingClient;
  private final Counter missingCountry;
  private final Counter missingDevice;
  private final Counter missingLocale;
  private final Counter missingUser;
  private final Counter missingVisit;
  private final Counter missingAppVersion;
  private final RequestPriorityMetrics requestPriorityMetrics;

  public MicrocontextInitMetrics(Registry registry) {
    Id error = registry.createId("microcontext.error");
    String missing = "missing_contexts";
    this.missingAll = registry.counter(error.withTag(missing, "all"));
    this.missingClient = registry.counter(error.withTag(missing, "client"));
    this.missingCountry = registry.counter(error.withTag(missing, "country"));
    this.missingDevice = registry.counter(error.withTag(missing, "device"));
    this.missingLocale = registry.counter(error.withTag(missing, "locale"));
    this.missingUser = registry.counter(error.withTag(missing, "user"));
    this.missingVisit = registry.counter(error.withTag(missing, "visit"));
    this.missingAppVersion = registry.counter(error.withTag(missing, "appVersion"));
    this.requestPriorityMetrics = new RequestPriorityMetrics(registry);
  }

  public void emit(@Nullable Context context) {
    if (context == null || ContextUtils.isDefault(context)) {
      missingAll.increment();
      return;
    }

    ClientContext client = context.getClient();
    if (!client.isInitialized() || ClientContext.getDefaultInstance().equals(client)) {
      missingClient.increment();
    }

    if (!client.hasAppVersion()) {
      missingAppVersion.increment();
    }

    Country country = context.getCountry();
    if (!country.isInitialized() || Country.getDefaultInstance().equals(country)) {
      missingCountry.increment();
    }

    DeviceContext device = context.getDevice();
    if (!device.isInitialized() || DeviceContext.getDefaultInstance().equals(device)) {
      missingDevice.increment();
    }

    final List<Locale> localesList = context.getLocalesList();
    if (localesList.isEmpty()) {
      missingLocale.increment();
    }

    UserContext user = context.getUser();
    if (!user.isInitialized() || UserContext.getDefaultInstance().equals(user)) {
      missingUser.increment();
    }

    VisitContext visit = context.getVisit();
    if (!visit.isInitialized() || VisitContext.getDefaultInstance().equals(visit)) {
      missingVisit.increment();
    }

    requestPriorityMetrics.emitPriorityMetrics(context.getVisit());
  }
}
