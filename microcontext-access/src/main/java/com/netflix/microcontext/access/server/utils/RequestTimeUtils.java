package com.netflix.microcontext.access.server.utils;

import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Timestamps;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.migration.RequestTimeMigration;
import java.util.Optional;
import javax.annotation.Nonnull;
import netflix.context.visit.VisitContext;

@SuppressWarnings("unused")
public class RequestTimeUtils {

  public static Optional<Long> currentRequestTimeMillis(@Nonnull Microcontext microcontext) {
    return currentRequestTime(microcontext).map(Timestamps::toMillis);
  }

  public static Optional<Timestamp> currentRequestTime(@Nonnull Microcontext microcontext) {
    final Optional<Timestamp> requestTime = RequestTimeMigration.getRequestTime(microcontext);
    if (requestTime.isPresent()) {
      return requestTime;
    }
    final VisitContext visit = microcontext.getVisit();
    if (visit.hasEdgeTime()) {
      return Optional.of(visit.getEdgeTime());
    }
    return Optional.empty();
  }
}
