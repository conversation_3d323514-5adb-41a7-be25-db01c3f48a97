package com.netflix.microcontext.access.server.resolvers;

import io.grpc.Metadata;
import io.grpc.Metadata.Key;
import java.util.Collections;
import java.util.concurrent.TimeUnit;
import org.openjdk.jmh.annotations.Benchmark;
import org.openjdk.jmh.annotations.BenchmarkMode;
import org.openjdk.jmh.annotations.Fork;
import org.openjdk.jmh.annotations.Measurement;
import org.openjdk.jmh.annotations.Mode;
import org.openjdk.jmh.annotations.OutputTimeUnit;
import org.openjdk.jmh.annotations.Threads;
import org.openjdk.jmh.annotations.Warmup;

@Warmup(iterations = 2, time = 5)
@Threads(8)
@BenchmarkMode(Mode.Throughput)
@OutputTimeUnit(TimeUnit.MILLISECONDS)
@Measurement(iterations = 2, time = 10)
@Fork(1)
public class HeaderResolverBenchmark {

  private static final MetadataHeaderResolver resolver;

  static {
    Metadata metadata = new Metadata();
    metadata.put(Key.of("foo", Metadata.ASCII_STRING_MARSHALLER), "bar");
    resolver = new MetadataHeaderResolver(metadata);
  }

  @Benchmark
  public boolean simpleGet() {
    return resolver.get("foo").isPresent();
  }

  @Benchmark
  public boolean getFirst() {
    return resolver.getFirst(Collections.singletonList("foo")).isPresent();
  }
}
