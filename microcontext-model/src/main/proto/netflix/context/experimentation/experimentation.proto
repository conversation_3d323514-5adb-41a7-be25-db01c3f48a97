syntax = "proto3";

package netflix.context.experimentation;

option java_multiple_files = true;
option java_outer_classname = "ExperimentationProtos";
option java_package = "netflix.context.experimentation";

message ExperimentationContext {
  reserved 1;
  Allocations account_allocations = 2;
  Allocations vdid_allocations = 3;
}

message Allocations {
  reserved 1;
  map<int32, int32> test_to_cell = 2;
}