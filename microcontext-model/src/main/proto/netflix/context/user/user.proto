syntax = "proto3";

package netflix.context.user;

import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "netflix/basicTypes/basic_types.proto";
import "subscriberservicetypes.proto";

option java_multiple_files = true;
option java_outer_classname = "UserProtos";
option java_package = "netflix.context.user";

message UserContext {
  reserved 2;
  User current_user = 1;
}

message User {
  reserved 19;
  int64 id = 1;
  string guid = 2;
  google.protobuf.Int64Value owner_id = 3;
  google.protobuf.StringValue owner_guid = 4;
  netflix.basicTypes.Country registration_country = 5;
  netflix.basicTypes.Country signup_country = 6;
  netflix.basicTypes.Country recent_viewing_country = 7;
  google.protobuf.StringValue primary_language = 8;
  google.protobuf.Int32Value maturity_level = 9;
  google.protobuf.StringValue profile_name = 10;
  google.protobuf.Int64Value plan_id = 11;
  bool current_member = 12;
  bool active_or_hold = 13;
  com.netflix.subscriber.types.Membership.Status membership_status = 14;
  com.netflix.subscriber.types.Experience.Type experience_type = 15;
  bool is_tester = 16 [deprecated = true];
  google.protobuf.Timestamp profile_creation_time = 17;
  bool is_autoplay_enabled = 18;
  bool has_profile_lock_pin_enabled = 20;
  bool is_pin_enabled = 21;
  bool fallback = 22;
  google.protobuf.StringValue ucid = 23;
  com.netflix.subscriber.types.SOpType.Type sop_type = 24;
}