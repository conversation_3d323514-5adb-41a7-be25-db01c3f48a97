syntax = "proto3";

package netflix.context.auth;

import "google/protobuf/wrappers.proto";
import "netflix/basicTypes/basic_types.proto";
import "appregistry-models.proto";

option java_multiple_files = true;
option java_outer_classname = "AuthProtos";
option java_package = "netflix.context.auth";

message AuthContext {
  Auth current_auth = 1;
  google.protobuf.StringValue visitor_device_id = 2;
  google.protobuf.StringValue esn = 3;
  netflix.basicTypes.DeviceType device_type = 4;
  com.netflix.appregistry.AppType app_type = 5;
  google.protobuf.StringValue app_id = 6;
}

message Auth {
  int64 customer_id = 1;
  string customer_guid = 2;
  google.protobuf.Int64Value account_owner_id = 3;
  google.protobuf.StringValue account_owner_guid = 4;
}