syntax = "proto3";

package netflix.context.device.supportlevel;

option java_multiple_files = true;
option java_outer_classname = "DeviceSupportLevelProtos";
option java_package = "netflix.context.device.supportlevel";

// https://dcms.netflixpartners.com/#/properties/view/SUPPORT_LEVEL
enum DeviceSupportLevel {
  UNSPECIFIED = 0;
  STANDARD = 1; // Full support, Engineering teams execute root cause analysis and will fix/rollback as appropriate
  MEASURED = 2; // Time-boxed recovery effort except for catastrophic failures
  DNR = 3; // Do Not Recover. Minimal triage and effort, catastrophic failures likely result in forced retirement
  UNSUPPORTED = 4; // The device is no longer supported, and no effort will be made to restore functionality. Likely, the device was intentionally rendered unfunctional.
}