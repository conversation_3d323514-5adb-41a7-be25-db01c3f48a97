syntax = "proto3";

package netflix.context.device;

import "dcms-models.proto";
import "google/protobuf/wrappers.proto";
import "netflix/basicTypes/basic_types.proto";
import "netflix/context/device/supportlevel/supportlevel.proto";

option java_multiple_files = true;
option java_outer_classname = "DeviceProtos";
option java_package = "netflix.context.device";

message DeviceContext {
  reserved 2, 3, 5;
  reserved "category", "category_id";
  netflix.basicTypes.DeviceType type = 1;
  google.protobuf.StringValue esn = 4;
  .com.netflix.dcms.DseHardwareMajorCategory hardware_major_category = 6;
  .com.netflix.dcms.DseClientPlatformName client_platform = 7;
  .netflix.context.device.supportlevel.DeviceSupportLevel support_level = 8;
  bool retired = 9;
  google.protobuf.UInt32Value memory_range = 10;
  google.protobuf.StringValue retirement_message = 11;
}