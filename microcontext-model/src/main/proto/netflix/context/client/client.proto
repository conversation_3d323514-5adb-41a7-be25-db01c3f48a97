syntax = "proto3";

package netflix.context.client;

import "google/protobuf/wrappers.proto";
import "netflix/context/client/capabilities.proto";
import "netflix/context/client/category/category.proto";
import "netflix/context/client/flavor/flavor.proto";
import "netflix/context/client/formfactor/formfactor.proto";
import "netflix/context/client/tier/tier.proto";
import "netflix/context/common/common.proto";

option java_multiple_files = true;
option java_outer_classname = "ClientProtos";
option java_package = "netflix.context.client";

message ClientContext {
  reserved 1, 2, 3, 11;
  netflix.context.common.Version app_version = 4;
  netflix.context.common.Version os_version = 5;
  netflix.context.common.Version sdk_version = 6;
  netflix.context.client.CapabilitiesContext capabilities = 7 [deprecated = true];
  netflix.context.client.flavor.ClientFlavor client_flavor = 8;
  netflix.context.client.category.ClientCategory client_category = 9;
  netflix.context.client.formfactor.ClientFormFactor client_form_factor = 10;
  google.protobuf.StringValue os_name = 12;
  netflix.context.client.tier.DeviceTier tier = 13;

  oneof category_details {
    netflix.context.client.category.BrowserCategoryDetails browser_details = 20;
    netflix.context.client.category.AndroidCategoryDetails android_details = 21;
    netflix.context.client.category.TVCategoryDetails tv_details = 22;
  }
}