syntax = "proto3";

package netflix.context.client;

option java_multiple_files = true;
option java_outer_classname = "CapabilitiesProtos";
option java_package = "netflix.context.client";

// capabilities available to the device
message CapabilitiesContext {
  repeated FeatureCapabilityUnion feature_capabilities = 1;
  repeated LocalizationCapabilityUnion localization_capabilities = 2;
  repeated TitleCapabilityUnion title_capabilities = 3;
  repeated InteractiveOriginalUnion interactive_originals = 4;
}

message FeatureCapabilityUnion {
  oneof capability {
    FeatureCapability enum_value = 1;
    string unkown_value = 2;
  }
}

enum FeatureCapability {
  supportsTop10 = 0;
  supportsTop10Kids = 1;
  // This is to support the Studio Branding initiative: https://docs.google.com/document/d/1CsPjFOmrPQfFgcz0e_zBcwpwFjUfACw6jU25WHM76EI/edit?usp=sharing
  // We will be moving away from N-<Content label> branding on Netflix originals and will instead be applying just the Netflix logo.
  // We will also be allowing branding from other studios/networks as well. Eclipse is targeted to roll out in Q3 2025 and we're targeting other platforms for 2026.
  // This flag will be consumed by Evidence services and can be removed once we're fully rolled out.
  supportsStudioBranding = 2;
}

message LocalizationCapabilityUnion {
  oneof capability {
    LocalizationCapability enum_value = 1;
    string unkown_value = 2;
  }
}

enum LocalizationCapability {
  defaultKidsProfile = 0;
}

message TitleCapabilityUnion {
  oneof capability {
    TitleCapability enum_value = 1;
    string unkown_value = 2;
  }
}

enum TitleCapability {
  episodeOrdering = 0;
  seasonOrdering = 1;
  hiddenEpisodeNumbers = 2;
  episodicNewBadge = 3;
  episodicAvailabilityMessage = 4;
  flattenedShow = 5;
  episodeSkipping = 6;
  // Defined by text-evidence, see go/6ukTrd
  SUPPORTS_ALT_SEASON_LABEL = 7;
}

message InteractiveOriginalUnion {
  oneof originals {
    InteractiveOriginal enum_value = 1;
    string unknown_value = 2;
  }
}

// https://stash.corp.netflix.com/projects/graph/repos/nq_video_dgs/browse/src/graphql/context/platforms/ios.ts#9
enum InteractiveOriginal {
  pussinbook = 0;
  buddymaybepile = 1;
  stretchbreakout = 2;
  minecraft = 3;
  bandersnatch = 4;
  bandersnatchPrePlay = 5;
  badgeIconTest = 6;
}