syntax = "proto3";

package netflix.context.client.flavor;

option java_multiple_files = true;
option java_outer_classname = "ClientFlavorProtos";
option java_package = "netflix.context.client.flavor";

/* derived from http://go.netflix.com/clientlist */
enum ClientFlavor {
  reserved 10;
  UNSPECIFIED = 0;
  AKIRA = 1;
  ANDROID = 2;
  ARGO = 3;
  ATV_FUJI = 4;
  ATV_HOPPER = 5;
  DARWIN = 6;
  FAKIRA = 7;
  IOS_LEGACY = 8;
  TV_OTHER = 9;
  WINDOWS_GOTHAM = 11;
  WINDOWS_PX = 12;
  WINDOWS_WILDCAT = 13;
  ECLIPSE = 14;
  ATV_ECLIPSE = 15;
  BUTTERFLY = 16; // native ios
  TREX = 17; // native android
  DET = 18; // DET
}