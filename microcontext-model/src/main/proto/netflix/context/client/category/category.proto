syntax = "proto3";

package netflix.context.client.category;

import "google/protobuf/wrappers.proto";
import "netflix/context/common/common.proto";

option java_multiple_files = true;
option java_outer_classname = "ClientCategoryProtos";
option java_package = "netflix.context.client.category";

enum ClientCategory {
  UNSPECIFIED = 0;
  TV = 1;
  ANDROID = 2;
  IOS = 3;
  WEB = 4;
  WIN = 5 [deprecated = true];
  MEDIAROOM = 6 [deprecated = true];
  ATV = 7;
}

message BrowserCategoryDetails {
  string browser_name = 1;
  string browser_version = 2;
}

message AndroidCategoryDetails {
  optional string installer_source = 1;
}

message TVCategoryDetails {
  netflix.context.common.Version nrd_app_version = 1;
  netflix.context.common.Version ui_version = 2;
}
