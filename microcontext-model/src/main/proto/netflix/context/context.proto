syntax = "proto3";

package netflix.context;

import "google/protobuf/any.proto";
import "google/protobuf/wrappers.proto";
import "netflix/basicTypes/basic_types.proto";
import "netflix/context/auth/auth.proto";
import "netflix/context/client/client.proto";
import "netflix/context/device/device.proto";
import "netflix/context/geo/geo.proto";
import "netflix/context/user/user.proto";
import "netflix/context/video/video.proto";
import "netflix/context/visit/visit.proto";

option java_multiple_files = true;
option java_outer_classname = "ContextProtos";
option java_package = "netflix.context";

message Context {
  reserved 16, 23, 50;
  google.protobuf.StringValue request_id = 1;
  netflix.basicTypes.Country country = 2;
  repeated netflix.basicTypes.Locale locales = 3;
  .netflix.context.client.ClientContext client = 15;
  .netflix.context.geo.GeoContext geo = 17 [deprecated = true];
  .netflix.context.user.UserContext user = 18 [deprecated = true];
  .netflix.context.video.VideoContext video = 19;
  .netflix.context.visit.VisitContext visit = 20;
  .netflix.context.device.DeviceContext device = 21;
  .google.protobuf.Any resolved = 40;
  Details details = 51;
}

message Details {
  reserved 1;
  bool fallback = 2;
}