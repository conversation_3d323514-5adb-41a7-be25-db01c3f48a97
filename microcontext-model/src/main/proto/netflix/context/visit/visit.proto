syntax = "proto3";

package netflix.context.visit;

import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "netflix/context/common/common.proto";
import "netflix/context/geo/geo.proto";

option java_multiple_files = true;
option java_outer_classname = "VisitProtos";
option java_package = "netflix.context.visit";

message VisitContext {
  reserved 2, 3, 4, 6;
  Connection connection = 1;
  google.protobuf.Timestamp request_time = 5;
  google.protobuf.Timestamp edge_time = 7;
  RequestPriority priority = 8;
  google.protobuf.StringValue device_memory_level = 9;
  UserAgent user_agent = 10;
  // provided by clients as a header which should override GeoContext.getIanaTimezone()
  google.protobuf.StringValue override_iana_timezone = 11;
  bool load_test = 12;
}

message Connection {
  reserved 1, 2, 3, 4;
  google.protobuf.BoolValue secure_port = 5;
}

enum AppState {
  APP_STATE_UNSPECIFIED = 0;
  APP_STATE_FOREGROUND = 1;
  APP_STATE_BACKGROUND = 2;
  APP_STATE_IDLE = 3;
  APP_STATE_UNKNOWN = 4;
}

enum RequestCategory {
  REQUEST_CATEGORY_UNSPECIFIED = 0;
  REQUEST_CATEGORY_STREAMING = 1;
  REQUEST_CATEGORY_LIVE = 2;
  REQUEST_CATEGORY_UNKNOWN = 3;
  REQUEST_CATEGORY_GAMES = 4 [deprecated = true];
}

// The priority of a request as calculated by Zuul using the device to zuul protocol
// RequestPriority can be used to make intelligent load shedding decisions
message RequestPriority {
  // a computed priority (1-100) for a request, with 1 representing highest priority and 100 lowest (or unknown priority)
  int32 priority = 1;
  // the attempt number as specified by a device
  google.protobuf.Int32Value attempt = 2;
  // the category of an api request. Unlabeled API calls have the category REQUEST_CATEGORY_UNKNOWN
  RequestCategory category = 3;
  // The state the app was in when making an api call
  AppState app_state = 4;
}

message UserAgent {
  string value = 1;
  WebClientDetails client_details = 2; // only populated for zuul web requests
}

message WebClientDetails {
  UserAgentDetails user_agent_details = 1;
  OSDetails os_details = 2;
  DeviceDetails device_details = 3;
}

message UserAgentDetails {
  reserved 2;
  google.protobuf.StringValue family = 1;
  google.protobuf.StringValue version = 3 [deprecated = true];
  SemVer sem_ver = 4;
}

message OSDetails {
  reserved 2;
  google.protobuf.StringValue family = 1;
  google.protobuf.StringValue version = 3 [deprecated = true];
  SemVer sem_ver = 4;
}

message DeviceDetails {
  google.protobuf.StringValue family = 1;
}

message SemVer {
  sint32 major = 1;
  sint32 minor = 2;
  sint32 patch = 3;
}
