syntax = "proto3";

package netflix.context.geo;

import "com/netflix/proto/options/docs/api-docs.proto";
import "google/protobuf/wrappers.proto";
import "google/type/latlng.proto";
import "netflix/basicTypes/basic_types.proto";

option java_multiple_files = true;
option java_outer_classname = "GeoProtos";
option java_package = "netflix.context.geo";

message GeoContext {
  reserved 7, 15, 16;
  netflix.basicTypes.Country country = 1 [(com.netflix.proto.options.docs.field) = {description: "The ISO-3166 country code for the country where the request IP addressmaps to according to the service provider"}];
  netflix.basicTypes.Country real_country = 2 [(com.netflix.proto.options.docs.field) = {description: "Only populated if an override is applied"}];
  google.protobuf.StringValue region_code = 3 [(com.netflix.proto.options.docs.field) = {description: "ISO-3166, two-letter code for the state, province, or region"}];
  google.protobuf.StringValue city = 4 [(com.netflix.proto.options.docs.field) = {description: "the city (within a 50-mile radius)"}];
  google.protobuf.StringValue zip = 5 [(com.netflix.proto.options.docs.field) = {description: "the zip code"}];
  IpAddress ip_address = 8 [(com.netflix.proto.options.docs.field) = {description: "the IP address for which this object contains data"}];
  IpAddress real_ip_address = 9 [(com.netflix.proto.options.docs.field) = {description: "Only populated if ip_address overridden"}];
  google.protobuf.StringValue asn = 10 [(com.netflix.proto.options.docs.field) = {description: "The autonomous system number of the request.  On the internet, an ISP officially registers an ASN as a form of identification.  These numbers are written either as simple integers, or in the form x.y, where x and y are 16-bit numbers."}];
  optional bool blocked_proxy = 11 [(com.netflix.proto.options.docs.field) = {description: "Indicates this is a blocked proxy"}];
  bool fallback = 12 [(com.netflix.proto.options.docs.field) = {description: "Indicates if this is a fallback"}];
  bool overridden = 13 [(com.netflix.proto.options.docs.field) = {description: "Indicates if the geo is overriden"}];
  google.protobuf.StringValue domain = 14 [(com.netflix.proto.options.docs.field) = {description: "domain that the IP address belongs to"}];

  // This field is the IANA TimeZone ID (i.e. America/Los_Angeles) related to the GeoIP location.
  // The value can be overridden by clients via the `x-netflix.request.client.timezoneid` header
  // to provide a more accurate value (such as the device's local timezone id).
  google.protobuf.StringValue iana_timezone = 17 [(com.netflix.proto.options.docs.field) = {description: "the iana timezone"}];
  google.type.LatLng coordinates = 18 [(com.netflix.proto.options.docs.field) = {description: "the geo coordinates that the IP address maps to"}];
  google.protobuf.StringValue company = 19 [(com.netflix.proto.options.docs.field) = {description: "company that the IP address belongs to"}];
  google.protobuf.StringValue network_type = 20 [(com.netflix.proto.options.docs.field) = {description: "the network connection type the IP address belongs to"}];
  bool country_mapped = 21 [(com.netflix.proto.options.docs.field) = {description: "indicates if real_country is mapped to country using OctoberSky (e.g. PR to US)"}];

  map<string, string> extra_attributes = 50 [(com.netflix.proto.options.docs.field) = {description: "contains additional attributes that are not explicitly modeled"}];
}

message IpAddress {
  reserved 2;
  string address = 1;
}

enum IpAddressFamily {
  UNKNOWN = 0;
  IPv4 = 1;
  IPv6 = 2;
}