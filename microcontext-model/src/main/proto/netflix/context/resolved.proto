syntax = "proto3";

package netflix.context;

import "com/netflix/proto/options/docs/api-docs.proto";
import "netflix/context/experimentation/experimentation.proto";
import "netflix/context/user/user.proto";
import "netflix/context/video/video.proto";

option java_multiple_files = true;
option java_outer_classname = "ResolvedProtos";
option java_package = "netflix.context";

message ResolvedContexts {
  netflix.context.experimentation.ExperimentationContext experimentation = 1;
}

message InteractiveContext {
  repeated int32 package_video_ids = 1 [
    deprecated=true,
    (com.netflix.proto.options.docs.field) = {description: "list of videos that should get Interactive merch artwork"},
    packed = true
  ];
  repeated int32 merch_video_ids = 2 [
    deprecated=true,
    (com.netflix.proto.options.docs.field) = {description: "list of videos that should leverage the CompleteVideo with an Interactive package tag"},
    packed = true
  ];
}