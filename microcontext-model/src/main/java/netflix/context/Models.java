package netflix.context;

import com.netflix.type.protogen.BasicTypes.Country;
import com.netflix.type.protogen.BasicTypes.Locale;
import javax.annotation.Nonnull;

public class Models {

  public static Country country(@Nonnull String country) {
    return Country.newBuilder().setId(country).build();
  }

  public static Locale locale(@Nonnull String locale) {
    return Locale.newBuilder().setId(locale).build();
  }
}
