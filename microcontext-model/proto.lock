{"definitions": [{"protopath": "src:/:main:/:proto:/:netflix:/:context:/:auth:/:auth.proto", "def": {"messages": [{"name": "<PERSON><PERSON>", "fields": [{"id": 1, "name": "customer_id", "type": "int64"}, {"id": 2, "name": "customer_guid", "type": "string"}, {"id": 3, "name": "account_owner_id", "type": "google.protobuf.Int64Value"}, {"id": 4, "name": "account_owner_guid", "type": "google.protobuf.StringValue"}]}, {"name": "AuthContext", "fields": [{"id": 1, "name": "current_auth", "type": "<PERSON><PERSON>"}, {"id": 2, "name": "visitor_device_id", "type": "google.protobuf.StringValue"}, {"id": 3, "name": "esn", "type": "google.protobuf.StringValue"}, {"id": 4, "name": "device_type", "type": "netflix.basicTypes.DeviceType"}, {"id": 5, "name": "app_type", "type": "com.netflix.appregistry.AppType"}, {"id": 6, "name": "app_id", "type": "google.protobuf.StringValue"}]}], "imports": [{"path": "google/protobuf/wrappers.proto"}, {"path": "netflix/basicTypes/basic_types.proto"}, {"path": "appregistry-models.proto"}], "package": {"name": "netflix.context.auth"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "Auth<PERSON><PERSON><PERSON>"}, {"name": "java_package", "value": "netflix.context.auth"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:client:/:capabilities.proto", "def": {"enums": [{"name": "FeatureCapability", "enum_fields": [{"name": "supportsTop10"}, {"name": "supportsTop10Kids", "integer": 1}, {"name": "supportsStudioBranding", "integer": 2}]}, {"name": "InteractiveOriginal", "enum_fields": [{"name": "pussinbook"}, {"name": "<PERSON><PERSON><PERSON>be<PERSON>", "integer": 1}, {"name": "stretchbreakout", "integer": 2}, {"name": "minecraft", "integer": 3}, {"name": "bandersnatch", "integer": 4}, {"name": "bandersnatchPrePlay", "integer": 5}, {"name": "badgeIconTest", "integer": 6}]}, {"name": "LocalizationCapability", "enum_fields": [{"name": "defaultKidsProfile"}]}, {"name": "TitleCapability", "enum_fields": [{"name": "episodeOrdering"}, {"name": "seasonOrdering", "integer": 1}, {"name": "hiddenEpisodeNumbers", "integer": 2}, {"name": "episodicNewBadge", "integer": 3}, {"name": "episodicAvailabilityMessage", "integer": 4}, {"name": "flattenedShow", "integer": 5}, {"name": "episodeSkipping", "integer": 6}, {"name": "SUPPORTS_ALT_SEASON_LABEL", "integer": 7}]}], "messages": [{"name": "CapabilitiesContext", "fields": [{"id": 1, "name": "feature_capabilities", "type": "FeatureCapabilityUnion", "is_repeated": true}, {"id": 2, "name": "localization_capabilities", "type": "LocalizationCapabilityUnion", "is_repeated": true}, {"id": 3, "name": "title_capabilities", "type": "TitleCapabilityUnion", "is_repeated": true}, {"id": 4, "name": "interactive_originals", "type": "InteractiveOriginalUnion", "is_repeated": true}]}, {"name": "FeatureCapabilityUnion", "fields": [{"id": 1, "name": "enum_value", "type": "FeatureCapability"}, {"id": 2, "name": "unkown_value", "type": "string"}]}, {"name": "InteractiveOriginalUnion", "fields": [{"id": 1, "name": "enum_value", "type": "InteractiveOriginal"}, {"id": 2, "name": "unknown_value", "type": "string"}]}, {"name": "LocalizationCapabilityUnion", "fields": [{"id": 1, "name": "enum_value", "type": "LocalizationCapability"}, {"id": 2, "name": "unkown_value", "type": "string"}]}, {"name": "TitleCapabilityUnion", "fields": [{"id": 1, "name": "enum_value", "type": "TitleCapability"}, {"id": 2, "name": "unkown_value", "type": "string"}]}], "package": {"name": "netflix.context.client"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "CapabilitiesProtos"}, {"name": "java_package", "value": "netflix.context.client"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:client:/:category:/:category.proto", "def": {"enums": [{"name": "ClientCategory", "enum_fields": [{"name": "UNSPECIFIED"}, {"name": "TV", "integer": 1}, {"name": "ANDROID", "integer": 2}, {"name": "IOS", "integer": 3}, {"name": "WEB", "integer": 4}, {"name": "WIN", "integer": 5, "options": [{"name": "deprecated", "value": "true"}]}, {"name": "MEDIAROOM", "integer": 6, "options": [{"name": "deprecated", "value": "true"}]}, {"name": "ATV", "integer": 7}]}], "messages": [{"name": "AndroidCategoryDetails", "fields": [{"id": 1, "name": "installer_source", "type": "string"}]}, {"name": "BrowserCategoryDetails", "fields": [{"id": 1, "name": "browser_name", "type": "string"}, {"id": 2, "name": "browser_version", "type": "string"}]}, {"name": "TVCategoryDetails", "fields": [{"id": 1, "name": "nrd_app_version", "type": "netflix.context.common.Version"}, {"id": 2, "name": "ui_version", "type": "netflix.context.common.Version"}]}], "imports": [{"path": "google/protobuf/wrappers.proto"}, {"path": "netflix/context/common/common.proto"}], "package": {"name": "netflix.context.client.category"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "ClientCategoryProtos"}, {"name": "java_package", "value": "netflix.context.client.category"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:client:/:client.proto", "def": {"messages": [{"name": "ClientContext", "fields": [{"id": 4, "name": "app_version", "type": "netflix.context.common.Version"}, {"id": 5, "name": "os_version", "type": "netflix.context.common.Version"}, {"id": 6, "name": "sdk_version", "type": "netflix.context.common.Version"}, {"id": 7, "name": "capabilities", "type": "netflix.context.client.CapabilitiesContext", "options": [{"name": "deprecated", "value": "true"}]}, {"id": 8, "name": "client_flavor", "type": "netflix.context.client.flavor.ClientFlavor"}, {"id": 9, "name": "client_category", "type": "netflix.context.client.category.ClientCategory"}, {"id": 10, "name": "client_form_factor", "type": "netflix.context.client.formfactor.ClientFormFactor"}, {"id": 12, "name": "os_name", "type": "google.protobuf.StringValue"}, {"id": 13, "name": "tier", "type": "netflix.context.client.tier.DeviceTier"}, {"id": 20, "name": "browser_details", "type": "netflix.context.client.category.BrowserCategoryDetails"}, {"id": 21, "name": "android_details", "type": "netflix.context.client.category.AndroidCategoryDetails"}, {"id": 22, "name": "tv_details", "type": "netflix.context.client.category.TVCategoryDetails"}], "reserved_ids": [1, 2, 3, 11]}], "imports": [{"path": "google/protobuf/wrappers.proto"}, {"path": "netflix/context/client/capabilities.proto"}, {"path": "netflix/context/client/category/category.proto"}, {"path": "netflix/context/client/flavor/flavor.proto"}, {"path": "netflix/context/client/formfactor/formfactor.proto"}, {"path": "netflix/context/client/tier/tier.proto"}, {"path": "netflix/context/common/common.proto"}], "package": {"name": "netflix.context.client"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "ClientProtos"}, {"name": "java_package", "value": "netflix.context.client"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:client:/:flavor:/:flavor.proto", "def": {"enums": [{"name": "ClientFlavor", "enum_fields": [{"name": "UNSPECIFIED"}, {"name": "AKIRA", "integer": 1}, {"name": "ANDROID", "integer": 2}, {"name": "ARGO", "integer": 3}, {"name": "ATV_FUJI", "integer": 4}, {"name": "ATV_HOPPER", "integer": 5}, {"name": "DARWIN", "integer": 6}, {"name": "FAKIRA", "integer": 7}, {"name": "IOS_LEGACY", "integer": 8}, {"name": "TV_OTHER", "integer": 9}, {"name": "WINDOWS_GOTHAM", "integer": 11}, {"name": "WINDOWS_PX", "integer": 12}, {"name": "WINDOWS_WILDCAT", "integer": 13}, {"name": "ECLIPSE", "integer": 14}, {"name": "ATV_ECLIPSE", "integer": 15}, {"name": "BUTTERFLY", "integer": 16}, {"name": "TREX", "integer": 17}, {"name": "DET", "integer": 18}], "reserved_ids": [10]}], "package": {"name": "netflix.context.client.flavor"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "ClientFlavorProtos"}, {"name": "java_package", "value": "netflix.context.client.flavor"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:client:/:formfactor:/:formfactor.proto", "def": {"enums": [{"name": "ClientFormFactor", "enum_fields": [{"name": "UNSPECIFIED"}, {"name": "PHONE", "integer": 1}, {"name": "TABLET", "integer": 2}]}], "package": {"name": "netflix.context.client.formfactor"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "ClientFormFactorProtos"}, {"name": "java_package", "value": "netflix.context.client.formfactor"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:client:/:tier:/:tier.proto", "def": {"enums": [{"name": "DeviceTier", "enum_fields": [{"name": "UNSPECIFIED"}, {"name": "INNOVATION", "integer": 1}, {"name": "FOLLOWER", "integer": 2}, {"name": "MAINTENANCE", "integer": 3}, {"name": "UNSUPPORTED", "integer": 4}]}], "imports": [{"path": "google/protobuf/wrappers.proto"}, {"path": "netflix/context/common/common.proto"}], "package": {"name": "netflix.context.client.tier"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "ClientTierProtos"}, {"name": "java_package", "value": "netflix.context.client.tier"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:common:/:common.proto", "def": {"messages": [{"name": "StringList", "fields": [{"id": 1, "name": "values", "type": "string", "is_repeated": true}]}, {"name": "Version", "fields": [{"id": 1, "name": "version", "type": "string"}]}], "imports": [{"path": "google/protobuf/wrappers.proto"}], "package": {"name": "netflix.context.common"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "CommonProtos"}, {"name": "java_package", "value": "netflix.context.common"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:context.proto", "def": {"messages": [{"name": "Context", "fields": [{"id": 1, "name": "request_id", "type": "google.protobuf.StringValue"}, {"id": 2, "name": "country", "type": "netflix.basicTypes.Country"}, {"id": 3, "name": "locales", "type": "netflix.basicTypes.Locale", "is_repeated": true}, {"id": 15, "name": "client", "type": ".netflix.context.client.ClientContext"}, {"id": 17, "name": "geo", "type": ".netflix.context.geo.GeoContext", "options": [{"name": "deprecated", "value": "true"}]}, {"id": 18, "name": "user", "type": ".netflix.context.user.UserContext", "options": [{"name": "deprecated", "value": "true"}]}, {"id": 19, "name": "video", "type": ".netflix.context.video.VideoContext"}, {"id": 20, "name": "visit", "type": ".netflix.context.visit.VisitContext"}, {"id": 21, "name": "device", "type": ".netflix.context.device.DeviceContext"}, {"id": 40, "name": "resolved", "type": ".google.protobuf.Any"}, {"id": 51, "name": "details", "type": "Details"}], "reserved_ids": [16, 23, 50]}, {"name": "Details", "fields": [{"id": 2, "name": "fallback", "type": "bool"}], "reserved_ids": [1]}], "imports": [{"path": "google/protobuf/any.proto"}, {"path": "google/protobuf/wrappers.proto"}, {"path": "netflix/basicTypes/basic_types.proto"}, {"path": "netflix/context/auth/auth.proto"}, {"path": "netflix/context/client/client.proto"}, {"path": "netflix/context/device/device.proto"}, {"path": "netflix/context/geo/geo.proto"}, {"path": "netflix/context/user/user.proto"}, {"path": "netflix/context/video/video.proto"}, {"path": "netflix/context/visit/visit.proto"}], "package": {"name": "netflix.context"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "ContextProtos"}, {"name": "java_package", "value": "netflix.context"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:device:/:device.proto", "def": {"messages": [{"name": "DeviceContext", "fields": [{"id": 1, "name": "type", "type": "netflix.basicTypes.DeviceType"}, {"id": 4, "name": "esn", "type": "google.protobuf.StringValue"}, {"id": 6, "name": "hardware_major_category", "type": ".com.netflix.dcms.DseHardwareMajorCategory"}, {"id": 7, "name": "client_platform", "type": ".com.netflix.dcms.DseClientPlatformName"}, {"id": 8, "name": "support_level", "type": ".netflix.context.device.supportlevel.DeviceSupportLevel"}, {"id": 9, "name": "retired", "type": "bool"}, {"id": 10, "name": "memory_range", "type": "google.protobuf.UInt32Value"}, {"id": 11, "name": "retirement_message", "type": "google.protobuf.StringValue"}], "reserved_ids": [2, 3, 5], "reserved_names": ["category", "category_id"]}], "imports": [{"path": "dcms-models.proto"}, {"path": "google/protobuf/wrappers.proto"}, {"path": "netflix/basicTypes/basic_types.proto"}, {"path": "netflix/context/device/supportlevel/supportlevel.proto"}], "package": {"name": "netflix.context.device"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "DeviceProtos"}, {"name": "java_package", "value": "netflix.context.device"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:device:/:supportlevel:/:supportlevel.proto", "def": {"enums": [{"name": "DeviceSupportLevel", "enum_fields": [{"name": "UNSPECIFIED"}, {"name": "STANDARD", "integer": 1}, {"name": "MEASURED", "integer": 2}, {"name": "DNR", "integer": 3}, {"name": "UNSUPPORTED", "integer": 4}]}], "package": {"name": "netflix.context.device.supportlevel"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "DeviceSupportLevelProtos"}, {"name": "java_package", "value": "netflix.context.device.supportlevel"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:experimentation:/:experimentation.proto", "def": {"messages": [{"name": "Allocations", "maps": [{"key_type": "int32", "field": {"id": 2, "name": "test_to_cell", "type": "int32"}}], "reserved_ids": [1]}, {"name": "ExperimentationContext", "fields": [{"id": 2, "name": "account_allocations", "type": "Allocations"}, {"id": 3, "name": "vdid_allocations", "type": "Allocations"}], "reserved_ids": [1]}], "package": {"name": "netflix.context.experimentation"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "ExperimentationProtos"}, {"name": "java_package", "value": "netflix.context.experimentation"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:geo:/:geo.proto", "def": {"enums": [{"name": "IpAddressFamily", "enum_fields": [{"name": "UNKNOWN"}, {"name": "IPv4", "integer": 1}, {"name": "IPv6", "integer": 2}]}], "messages": [{"name": "GeoContext", "fields": [{"id": 1, "name": "country", "type": "netflix.basicTypes.Country", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "The ISO-3166 country code for the country where the request IP addressmaps to according to the service provider"}]}]}, {"id": 2, "name": "real_country", "type": "netflix.basicTypes.Country", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "Only populated if an override is applied"}]}]}, {"id": 3, "name": "region_code", "type": "google.protobuf.StringValue", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "ISO-3166, two-letter code for the state, province, or region"}]}]}, {"id": 4, "name": "city", "type": "google.protobuf.StringValue", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "the city (within a 50-mile radius)"}]}]}, {"id": 5, "name": "zip", "type": "google.protobuf.StringValue", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "the zip code"}]}]}, {"id": 8, "name": "ip_address", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "the IP address for which this object contains data"}]}]}, {"id": 9, "name": "real_ip_address", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "Only populated if ip_address overridden"}]}]}, {"id": 10, "name": "asn", "type": "google.protobuf.StringValue", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "The autonomous system number of the request.  On the internet, an ISP officially registers an ASN as a form of identification.  These numbers are written either as simple integers, or in the form x.y, where x and y are 16-bit numbers."}]}]}, {"id": 11, "name": "blocked_proxy", "type": "bool", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "Indicates this is a blocked proxy"}]}]}, {"id": 12, "name": "fallback", "type": "bool", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "Indicates if this is a fallback"}]}]}, {"id": 13, "name": "overridden", "type": "bool", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "Indicates if the geo is overriden"}]}]}, {"id": 14, "name": "domain", "type": "google.protobuf.StringValue", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "domain that the IP address belongs to"}]}]}, {"id": 17, "name": "iana_timezone", "type": "google.protobuf.StringValue", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "the iana timezone"}]}]}, {"id": 18, "name": "coordinates", "type": "google.type.LatLng", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "the geo coordinates that the IP address maps to"}]}]}, {"id": 19, "name": "company", "type": "google.protobuf.StringValue", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "company that the IP address belongs to"}]}]}, {"id": 20, "name": "network_type", "type": "google.protobuf.StringValue", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "the network connection type the IP address belongs to"}]}]}, {"id": 21, "name": "country_mapped", "type": "bool", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "indicates if real_country is mapped to country using OctoberSky (e.g. PR to US)"}]}]}], "maps": [{"key_type": "string", "field": {"id": 50, "name": "extra_attributes", "type": "string", "options": [{"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "contains additional attributes that are not explicitly modeled"}]}]}}], "reserved_ids": [7, 15, 16]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fields": [{"id": 1, "name": "address", "type": "string"}], "reserved_ids": [2]}], "imports": [{"path": "com/netflix/proto/options/docs/api-docs.proto"}, {"path": "google/protobuf/wrappers.proto"}, {"path": "google/type/latlng.proto"}, {"path": "netflix/basicTypes/basic_types.proto"}], "package": {"name": "netflix.context.geo"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "GeoProtos"}, {"name": "java_package", "value": "netflix.context.geo"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:resolved.proto", "def": {"messages": [{"name": "InteractiveContext", "fields": [{"id": 1, "name": "package_video_ids", "type": "int32", "is_repeated": true, "options": [{"name": "deprecated", "value": "true"}, {"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "list of videos that should get Interactive merch artwork"}]}, {"name": "packed", "value": "true"}]}, {"id": 2, "name": "merch_video_ids", "type": "int32", "is_repeated": true, "options": [{"name": "deprecated", "value": "true"}, {"name": "(com.netflix.proto.options.docs.field)", "aggregated": [{"name": "description", "value": "list of videos that should leverage the CompleteVideo with an Interactive package tag"}]}, {"name": "packed", "value": "true"}]}]}, {"name": "ResolvedContexts", "fields": [{"id": 1, "name": "experimentation", "type": "netflix.context.experimentation.ExperimentationContext"}]}], "imports": [{"path": "com/netflix/proto/options/docs/api-docs.proto"}, {"path": "netflix/context/experimentation/experimentation.proto"}, {"path": "netflix/context/user/user.proto"}, {"path": "netflix/context/video/video.proto"}], "package": {"name": "netflix.context"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "ResolvedProtos"}, {"name": "java_package", "value": "netflix.context"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:user:/:user.proto", "def": {"messages": [{"name": "User", "fields": [{"id": 1, "name": "id", "type": "int64"}, {"id": 2, "name": "guid", "type": "string"}, {"id": 3, "name": "owner_id", "type": "google.protobuf.Int64Value"}, {"id": 4, "name": "owner_guid", "type": "google.protobuf.StringValue"}, {"id": 5, "name": "registration_country", "type": "netflix.basicTypes.Country"}, {"id": 6, "name": "signup_country", "type": "netflix.basicTypes.Country"}, {"id": 7, "name": "recent_viewing_country", "type": "netflix.basicTypes.Country"}, {"id": 8, "name": "primary_language", "type": "google.protobuf.StringValue"}, {"id": 9, "name": "maturity_level", "type": "google.protobuf.Int32Value"}, {"id": 10, "name": "profile_name", "type": "google.protobuf.StringValue"}, {"id": 11, "name": "plan_id", "type": "google.protobuf.Int64Value"}, {"id": 12, "name": "current_member", "type": "bool"}, {"id": 13, "name": "active_or_hold", "type": "bool"}, {"id": 14, "name": "membership_status", "type": "com.netflix.subscriber.types.Membership.Status"}, {"id": 15, "name": "experience_type", "type": "com.netflix.subscriber.types.Experience.Type"}, {"id": 16, "name": "is_tester", "type": "bool", "options": [{"name": "deprecated", "value": "true"}]}, {"id": 17, "name": "profile_creation_time", "type": "google.protobuf.Timestamp"}, {"id": 18, "name": "is_autoplay_enabled", "type": "bool"}, {"id": 20, "name": "has_profile_lock_pin_enabled", "type": "bool"}, {"id": 21, "name": "is_pin_enabled", "type": "bool"}, {"id": 22, "name": "fallback", "type": "bool"}, {"id": 23, "name": "ucid", "type": "google.protobuf.StringValue"}, {"id": 24, "name": "sop_type", "type": "com.netflix.subscriber.types.SOpType.Type"}], "reserved_ids": [19]}, {"name": "UserContext", "fields": [{"id": 1, "name": "current_user", "type": "User"}], "reserved_ids": [2]}], "imports": [{"path": "google/protobuf/timestamp.proto"}, {"path": "google/protobuf/wrappers.proto"}, {"path": "netflix/basicTypes/basic_types.proto"}, {"path": "subscriberservicetypes.proto"}], "package": {"name": "netflix.context.user"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "UserProtos"}, {"name": "java_package", "value": "netflix.context.user"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:video:/:video.proto", "def": {"messages": [{"name": "VideoContext", "fields": [{"id": 3, "name": "content_preview_account", "type": "netflix.basicTypes.Visitor"}, {"id": 4, "name": "blocked_proxy", "type": "bool"}], "reserved_ids": [1, 2]}], "imports": [{"path": "netflix/basicTypes/basic_types.proto"}, {"path": "google/protobuf/wrappers.proto"}], "package": {"name": "netflix.context.video"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "VideoProtos"}, {"name": "java_package", "value": "netflix.context.video"}]}}, {"protopath": "src:/:main:/:proto:/:netflix:/:context:/:visit:/:visit.proto", "def": {"enums": [{"name": "AppState", "enum_fields": [{"name": "APP_STATE_UNSPECIFIED"}, {"name": "APP_STATE_FOREGROUND", "integer": 1}, {"name": "APP_STATE_BACKGROUND", "integer": 2}, {"name": "APP_STATE_IDLE", "integer": 3}, {"name": "APP_STATE_UNKNOWN", "integer": 4}]}, {"name": "RequestCategory", "enum_fields": [{"name": "REQUEST_CATEGORY_UNSPECIFIED"}, {"name": "REQUEST_CATEGORY_STREAMING", "integer": 1}, {"name": "REQUEST_CATEGORY_LIVE", "integer": 2}, {"name": "REQUEST_CATEGORY_UNKNOWN", "integer": 3}, {"name": "REQUEST_CATEGORY_GAMES", "integer": 4, "options": [{"name": "deprecated", "value": "true"}]}]}], "messages": [{"name": "Connection", "fields": [{"id": 5, "name": "secure_port", "type": "google.protobuf.BoolValue"}], "reserved_ids": [1, 2, 3, 4]}, {"name": "DeviceDetails", "fields": [{"id": 1, "name": "family", "type": "google.protobuf.StringValue"}]}, {"name": "OSDetails", "fields": [{"id": 1, "name": "family", "type": "google.protobuf.StringValue"}, {"id": 3, "name": "version", "type": "google.protobuf.StringValue", "options": [{"name": "deprecated", "value": "true"}]}, {"id": 4, "name": "sem_ver", "type": "Se<PERSON><PERSON><PERSON>"}], "reserved_ids": [2]}, {"name": "RequestPriority", "fields": [{"id": 1, "name": "priority", "type": "int32"}, {"id": 2, "name": "attempt", "type": "google.protobuf.Int32Value"}, {"id": 3, "name": "category", "type": "RequestCategory"}, {"id": 4, "name": "app_state", "type": "AppState"}]}, {"name": "Se<PERSON><PERSON><PERSON>", "fields": [{"id": 1, "name": "major", "type": "sint32"}, {"id": 2, "name": "minor", "type": "sint32"}, {"id": 3, "name": "patch", "type": "sint32"}]}, {"name": "UserAgent", "fields": [{"id": 1, "name": "value", "type": "string"}, {"id": 2, "name": "client_details", "type": "WebClientDetails"}]}, {"name": "UserAgentDetails", "fields": [{"id": 1, "name": "family", "type": "google.protobuf.StringValue"}, {"id": 3, "name": "version", "type": "google.protobuf.StringValue", "options": [{"name": "deprecated", "value": "true"}]}, {"id": 4, "name": "sem_ver", "type": "Se<PERSON><PERSON><PERSON>"}], "reserved_ids": [2]}, {"name": "VisitContext", "fields": [{"id": 1, "name": "connection", "type": "Connection"}, {"id": 5, "name": "request_time", "type": "google.protobuf.Timestamp"}, {"id": 7, "name": "edge_time", "type": "google.protobuf.Timestamp"}, {"id": 8, "name": "priority", "type": "RequestPriority"}, {"id": 9, "name": "device_memory_level", "type": "google.protobuf.StringValue"}, {"id": 10, "name": "user_agent", "type": "UserAgent"}, {"id": 11, "name": "override_iana_timezone", "type": "google.protobuf.StringValue"}, {"id": 12, "name": "load_test", "type": "bool"}], "reserved_ids": [2, 3, 4, 6]}, {"name": "WebClientDetails", "fields": [{"id": 1, "name": "user_agent_details", "type": "UserAgentDetails"}, {"id": 2, "name": "os_details", "type": "OSDetails"}, {"id": 3, "name": "device_details", "type": "DeviceDetails"}]}], "imports": [{"path": "google/protobuf/timestamp.proto"}, {"path": "google/protobuf/wrappers.proto"}, {"path": "netflix/context/common/common.proto"}, {"path": "netflix/context/geo/geo.proto"}], "package": {"name": "netflix.context.visit"}, "options": [{"name": "java_multiple_files", "value": "true"}, {"name": "java_outer_classname", "value": "VisitProtos"}, {"name": "java_package", "value": "netflix.context.visit"}]}}]}