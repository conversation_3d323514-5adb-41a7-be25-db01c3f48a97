apply plugin: 'com.netflix.grpc.proto-definition'
apply plugin: 'netflix.jvm-library'

dependencies {
    api 'com.google.protobuf:protobuf-java'
    api 'io.grpc:grpc-protobuf'
    api 'io.grpc:grpc-stub'
    api 'netflix.grpc:netflix-grpc-options-proto-definition'
    api 'netflix:basicTypes-proto:latest.release'
    api 'netflix:subscriberservice-proto-definition:latest.release'
    api 'com.netflix.dcms:dcms-runtime-proto:latest.release'
    api 'com.netflix.appregistry.admin:appregistry-rt-proto:latest.release'
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(8)
    }
}


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations:latest.release'
}
