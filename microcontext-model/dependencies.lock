{"compileClasspath": {"com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx"]}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["netflix:passport"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0"}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["netflix:passport"]}, "netflix:passport": {"locked": "4.6.0", "transitive": ["netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0"}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}}, "compileProtoPath": {"com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1", "transitive": ["netflix:basicTypes-proto"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.gson:gson": {"locked": "2.8.9", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["netflix:passport"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0"}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-lite-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["netflix:passport"]}, "netflix:passport": {"locked": "4.6.0", "transitive": ["netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0"}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}}, "nebulaRecommenderBom": {"com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations": {"locked": "2.7.226"}}, "protobufToolsLocator_grpc_java": {"io.grpc:protoc-gen-grpc-java": {"locked": "1.39.0"}}, "protobufToolsLocator_netflix_grpc_wrapper_type": {"netflix.grpc-plugins:netflix-grpc-plugin-protoc-wrapper-type": {"locked": "7.5.8"}}, "protobufToolsLocator_protoc": {"com.google.protobuf:protoc": {"locked": "3.22.2"}}, "protolock": {"nilslice:protolock": {"locked": "20210218T172155Z"}}, "resolutionRules": {"com.netflix.microcontext:microcontext": {"project": true}, "com.netflix.nebula:gradle-resolution-rules": {"locked": "0.82.0", "transitive": ["com.netflix.microcontext:microcontext"]}, "netflix.nebula.resolutionrules:resolution-rules": {"locked": "1.357.0", "transitive": ["com.netflix.microcontext:microcontext"]}}, "runtimeClasspath": {"com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1", "transitive": ["netflix:basicTypes-proto"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.gson:gson": {"locked": "2.8.9", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["netflix:passport"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0"}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-lite-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["netflix:passport"]}, "netflix:passport": {"locked": "4.6.0", "transitive": ["netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0"}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}}, "testCompileClasspath": {"com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx"]}, "com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["netflix:passport"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0"}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["netflix:passport"]}, "netflix:passport": {"locked": "4.6.0", "transitive": ["netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0"}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}}, "testCompileProtoPath": {"com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1", "transitive": ["netflix:basicTypes-proto"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.gson:gson": {"locked": "2.8.9", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["netflix:passport"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0"}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-lite-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["netflix:passport"]}, "netflix:passport": {"locked": "4.6.0", "transitive": ["netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0"}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}}, "testRuntimeClasspath": {"com.atomicjar:testcontainers-cloud-provider-strategy": {"locked": "0.0.41", "transitive": ["com.netflix.nebula.testcontainers:testcontainers-cloud-configurer"]}, "com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1", "transitive": ["netflix:basicTypes-proto"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.gson:gson": {"locked": "2.8.9", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["netflix:passport"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0"}, "com.netflix.nebula.testcontainers:testcontainers-cloud-configurer": {"locked": "1.16.0"}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-lite-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["netflix:passport"]}, "netflix:passport": {"locked": "4.6.0", "transitive": ["netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0"}, "org.apiguardian:apiguardian-api": {"locked": "1.1.2", "transitive": ["org.junit.platform:junit-platform-commons", "org.junit.platform:junit-platform-engine", "org.junit.platform:junit-platform-launcher"]}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}, "org.junit.platform:junit-platform-commons": {"locked": "1.12.2", "transitive": ["org.junit.platform:junit-platform-engine"]}, "org.junit.platform:junit-platform-engine": {"locked": "1.12.2", "transitive": ["org.junit.platform:junit-platform-launcher"]}, "org.junit.platform:junit-platform-launcher": {"locked": "1.12.2"}, "org.opentest4j:opentest4j": {"locked": "1.3.0", "transitive": ["org.junit.platform:junit-platform-engine"]}}}