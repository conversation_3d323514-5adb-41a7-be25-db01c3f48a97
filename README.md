# Microcontext

> Microcontext is a well-defined, immutable context model containing expected request data. The data is resolved in the earliest stage of request execution and passed to downstream services.

## Usage

see https://manuals.netflix.net/view/microcontext/mkdocs/master/starting/


## Initialization
For device requests Microcontext will be populated in Zuul and propagated downstream to each service in the call path.  For all other requests i.e. testing or manual initialization the Microcontext will need to be bootstrapped within the calling context.  For details see [initialization](microcontext-init/README.md)

***Note: any data that is marked as deprecated in [context.proto](microcontext-model/src/main/proto/netflix/context/context.proto) will not be available by default from the gateway and must be [resolved](microcontext-resolver/README.md)***

## Testing

see https://manuals.netflix.net/view/microcontext/mkdocs/master/guides/testing/

## Support

This project is owned by [<EMAIL>](mailto:<EMAIL>).
Join [#tli-support][#tli-support] for support questions, or [#tli-oncall][#tli-oncall] for issues impacting production. 

See the [proposal document][proposal] for an in-depth explanation of the motivations for this project.

### Dashboards

- [Performance Vitals][perf-vitals]
- [Generated service-specific dashboard][generated-dash]

[email]: mailto:<EMAIL>
[#tli-support]: https://netflix.enterprise.slack.com/archives/C04S66S0G64
[#tli-oncall]: https://netflix.enterprise.slack.com/archives/C04R28S7XJB 
[proposal]: https://docs.google.com/document/d/1uX-mgYp8IPmUsn61pv2tjThdclEOcn4NOyErsg4_Hg4/edit?usp=sharing
[perf-vitals]: https://lumen.prod.netflix.net/show/PerformanceVitals?v.nf.app=s~microcontext&v.nf.cluster=s~microcontext&v.target=s~prod.global
[generated-dash]: https://lumen-v2.prod.netflix.net/show/microcontextdashboard
