Initialization
===

## Usage

This will typically never be needed to be accessed outside of the gateway use case however you can provide the inputs to exercise the initialization, parameters indicate if they are Nullable or Nonnull

```java
MicrocontextInitializer.init(MapHeaderResolver.empty(), null, null, null, CurrentRequestContext.get(), null);
```

TODO add use cases for bootstrapping batch requests