package com.netflix.microcontext.init.resolvers.locale;

import com.netflix.i18n.NFLocale;
import com.netflix.type.proto.Countries;
import com.netflix.type.proto.Locales;
import com.netflix.type.protogen.BasicTypes.Country;
import com.netflix.type.protogen.BasicTypes.Locale;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;

public class CountryLocaleUtil {

  public static Optional<List<Locale>> resolveCountry(@Nonnull Country country) {
    List<Locale> matchingLocales = getMatchingLocales(country);
    if (!matchingLocales.isEmpty()) {
      return Optional.of(matchingLocales);
    } else {
      List<Locale> supportedLocales = getSupportedLocales(country);
      if (supportedLocales != null && !supportedLocales.isEmpty()) {
        return Optional.of(supportedLocales);
      }
    }
    return Optional.empty();
  }

  public static List<Locale> getSupportedLocales(Country country) {
    try {
      List<NFLocale> supportedLocales =
          NFLocale.getSupportedLocales(Countries.toBasicType(country));
      return supportedLocales != null
          ? convertLocaleList(supportedLocales)
          : Collections.emptyList();
    } catch (Throwable t) {
      return Collections.emptyList();
    }
  }

  public static List<Locale> getMatchingLocales(Country country) {
    try {
      final List<NFLocale> matchingLocaleList =
          NFLocale.getMatchingLocaleList(
              new ArrayList<>(), null, country.getId(), NFLocale.MATCH_UI_AND_MESSAGES);
      return convertLocaleList(matchingLocaleList);
    } catch (Throwable t) {
      return Collections.emptyList();
    }
  }

  public static List<Locale> convertLocaleList(@Nonnull List<NFLocale> locales) {
    return locales.stream()
        .map(NFLocale::getId)
        .map(Locales::toProtobuf)
        .collect(Collectors.toList());
  }
}
