package com.netflix.microcontext.init.params;

import com.netflix.microcontext.init.request.InputResolver;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nonnull;

public interface ParamResolver extends InputResolver {

  ParamResolver EMPTY =
      new ParamResolver() {

        @Override
        public Optional<String> get(@Nonnull String name) {
          return Optional.empty();
        }

        @Nonnull
        @Override
        public List<String> getAll(@Nonnull String name) {
          return Collections.emptyList();
        }

        @Override
        public boolean contains(@Nonnull String name) {
          return false;
        }
      };
}
