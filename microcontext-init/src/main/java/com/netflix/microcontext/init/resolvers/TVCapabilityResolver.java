package com.netflix.microcontext.init.resolvers;

import java.util.stream.Collectors;
import java.util.stream.Stream;
import netflix.context.client.CapabilitiesContext;
import netflix.context.client.FeatureCapability;
import netflix.context.client.FeatureCapabilityUnion;
import netflix.context.client.LocalizationCapability;
import netflix.context.client.LocalizationCapabilityUnion;
import netflix.context.client.TitleCapability;
import netflix.context.client.TitleCapabilityUnion;

/**
 * CapabilitiesContext is a <b>deprecated</b> way to pass device capability information. <br>
 * In NodeQuark, this was passed via BFF DNA context. This can also be passed via capability
 * headers, e.g. <code>x-netflix.context.title-capabilities</code>. This TV resolver matches the
 * capability logic from the TVUI BFF. See the <a href="https://go.netflix.com/-aIpD5">TVUI
 * implementation</a> for more details.
 */
public class TVCapabilityResolver {

  private static final Integer SUPPORTS_TOP_TEN_RANGE = 100;
  private static final CapabilitiesContext DEFAULT_CAPABILITIES =
      CapabilitiesContext.newBuilder()
          .addLocalizationCapabilities(
              LocalizationCapabilityUnion.newBuilder()
                  .setEnumValue(LocalizationCapability.defaultKidsProfile)
                  .build())
          .addAllTitleCapabilities(
              Stream.of(
                      TitleCapability.episodeOrdering,
                      TitleCapability.seasonOrdering,
                      TitleCapability.hiddenEpisodeNumbers,
                      TitleCapability.episodicNewBadge,
                      TitleCapability.episodicAvailabilityMessage,
                      TitleCapability.episodeSkipping)
                  .map(
                      capability ->
                          TitleCapabilityUnion.newBuilder().setEnumValue(capability).build())
                  .collect(Collectors.toSet()))
          .build();
  private static final CapabilitiesContext CAPABILITIES_SUPPORTS_TOP_10 =
      DEFAULT_CAPABILITIES.toBuilder()
          .addFeatureCapabilities(
              FeatureCapabilityUnion.newBuilder()
                  .setEnumValue(FeatureCapability.supportsTop10)
                  .build())
          .addFeatureCapabilities(
              FeatureCapabilityUnion.newBuilder()
                  .setEnumValue(FeatureCapability.supportsTop10Kids)
                  .build())
          .build();

  /** Given an existing Microcontext, resolve the device CapabilitiesContext */
  public static CapabilitiesContext getTvCapabilities(Integer memoryRange) {
    if (SUPPORTS_TOP_TEN_RANGE <= memoryRange) {
      return CAPABILITIES_SUPPORTS_TOP_10;
    }
    return DEFAULT_CAPABILITIES;
  }
}
