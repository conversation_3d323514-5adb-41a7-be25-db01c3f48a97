package com.netflix.microcontext.init.headers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.WebRequest;

@SuppressWarnings("unused")
public class SpringHeaderAttributesResolver implements HeaderResolver {

  private final WebRequest webRequest;
  private final boolean attributesOverHeaders;
  private final ConcurrentHashMap<String, List<String>> allValues = new ConcurrentHashMap<>();

  public SpringHeaderAttributesResolver(WebRequest webRequest) {
    this(webRequest, true);
  }

  public SpringHeaderAttributesResolver(WebRequest webRequest, boolean attributesOverHeaders) {
    this.webRequest = webRequest;
    this.attributesOverHeaders = attributesOverHeaders;
  }

  @Override
  public Optional<String> get(@Nonnull String name) {
    final List<String> all = all(name);
    return all.isEmpty() ? Optional.empty() : Optional.ofNullable(all.get(0));
  }

  @Nonnull
  @Override
  public List<String> getAll(@Nonnull String name) {
    return all(name);
  }

  @Override
  public boolean contains(@Nonnull String name) {
    return get(name).isPresent();
  }

  @Nonnull
  private List<String> all(String name) {
    if (!allValues.containsKey(name)) {
      final String[] headerValues = webRequest.getHeaderValues(name);
      final Object attribute = webRequest.getAttribute(name, RequestAttributes.SCOPE_REQUEST);

      final List<String> values = new ArrayList<>();
      if (attribute instanceof String) {
        if (attributesOverHeaders) {
          values.add((String) attribute);
          addAll(headerValues, values);
        } else {
          addAll(headerValues, values);
          values.add((String) attribute);
        }
      } else if (headerValues != null) {
        addAll(headerValues, values);
      }

      allValues.put(name, Collections.unmodifiableList(values));
    }
    return allValues.get(name);
  }

  private static void addAll(@Nullable String[] headerValues, @Nonnull List<String> values) {
    if (headerValues != null) {
      Collections.addAll(values, headerValues);
    }
  }
}
