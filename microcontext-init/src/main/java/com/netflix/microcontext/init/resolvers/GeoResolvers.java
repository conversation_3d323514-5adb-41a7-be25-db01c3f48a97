package com.netflix.microcontext.init.resolvers;

import com.netflix.geoclient.GeoData;
import com.netflix.geoclient.GeoDataImpl;
import com.netflix.geoclient.context.GeoRequestContextManager;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.resolvers.geo.Geos;
import com.netflix.microcontext.init.utils.Metrics;
import com.netflix.server.context.ContextSerializationException;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Spectator;
import com.netflix.type.ISOCountry;
import com.netflix.type.proto.Countries;
import com.netflix.type.protogen.BasicTypes.Country;
import java.util.Map;
import java.util.Optional;
import javax.annotation.CheckForNull;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.geo.GeoContext;
import netflix.context.user.User;
import netflix.context.user.UserContext;
import netflix.context.visit.VisitContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GeoResolvers {

  private static final Logger logger = LoggerFactory.getLogger(GeoResolvers.class);
  private static final Id RESOLVER_ID =
      Spectator.globalRegistry().createId("microcontext.resolver.geo");

  @Nonnull
  public static GeoContext resolve(
      HeaderResolver headers,
      @CheckForNull Map<String, String> attributes,
      @Nonnull Country lastResortCountry,
      @Nullable VisitContext visitContext) {
    try {
      if (attributes != null && !attributes.isEmpty()) {
        Metrics.incrementResultSuccess(RESOLVER_ID);
        return Geos.context(headers, attributes, visitContext);
      }
    } catch (Exception e) {
      Metrics.incrementResultException(RESOLVER_ID);
      logger.info("Could not resolve geo from attributes", e);
    }
    return defaultGeo(headers, lastResortCountry);
  }

  /**
   * @deprecated use {@link #lookup(HeaderResolver, RequestContext, Country, VisitContext)}
   */
  @Deprecated
  public static GeoContext lookup(
      HeaderResolver headers, RequestContext requestContext, @Nonnull Country lastResortCountry) {
    return lookup(headers, requestContext, lastResortCountry, null);
  }

  public static GeoContext lookup(
      HeaderResolver headers,
      RequestContext requestContext,
      @Nonnull Country lastResortCountry,
      @Nullable VisitContext visitContext) {
    Map<String, String> attributes = null;
    try {
      GeoData geoData =
          GeoRequestContextManager.instance().readFromGeoRequestContext(requestContext);
      if (geoData != null) {
        attributes = geoData.getAttributeMap();
      }
    } catch (ContextSerializationException e) {
      logger.debug("Could not deserialize request context", e);
    }

    return GeoResolvers.resolve(headers, attributes, lastResortCountry, visitContext);
  }

  public static Optional<GeoContext> requestContext(RequestContext requestContext) {
    return Optional.ofNullable(geoAttributes(requestContext)).map(Geos::context);
  }

  public static boolean hasGeo() {
    return hasGeo(CurrentRequestContext.get());
  }

  public static boolean hasGeo(RequestContext requestContext) {
    return requestContext.hasContext("GeoData");
  }

  public static void setGeo(@Nonnull GeoContext geoContext) {
    setGeo(geoContext, CurrentRequestContext.get());
  }

  public static void setGeo(
      @Nonnull GeoContext geoContext, @Nonnull RequestContext requestContext) {
    final GeoData geoData = new GeoDataImpl(Geos.attributeMap(geoContext));
    GeoRequestContextManager.instance().setIntoGeoRequestContext(requestContext, geoData);
  }

  @Nullable
  public static Map<String, String> geoAttributes(RequestContext requestContext) {
    try {
      GeoData geoData =
          GeoRequestContextManager.instance().readFromGeoRequestContext(requestContext);
      if (geoData != null) {
        return geoData.getAttributeMap();
      }
    } catch (ContextSerializationException e) {
      logger.debug("Could not deserialize request context", e);
    }
    return null;
  }

  @Nonnull
  public static GeoContext defaultGeo(HeaderResolver headers, @Nonnull Country lastResortCountry) {
    GeoContext.Builder builder =
        GeoContext.newBuilder().setFallback(true).setCountry(lastResortCountry);
    Geos.overrideTimezone(headers, builder);
    return builder.build();
  }

  @Nonnull
  public static Country lastResortCountry(@Nullable UserContext userContext) {
    return lastResortCountry(userContext, null);
  }

  @Nonnull
  public static Country lastResortCountry(
      @Nullable UserContext userContext, @Nullable ISOCountry signupCountry) {
    final User user =
        userContext == null ? User.getDefaultInstance() : userContext.getCurrentUser();

    if (user.hasRecentViewingCountry()) {
      return user.getRecentViewingCountry();
    } else if (user.hasSignupCountry()) {
      return user.getSignupCountry();
    } else if (signupCountry != null) {
      return Countries.toProtobuf(signupCountry.getId());
    } else {
      // TODO add metric
      return Countries.toProtobuf("US");
    }
  }
}
