package com.netflix.microcontext.init.resolvers;

import com.netflix.microcontext.init.utils.Metrics;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Spectator;
import com.netflix.type.proto.Visitors;
import com.netflix.type.protogen.BasicTypes.Visitor;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.geo.GeoContext;
import netflix.context.video.VideoContext;
import netflix.context.video.VideoContext.Builder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class VideoResolvers {

  private static final Logger logger = LoggerFactory.getLogger(VideoResolvers.class);
  private static final Id RESOLVER_ID =
      Spectator.globalRegistry().createId("microcontext.resolver.video");

  public static Optional<VideoContext> resolve() {
    return resolve(CurrentRequestContext.get());
  }

  public static Optional<VideoContext> resolve(@Nonnull RequestContext requestContext) {
    try {
      final Optional<Visitor> contentPreviewId =
          UserResolvers.getContextPreviewId(requestContext)
              .map(Long::parseLong)
              .map(Visitors::toProtobuf);
      if (contentPreviewId.isPresent()) {
        final Builder builder = VideoContext.newBuilder();
        contentPreviewId.ifPresent(builder::setContentPreviewAccount);
        return Optional.of(builder.build());
      }
      Metrics.incrementResultSuccess(RESOLVER_ID);
    } catch (Exception e) {
      Metrics.incrementResultException(RESOLVER_ID);
      logger.info("Could not set video context", e);
    }
    return Optional.empty();
  }

  public static Optional<Boolean> blockedProxy(@Nullable GeoContext geoContext) {
    if (geoContext != null && geoContext.hasBlockedProxy()) {
      return Optional.of(geoContext.getBlockedProxy());
    }
    return Optional.empty();
  }
}
