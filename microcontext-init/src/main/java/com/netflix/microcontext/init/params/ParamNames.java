package com.netflix.microcontext.init.params;

import static com.netflix.microcontext.init.params.Params.list;

import com.netflix.microcontext.init.headers.Headers;
import java.util.Collections;
import java.util.List;

public class ParamNames {

  public static final String BROWSER_NAME = "browsername";
  public static final String BROWSER_VERSION = "browserversion";

  public static final String OS_NAME = "osname";

  public static final String OS_VERSION = "osversion";
  public static final String API = "api";
  public static final List<String> ALL_OS_VERSIONS = list(OS_VERSION, API);

  public static final String SDK_VERSION = "sdk_version";

  public static final String APP_VERSION = "appversion";
  public static final String NRD_APP_VERSION = "nrdapp_version";
  public static final String CURRENT_NRD_VERSION = "currentNrdappVersion";

  public static final List<String> ALL_NRD_APP_VERSIONS =
      list(NRD_APP_VERSION, CURRENT_NRD_VERSION);

  public static final List<String> ALL_SDK_VERSIONS =
      Headers.combine(Collections.singletonList(SDK_VERSION), ALL_NRD_APP_VERSIONS);

  public static final String UI_SEM_VER = "ui_sem_ver";
  public static final String APPLICATION_V = "application_v";
  public static final String UI_VERSION = "uiversion";
  public static final List<String> ALL_APP_VERSIONS =
      list(APP_VERSION, APPLICATION_V, NRD_APP_VERSION, UI_SEM_VER, UI_VERSION);

  public static final String CLIENT_TYPE = "clienttype";
  public static final String AB_UI_VER = "ab_ui_ver";
  public static final List<String> ALL_CLIENT_TYPES = list(CLIENT_TYPE, AB_UI_VER);
}
