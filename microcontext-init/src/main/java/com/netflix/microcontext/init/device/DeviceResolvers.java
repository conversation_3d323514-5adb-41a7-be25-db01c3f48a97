package com.netflix.microcontext.init.device;

import static com.netflix.microcontext.init.params.Params.list;

import com.netflix.dcms.models.protogen.DseClientPlatformName;
import com.netflix.dcms.models.protogen.DseHardwareMajorCategory;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.headers.Headers;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.params.Params;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Spectator;
import com.netflix.type.protogen.BasicTypes;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.device.DeviceContext;
import netflix.context.device.supportlevel.DeviceSupportLevel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DeviceResolvers {

  private static final Logger logger = LoggerFactory.getLogger(DeviceResolvers.class);
  public static final String HARDWARE_MAJOR_CATEGORY = "HARDWARE_MAJOR_CATEGORY";
  public static final String CLIENT_PLATFORM_NAME = "CLIENT_PLATFORM_NAME";
  public static final String SUPPORT_LEVEL = "SUPPORT_LEVEL";
  public static final String RETIRED = "RETIRED";
  public static final String MEMORY_RANGE = "UI_MEMORY_RANGE";

  private static final Counter PASSPORT =
      Spectator.globalRegistry().counter("microcontext.device.resolver.esn", "source", "passport");
  private static final Counter HEADER =
      Spectator.globalRegistry().counter("microcontext.device.resolver.esn", "source", "header");
  private static final Counter PARAM =
      Spectator.globalRegistry().counter("microcontext.device.resolver.esn", "source", "param");
  private static final Counter NONE =
      Spectator.globalRegistry().counter("microcontext.device.resolver.esn", "source", "none");

  public static List<String> ALL_PROPERTIES =
      list(HARDWARE_MAJOR_CATEGORY, CLIENT_PLATFORM_NAME, SUPPORT_LEVEL, RETIRED, MEMORY_RANGE);

  public static DeviceContext resolve(
      @Nullable String esn,
      @Nullable Integer deviceTypeId,
      @Nonnull Map<String, String> properties) {
    if (esn == null && deviceTypeId == null && properties.isEmpty()) {
      return DeviceContext.getDefaultInstance();
    }
    DeviceContext.Builder builder =
        DeviceContext.newBuilder()
            .setEsn(esn)
            .setHardwareMajorCategory(
                normalizeHardwareCategory(properties.get(HARDWARE_MAJOR_CATEGORY)))
            .setClientPlatform(normalizeClientPlatform(properties.get(CLIENT_PLATFORM_NAME)))
            .setSupportLevel(normalizeSupportLevel(properties.get(SUPPORT_LEVEL)))
            .setRetired(normalizeRetired(properties.get(RETIRED)))
            .setMemoryRange(normalizeMemoryRange(properties.get(MEMORY_RANGE)));
    if (deviceTypeId != null) {
      builder.setType(BasicTypes.DeviceType.newBuilder().setId(deviceTypeId).build());
    }
    return builder.build();
  }

  private static Optional<Integer> normalizeMemoryRange(@Nullable String value) {
    return Optional.ofNullable(value)
        .filter(v -> !v.isEmpty())
        .map(
            v -> {
              try {
                return Integer.valueOf(v);
              } catch (Exception e) {
                logger.warn("Invalid memory range returned: {}", v);
                return null;
              }
            });
  }

  private static boolean normalizeRetired(@Nullable String retired) {
    return Boolean.parseBoolean(retired);
  }

  private static DeviceSupportLevel normalizeSupportLevel(@Nullable String supportLevel) {
    if (supportLevel != null) {
      try {
        return DeviceSupportLevel.valueOf(supportLevel.toUpperCase());
      } catch (Throwable t) {
        logger.info("Could not parse support level value {}", supportLevel, t);
      }
    }
    return DeviceSupportLevel.UNSPECIFIED;
  }

  private static DseClientPlatformName normalizeClientPlatform(
      @Nullable String clientPlatformCategory) {
    if (clientPlatformCategory != null) {
      try {
        return DseClientPlatformName.valueOf(clientPlatformCategory);
      } catch (Throwable t) {
        logger.info("Could not parse client platform value {}", clientPlatformCategory, t);
      }
    }
    return DseClientPlatformName.UNKNOWN_CLIENT_PLATFORM_NAME;
  }

  private static DseHardwareMajorCategory normalizeHardwareCategory(
      @Nullable String deviceHardwareCategory) {
    if (deviceHardwareCategory != null) {
      try {
        return DseHardwareMajorCategory.valueOf(deviceHardwareCategory);
      } catch (Throwable t) {
        logger.info("Could not parse hardware category value {}", deviceHardwareCategory, t);
      }
    }
    return DseHardwareMajorCategory.UNKNOWN;
  }

  public static Optional<String> resolveEsn(
      @Nullable PassportIdentity passport,
      @Nonnull HeaderResolver headerResolver,
      ParamResolver paramResolver) {
    if (passport != null) {
      Optional<String> esn = passport.getEsn();
      if (esn.isPresent() && !esn.get().isEmpty()) {
        PASSPORT.increment();
        return esn;
      }
    }
    final Optional<String> header = headerResolver.getFirst(Headers.ALL_ESNS);
    if (header.isPresent()) {
      HEADER.increment();
      return header;
    }
    final Optional<String> param = paramResolver.getFirst(Params.ALL_ESNS);
    if (param.isPresent()) {
      PARAM.increment();
      return param;
    }
    NONE.increment();
    return Optional.empty();
  }
}
