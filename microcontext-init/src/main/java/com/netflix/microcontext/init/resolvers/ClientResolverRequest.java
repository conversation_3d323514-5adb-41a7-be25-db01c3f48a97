package com.netflix.microcontext.init.resolvers;

import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.request.CookieResolver;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.device.DeviceContext;
import netflix.context.device.DeviceContextOrBuilder;
import netflix.context.visit.WebClientDetails;

public class ClientResolverRequest {
  private final HeaderResolver headerResolver;
  private final ParamResolver paramResolver;
  private final CookieResolver cookieResolver;
  private final boolean resolveCapabilities;
  private final DeviceContextOrBuilder deviceContext;
  private final WebClientDetails clientDetails;
  private final String hostGroup;
  private final boolean disableWebTier;

  ClientResolverRequest(
      HeaderResolver headerResolver,
      ParamResolver paramResolver,
      CookieResolver cookieResolver,
      boolean resolveCapabilities,
      DeviceContextOrBuilder deviceContext,
      WebClientDetails clientDetails,
      String hostGroup,
      boolean disableWebTier) {
    this.headerResolver = headerResolver;
    this.paramResolver = paramResolver;
    this.cookieResolver = cookieResolver;
    this.resolveCapabilities = resolveCapabilities;
    this.deviceContext = deviceContext;
    this.clientDetails = clientDetails;
    this.hostGroup = hostGroup;
    this.disableWebTier = disableWebTier;
  }

  @Nonnull
  public HeaderResolver getHeaderResolver() {
    return headerResolver;
  }

  @Nonnull
  public ParamResolver getParamResolver() {
    return paramResolver;
  }

  @Nonnull
  public CookieResolver getCookieResolver() {
    return cookieResolver;
  }

  public boolean isResolveCapabilities() {
    return resolveCapabilities;
  }

  @Nonnull
  public DeviceContextOrBuilder getDeviceContext() {
    return deviceContext;
  }

  @Nonnull
  public WebClientDetails getClientDetails() {
    return clientDetails;
  }

  @Nullable
  public String getHostGroup() {
    return hostGroup;
  }

  public boolean isDisableWebTier() {
    return disableWebTier;
  }

  public static ClientResolverRequest empty() {
    return builder().build();
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {

    private HeaderResolver headerResolver;
    private ParamResolver paramResolver;
    private CookieResolver cookieResolver;
    private boolean resolveCapabilities;
    private DeviceContextOrBuilder deviceContext;
    private WebClientDetails clientDetails;
    private String hostGroup;
    private boolean disableWebTier;

    public Builder setHeaderResolver(HeaderResolver headerResolver) {
      this.headerResolver = headerResolver;
      return this;
    }

    public Builder setParamResolver(ParamResolver paramResolver) {
      this.paramResolver = paramResolver;
      return this;
    }

    public Builder setCookieResolver(CookieResolver cookieResolver) {
      this.cookieResolver = cookieResolver;
      return this;
    }

    public Builder setResolveCapabilities(boolean resolveCapabilities) {
      this.resolveCapabilities = resolveCapabilities;
      return this;
    }

    public Builder setDeviceContext(DeviceContextOrBuilder deviceContext) {
      this.deviceContext = deviceContext;
      return this;
    }

    public Builder setClientDetails(WebClientDetails clientDetails) {
      this.clientDetails = clientDetails;
      return this;
    }

    public Builder setHostGroup(String hostGroup) {
      this.hostGroup = hostGroup;
      return this;
    }

    public Builder setDisableWebTier(boolean disableWebTier) {
      this.disableWebTier = disableWebTier;
      return this;
    }

    public ClientResolverRequest build() {
      return new ClientResolverRequest(
          headerResolver == null ? HeaderResolver.EMPTY : headerResolver,
          paramResolver == null ? ParamResolver.EMPTY : paramResolver,
          cookieResolver == null ? CookieResolver.EMPTY : cookieResolver,
          resolveCapabilities,
          deviceContext == null ? DeviceContext.getDefaultInstance() : deviceContext,
          clientDetails == null ? WebClientDetails.getDefaultInstance() : clientDetails,
          hostGroup,
          disableWebTier);
    }
  }
}
