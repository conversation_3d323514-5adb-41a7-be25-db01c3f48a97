package com.netflix.microcontext.init.params;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.Nonnull;

public class Params {

  // Esn
  public static final String PARAM_ESN = "esn"; // legacy param used exclusively in APINext
  public static final String PARAM_E = "e"; // legacy param used mostly in APINext
  public static final String FALLBACK_ESN = "fallbackEsn"; // legacy param used in API and APINext
  public static final List<String> ALL_ESNS = list(PARAM_ESN, PARAM_E, FALLBACK_ESN);

  public static List<String> list(@Nonnull String... elements) {
    List<String> list = new ArrayList<>();
    Collections.addAll(list, elements);
    return Collections.unmodifiableList(list);
  }
}
