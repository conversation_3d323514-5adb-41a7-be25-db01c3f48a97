package com.netflix.microcontext.init.resolvers.locale;

import java.util.*;
import java.util.stream.Collectors;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/** Java equivalent of the TypeScript locale resolution functions from shared.ts */
public class WebLocaleResolver {

  private static final String DEFAULT_LOCALE = "en-US";
  private static final Pattern LOCALE_UNDERSCORE_FORMAT =
      Pattern.compile("^([a-zA-Z]+)_([a-zA-Z]+)$");
  private static final Pattern LOCALE_ID_PATTERN = Pattern.compile("^[\\w\\-]+$");
  private static final String WEIGHT_FACTOR_PREFIX = "q=";
  private static final String WILDCARD_SELECTOR = "*";
  private static final String SELECTION_SEPARATOR = ",";
  private static final String WEIGHT_SEPARATOR = ";";
  private static final double DEFAULT_WEIGHT = 1.0;

  /**
   * Returns the scripts (or language families) that are supported by the Netflix app Equivalent to
   * getDeviceSupportedScripts() in TypeScript
   */
  public static List<String> getDeviceSupportedScripts(Map<String, String> parameters) {
    String suppScriptsParam = parameters.get("suppScripts");
    if (suppScriptsParam == null || suppScriptsParam.trim().isEmpty()) {
      return new ArrayList<>();
    }
    return Arrays.stream(suppScriptsParam.split(","))
        .map(String::trim)
        .filter(s -> !s.isEmpty())
        .collect(Collectors.toList());
  }

  /**
   * Returns the languages that the Netflix app can render Equivalent to getDeviceSupportedLocales()
   * in TypeScript
   */
  public static List<Locale> getDeviceSupportedLocales(Map<String, String> parameters) {
    String availableLocalesParam = parameters.get("availableLocales");
    if (availableLocalesParam == null || availableLocalesParam.trim().isEmpty()) {
      return new ArrayList<>();
    }
    List<String> localeIds =
        Arrays.stream(availableLocalesParam.split(","))
            .map(String::trim)
            .filter(s -> !s.isEmpty())
            .collect(Collectors.toList());

    return parseLocaleIdList(localeIds);
  }

  /**
   * Resolves the locales that are being requested by the device Equivalent to
   * getDeviceRequestedLocales() in TypeScript
   */
  public static List<Locale> getDeviceRequestedLocales(
      Map<String, String> parameters, Map<String, String> headers) {
    // Get locales from deviceLocale parameter
    List<String> localesFromParam = new ArrayList<>();
    String deviceLocaleParam = parameters.get("deviceLocale");
    if (deviceLocaleParam != null && !deviceLocaleParam.trim().isEmpty()) {
      localesFromParam =
          Arrays.stream(deviceLocaleParam.split(","))
              .map(String::trim)
              .filter(s -> !s.isEmpty())
              .collect(Collectors.toList());
    }

    // Get locales from Accept-Language header
    List<String> localesFromHeaders = decodeAcceptLanguageHeader(headers);

    // Combine both sources
    List<String> allLocaleIds = new ArrayList<>();
    allLocaleIds.addAll(localesFromParam);
    allLocaleIds.addAll(localesFromHeaders);

    return parseLocaleIdList(allLocaleIds);
  }

  /** Parse Accept-Language header to extract locale identifiers */
  private static List<String> decodeAcceptLanguageHeader(Map<String, String> headers) {
    String acceptLanguageHeader = headers.get("Accept-Language");
    if (acceptLanguageHeader == null || acceptLanguageHeader.trim().isEmpty()) {
      // Default to en-US if no Accept-Language header is provided
      return Arrays.asList(DEFAULT_LOCALE);
    }

    List<LanguagePreference> languagePreferences = parseAcceptLanguageHeader(acceptLanguageHeader);
    return languagePreferences.stream()
        .filter(pref -> pref.selector.type.equals("specific"))
        .map(pref -> pref.selector.id)
        .collect(Collectors.toList());
  }

  /** Parse Accept-Language header value into language preferences */
  private static List<LanguagePreference> parseAcceptLanguageHeader(String headerValue) {
    String[] entries = headerValue.split(SELECTION_SEPARATOR);
    List<LanguagePreference> selections = new ArrayList<>();

    for (String entry : entries) {
      LanguagePreference preference = parsePreferenceEntry(entry);
      if (preference != null) {
        selections.add(preference);
      }
    }

    // Sort by weight (highest first)
    selections.sort((pref1, pref2) -> Double.compare(pref2.weight, pref1.weight));
    return selections;
  }

  /** Parse individual preference entry from Accept-Language header */
  private static LanguagePreference parsePreferenceEntry(String entry) {
    String[] parts = entry.split(WEIGHT_SEPARATOR);
    if (parts.length == 0) {
      return null;
    }

    String localeIdPart = parts[0];
    String weightPart = parts.length > 1 ? parts[1] : null;

    LocaleSelector localeSelector = parseLocaleSelector(localeIdPart);
    Double weight = parseWeight(weightPart);

    if (localeSelector != null && weight != null && isFinite(weight)) {
      return new LanguagePreference(localeSelector, weight);
    }
    return null;
  }

  /** Parse locale selector from string */
  private static LocaleSelector parseLocaleSelector(String localePart) {
    String trimmed = localePart.trim();
    if (WILDCARD_SELECTOR.equals(trimmed)) {
      return new LocaleSelector("wildcard", null);
    } else if (LOCALE_ID_PATTERN.matcher(trimmed).matches()) {
      return new LocaleSelector("specific", trimmed);
    }
    return null;
  }

  /** Parse weight from string */
  private static Double parseWeight(String weightPart) {
    if (weightPart == null) {
      return DEFAULT_WEIGHT;
    }

    String trimmed = weightPart.trim();
    if (trimmed.isEmpty()) {
      return DEFAULT_WEIGHT;
    }

    if (!trimmed.startsWith(WEIGHT_FACTOR_PREFIX)) {
      return null;
    }

    String weightStr = trimmed.substring(WEIGHT_FACTOR_PREFIX.length()).trim();
    try {
      double weight = Double.parseDouble(weightStr);
      return isValidWeight(weight) ? weight : null;
    } catch (NumberFormatException e) {
      return null;
    }
  }

  /** Check if weight is valid (between 0 and 1) */
  private static boolean isValidWeight(double weight) {
    return weight >= 0 && weight <= 1.0;
  }

  /** Check if number is finite */
  private static boolean isFinite(double value) {
    return Double.isFinite(value);
  }

  /** Parse locale identifiers into Locale objects */
  private static List<Locale> parseLocaleIdList(List<String> idList) {
    return idList.stream()
        .map(WebLocaleResolver::parseLocale)
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  /** Parse a locale identifier into a Locale object */
  private static Locale parseLocale(String identifier) {
    Locale locale = tryParseLocale(identifier);
    if (locale != null) {
      return locale;
    }
    return parseLocaleUnderscoreFormat(identifier);
  }

  /** Try to parse locale using standard format */
  private static Locale tryParseLocale(String identifier) {
    try {
      Locale locale = Locale.forLanguageTag(identifier);
      // Check if the locale was parsed successfully (not "und" - undetermined)
      if ("und".equals(locale.toLanguageTag())) {
        return null;
      }
      // Additional validation: check if the language is a valid ISO language code
      String language = locale.getLanguage();
      if (language.isEmpty() || !isValidLanguageCode(language)) {
        return null;
      }
      return locale;
    } catch (Exception e) {
      return null;
    }
  }

  /** Parse locale in underscore format (e.g., es_MX) */
  private static Locale parseLocaleUnderscoreFormat(String value) {
    Matcher matcher = LOCALE_UNDERSCORE_FORMAT.matcher(value);
    if (!matcher.matches()) {
      return null;
    }

    String languageId = matcher.group(1);
    String regionId = matcher.group(2);
    return tryParseLocale(languageId + "-" + regionId);
  }

  /** Language preference data class */
  private static class LanguagePreference {
    final LocaleSelector selector;
    final double weight;

    LanguagePreference(LocaleSelector selector, double weight) {
      this.selector = selector;
      this.weight = weight;
    }
  }

  /** Check if a language code is valid (basic validation) */
  private static boolean isValidLanguageCode(String language) {
    // Basic validation: language should be 2-3 lowercase letters
    return language.matches("^[a-z]{2,3}$");
  }

  /** Locale selector data class */
  private static class LocaleSelector {
    final String type; // "specific" or "wildcard"
    final String id; // locale identifier (null for wildcard)

    LocaleSelector(String type, String id) {
      this.type = type;
      this.id = id;
    }
  }
}
