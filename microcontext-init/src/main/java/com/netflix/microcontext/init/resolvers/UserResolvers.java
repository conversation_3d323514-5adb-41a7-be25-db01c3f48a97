package com.netflix.microcontext.init.resolvers;

import com.google.common.io.ByteStreams;
import com.netflix.microcontext.resolver.user.UserConverter;
import com.netflix.server.context.ContextSerializationException;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Spectator;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import io.grpc.KnownLength;
import io.grpc.MethodDescriptor;
import java.io.IOException;
import java.io.InputStream;
import java.io.UncheckedIOException;
import java.util.Base64;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.user.UserContext;

public class UserResolvers {

  public static final String CACHE_KEY = "subscriber";
  private static final Base64.Decoder DECODER = Base64.getDecoder();
  private static final Base64.Encoder ENCODER = Base64.getEncoder();
  private static final Counter HIT;
  private static final Counter MISS;

  static {
    HIT = Spectator.globalRegistry().counter("microcontext.user.requestcontext", "result", "hit");
    MISS = Spectator.globalRegistry().counter("microcontext.user.requestcontext", "result", "miss");
  }

  public static boolean hasAccountProfile() {
    return CurrentRequestContext.get().containsKey(CACHE_KEY);
  }

  public static Optional<UserContext> requestContext() {
    return getAccountProfile().map(UserConverter::userContext);
  }

  public static Optional<AccountProfileRemote> getAccountProfile() {
    final Optional<AccountProfileRemote> accountProfile =
        deserialize(CurrentRequestContext.get().get(CACHE_KEY));
    (accountProfile.isPresent() ? HIT : MISS).increment();
    return accountProfile;
  }

  private static Optional<AccountProfileRemote> deserialize(@Nullable String serialized) {
    if (serialized != null) {
      try {
        return Optional.of(AccountProfileRemote.parseFrom(DECODER.decode(serialized)));
      } catch (Throwable ignored) {
      }
    }
    return Optional.empty();
  }

  public static Optional<String> getContextPreviewId() {
    return getContextPreviewId(CurrentRequestContext.get());
  }

  public static Optional<String> getContextPreviewId(RequestContext requestContext) {
    try {
      String contentPreviewAccountId = requestContext.getContext("ContentPreviewAccountId");
      return Optional.ofNullable(contentPreviewAccountId);
    } catch (ContextSerializationException e) {
      return Optional.empty();
    }
  }

  public static void setContextPreviewId(long customerId) {
    setContextPreviewId(customerId, CurrentRequestContext.get());
  }

  public static void setContextPreviewId(long customerId, RequestContext requestContext) {
    requestContext.addContext("ContentPreviewAccountId", String.valueOf(customerId));
  }

  public static void setUser(UserContext userContext) {
    setUser(userContext, CurrentRequestContext.get());
  }

  public static void setUser(UserContext userContext, @Nonnull RequestContext requestContext) {
    if (userContext != null && userContext.hasCurrentUser()) {
      requestContext.put(CACHE_KEY, serialize(UserConverter.convert(userContext.getCurrentUser())));
    }
  }

  @SuppressWarnings("UnstableApiUsage")
  private static String serialize(AccountProfileRemote accountProfileRemote) {
    MethodDescriptor.Marshaller<AccountProfileRemote> marshaller =
        io.grpc.protobuf.ProtoUtils.marshaller(
            com.netflix.subscriberservice.protogen.AccountProfileRemote.getDefaultInstance());
    try (InputStream inputStream = marshaller.stream(accountProfileRemote)) {
      final byte[] bytes;
      if (inputStream instanceof KnownLength) {
        bytes = new byte[inputStream.available()];
        ByteStreams.readFully(inputStream, bytes);
      } else {
        bytes = ByteStreams.toByteArray(inputStream);
      }
      return ENCODER.encodeToString(bytes);
    } catch (IOException exc) {
      throw new UncheckedIOException(exc);
    }
  }
}
