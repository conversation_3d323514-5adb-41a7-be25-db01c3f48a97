package com.netflix.microcontext.init.resolvers;

import static com.netflix.microcontext.init.headers.Headers.X_FORWARDED_PROTO_HEADER;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Timestamps;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.headers.Headers;
import com.netflix.microcontext.init.resolvers.geo.Geos;
import com.netflix.microcontext.init.utils.Metrics;
import com.netflix.microcontext.init.utils.NumberUtils;
import com.netflix.server.context.ContextSerializationException;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Spectator;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.visit.AppState;
import netflix.context.visit.Connection;
import netflix.context.visit.RequestPriority;
import netflix.context.visit.UserAgent;
import netflix.context.visit.VisitContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class VisitResolvers {

  private static final Logger logger = LoggerFactory.getLogger(VisitResolvers.class);
  public static final String EDGE_SERVER_TIMESTAMP = "x-netflix.edge.server.timestamp";
  public static final String CLIENT_CONTEXT = "x-netflix.request.client.context";
  public static final String DEVICE_MEMORY_LEVEL = "x-netflix.devicememorylevel";
  public static final String TIME_KEY = "RequestTimestamp";
  public static final String USER_AGENT = "User-Agent";
  private static final Id RESOLVER_ID =
      Spectator.globalRegistry().createId("microcontext.resolver.visit");

  private static final ObjectMapper reader = new ObjectMapper();

  public static VisitContext resolve(@Nonnull HeaderResolver headerResolver) {
    return resolve(headerResolver, CurrentRequestContext.get(), null);
  }

  @Deprecated
  public static VisitContext resolve(
      @Nonnull HeaderResolver headerResolver,
      @Nonnull RequestContext requestContext,
      @Nullable RequestPriority requestPriority) {
    return resolve(headerResolver, requestContext, requestPriority, null);
  }

  public static VisitContext resolve(
      @Nonnull HeaderResolver headerResolver,
      @Nonnull RequestContext requestContext,
      @Nullable RequestPriority requestPriority,
      @Nullable UserAgent userAgent) {
    return resolve(
        headerResolver.get(X_FORWARDED_PROTO_HEADER).orElse(null),
        getServerTimestamp(requestContext).orElse(null),
        getRequestTime(requestContext).orElse(null),
        headerResolver.get(CLIENT_CONTEXT).orElse(null),
        headerResolver.get(DEVICE_MEMORY_LEVEL).orElse(null),
        requestPriority,
        getUserAgent(userAgent, headerResolver).orElse(null),
        Geos.getOverrideTimezoneHeader(headerResolver).orElse(null),
        headerResolver.get(Headers.REGION_LOAD_TEST).orElse(null));
  }

  @Nonnull
  private static Optional<UserAgent> getUserAgent(
      @Nullable UserAgent userAgent, HeaderResolver headerResolver) {
    if (userAgent != null && !userAgent.getValue().isEmpty()) {
      return Optional.of(userAgent);
    }
    return headerResolver
        .get(USER_AGENT)
        .map(header -> UserAgent.newBuilder().setValue(header).build());
  }

  public static Optional<String> getServerTimestamp(RequestContext requestContext) {
    return getContext(EDGE_SERVER_TIMESTAMP, requestContext);
  }

  public static Optional<String> getRequestTime(RequestContext requestContext) {
    return getTime(requestContext);
  }

  public static VisitContext resolve(
      @Nullable String xForwardedProto,
      @Nullable String serverTimestamp,
      @Nullable String requestTime,
      @Nullable String clientContext,
      @Nullable String deviceMemoryLevel,
      @Nullable RequestPriority requestPriority,
      @Nullable UserAgent userAgent,
      @Nullable String overrideTimezone,
      @Nullable String regionLoadTest) {
    return ofInternal(
        xForwardedProto,
        serverTimestamp,
        requestTime,
        clientContext,
        deviceMemoryLevel,
        requestPriority,
        userAgent,
        overrideTimezone,
        regionLoadTest);
  }

  private static VisitContext ofInternal(
      @Nullable String xfp,
      @Nullable String serverTimestamp,
      @Nullable String requestTime,
      @Nullable String clientContext,
      @Nullable String deviceMemoryLevel,
      @Nullable RequestPriority requestPriority,
      @Nullable UserAgent userAgent,
      @Nullable String overrideTimezone,
      @Nullable String regionLoadTest) {
    VisitContext.Builder builder =
        VisitContext.newBuilder().setDeviceMemoryLevel(deviceMemoryLevel);
    try {
      Timestamp currentTimeMillis = Timestamps.fromMillis(System.currentTimeMillis());

      // edge timestamp
      builder.setEdgeTime(
          NumberUtils.parseLong(serverTimestamp)
              .map(Timestamps::fromMillis)
              .orElse(currentTimeMillis));

      // request timestamp
      NumberUtils.parseLong(requestTime)
          .map(Timestamps::fromMillis)
          .ifPresent(builder::setRequestTime);

      Connection.Builder connection = Connection.newBuilder();
      if (xfp != null) {
        connection.setSecurePort(xfp.equals("https"));
      }

      // connection
      if (connection.hasSecurePort()) {
        builder.setConnection(connection);
      }

      if (requestPriority != null) {
        builder.setPriority(requestPriority);
      }

      if (userAgent != null) {
        builder.setUserAgent(userAgent);
      }

      if (builder.getPriority().getAppState() == AppState.APP_STATE_UNSPECIFIED
          && clientContext != null
          && !clientContext.isEmpty()) {
        builder.getPriorityBuilder().setAppState(appState(clientContext));
      }

      // Load tests will provide the value of "1" but we just use the presence as the indicator
      if (regionLoadTest != null) {
        builder.setLoadTest(true);
      }

      builder.setOverrideIanaTimezone(overrideTimezone);

      Metrics.incrementResultSuccess(RESOLVER_ID);
    } catch (Throwable t) {
      Metrics.incrementResultException(RESOLVER_ID);
      logger.error("Could not resolve visit", t);
    }
    return builder.build();
  }

  public static Optional<String> getContext(String key, RequestContext requestContext) {
    try {
      Object context = requestContext.getContext(key);
      if (context instanceof String) {
        return Optional.of((String) context);
      }
    } catch (ContextSerializationException e) {
      logger.error("Could not deserialize {}", key);
    }
    return Optional.empty();
  }

  private static Optional<String> getTime(RequestContext requestContext) {
    return Optional.ofNullable(requestContext.get(VisitResolvers.TIME_KEY));
  }

  static AppState appState(@Nonnull String header) {
    try {
      Map<String, String> map =
          reader.readValue(header, new TypeReference<Map<String, String>>() {});
      return parseState(map);
    } catch (Throwable t) {
      logger.info("Could not parseState client context {}", header, t);
    }
    return AppState.APP_STATE_UNSPECIFIED;
  }

  public static String displayName(@Nonnull AppState appState) {
    switch (appState) {
      case APP_STATE_FOREGROUND:
        return "foreground";
      case APP_STATE_BACKGROUND:
        return "background";
      case APP_STATE_IDLE:
        return "idle";
      case APP_STATE_UNKNOWN:
        return "unknown";
      default:
        return "unspecified";
    }
  }

  private static AppState parseState(final Map<String, String> map) {
    String appState = map.get("appstate");
    if (appState == null) {
      appState = map.get("appState");
    }

    if (appState != null) {
      if (appState.equalsIgnoreCase("unknown")) {
        return AppState.APP_STATE_UNKNOWN;
      } else if (appState.equalsIgnoreCase("background")) {
        return AppState.APP_STATE_BACKGROUND;
      } else if (appState.equalsIgnoreCase("foreground")) {
        return AppState.APP_STATE_FOREGROUND;
      } else if (appState.equalsIgnoreCase("idle")) {
        return AppState.APP_STATE_IDLE;
      }
    }
    return AppState.APP_STATE_UNSPECIFIED;
  }
}
