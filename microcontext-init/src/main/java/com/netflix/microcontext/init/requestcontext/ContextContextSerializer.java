package com.netflix.microcontext.init.requestcontext;

import com.netflix.microcontext.init.serializers.Serializers;
import com.netflix.server.context.ContextSerializationException;
import com.netflix.server.context.ContextSerializer;
import java.util.Optional;
import netflix.context.Context;

public class ContextContextSerializer implements ContextSerializer<Context> {

  public static final ContextContextSerializer INSTANCE = new ContextContextSerializer();

  private ContextContextSerializer() {}

  @Override
  public int getVersion() {
    return 0;
  }

  @Override
  public String serialize(Context toSerialize) {
    return Serializers.toString(toSerialize);
  }

  @Override
  public Context deserialize(String serialized, int version) throws ContextSerializationException {
    Optional<Context> context = Serializers.fromString(serialized);
    if (context.isPresent()) {
      return context.get();
    } else {
      throw new ContextSerializationException("Could not serialize into Context");
    }
  }
}
