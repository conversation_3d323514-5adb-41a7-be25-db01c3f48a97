package com.netflix.microcontext.init.utils;

import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Spectator;

public class Metrics {

  public static void incrementResultSuccess(Id id) {
    Spectator.globalRegistry().counter(id.withTag("result", "success")).increment();
  }

  public static void incrementResultException(Id id) {
    Spectator.globalRegistry().counter(id.withTag("result", "exception")).increment();
  }
}
