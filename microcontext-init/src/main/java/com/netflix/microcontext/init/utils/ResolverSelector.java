package com.netflix.microcontext.init.utils;

import static com.netflix.microcontext.init.request.InputResolver.source;

import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.request.CookieResolver;
import com.netflix.microcontext.init.request.InputResolver;
import com.netflix.microcontext.init.request.InputResolver.SelectedValue;
import com.netflix.spectator.api.Registry;
import com.netflix.spectator.api.Spectator;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nonnull;

public class ResolverSelector {

  private final Registry registry;

  private static final ResolverSelector INSTANCE = new ResolverSelector();

  public static ResolverSelector instance() {
    return INSTANCE;
  }

  public ResolverSelector() {
    this.registry = Spectator.globalRegistry();
  }

  public ResolverSelector(Registry registry) {
    this.registry = registry;
  }

  public Optional<String> selectValue(
      @Nonnull String field,
      @Nonnull List<String> headerNames,
      @Nonnull HeaderResolver headerResolver,
      @Nonnull List<String> paramNames,
      @Nonnull ParamResolver paramResolver) {
    return select(field, headerNames, headerResolver, paramNames, paramResolver)
        .map(SelectedValue::getValue);
  }

  public Optional<SelectedValue> select(
      @Nonnull String field,
      @Nonnull List<String> headerNames,
      @Nonnull HeaderResolver headerResolver,
      @Nonnull List<String> paramNames,
      @Nonnull ParamResolver paramResolver) {
    Optional<SelectedValue> selected = headerResolver.selectFirst(headerNames);
    if (selected.isPresent()) {
      metric("header", field, selected.get().getName());
      return selected;
    }
    selected = paramResolver.selectFirst(paramNames);
    if (selected.isPresent()) {
      metric("param", field, selected.get().getName());
      return selected;
    }
    metric("none", field, "miss");
    return Optional.empty();
  }

  public Optional<SelectedValue> select(
      @Nonnull String field, @Nonnull SelectorInput... selectorInputs) {
    for (SelectorInput selectorInput : selectorInputs) {
      Optional<SelectedValue> selected =
          selectorInput.inputResolver().selectFirst(selectorInput.names);
      if (selected.isPresent()) {
        metric(selectorInput.type(), field, selected.get().getName());
        return selected;
      }
    }
    metric("none", field, "miss");
    return Optional.empty();
  }

  /** Selects one value from a list of inputs */
  public Optional<String> selectSimple(@Nonnull String field, @Nonnull InputResolver... resolvers) {
    for (InputResolver resolver : resolvers) {
      Optional<SelectedValue> selected = resolver.selectFirst(Collections.singletonList(field));
      if (selected.isPresent()) {
        metric(source(resolver), field, selected.get().getName());
        return selected.map(SelectedValue::getValue);
      }
    }
    metric("none", field, "miss");
    return Optional.empty();
  }

  public static class SelectorInput {
    private final String type;
    private final InputResolver inputResolver;
    private final List<String> names;

    public static SelectorInput create(ParamResolver inputResolver, String... names) {
      return new SelectorInput("param", inputResolver, Arrays.asList(names));
    }

    public static SelectorInput create(HeaderResolver inputResolver, String... names) {
      return new SelectorInput("header", inputResolver, Arrays.asList(names));
    }

    public static SelectorInput create(CookieResolver inputResolver, String... names) {
      return new SelectorInput("cookie", inputResolver, Arrays.asList(names));
    }

    public SelectorInput(String type, InputResolver inputResolver, List<String> names) {
      this.type = type;
      this.inputResolver = inputResolver;
      this.names = names;
    }

    public String type() {
      return type;
    }

    public InputResolver inputResolver() {
      return inputResolver;
    }

    public List<String> names() {
      return names;
    }
  }

  private void metric(String resolver, String field, String selected) {
    registry
        .counter(
            "microcontext.resolver.selected",
            "resolver",
            resolver,
            "field",
            field,
            "selected",
            selected)
        .increment();
  }
}
