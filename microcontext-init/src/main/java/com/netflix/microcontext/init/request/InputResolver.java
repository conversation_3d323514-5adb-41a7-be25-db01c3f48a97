package com.netflix.microcontext.init.request;

import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nonnull;

public interface InputResolver {

  default Optional<String> getFirst(@Nonnull List<String> names) {
    for (String name : names) {
      Optional<String> s = get(name);
      if (s.isPresent() && !s.get().isEmpty()) {
        return s;
      }
    }

    return Optional.empty();
  }

  default Optional<SelectedValue> selectFirst(@Nonnull List<String> names) {
    for (String name : names) {
      if (contains(name)) {
        return get(name).map(value -> new SelectedValue(name, value, source(this)));
      }
    }
    return Optional.empty();
  }

  /**
   * @return an Optional with the first value present if a header exists, empty otherwise
   */
  Optional<String> get(@Nonnull String name);

  /**
   * @return a list of all the values for the header name or an empty list
   */
  @Nonnull
  List<String> getAll(@Nonnull String name);

  /**
   * @return true if a header exists with the parameter name, false otherwise
   */
  boolean contains(@Nonnull String name);

  static String source(InputResolver resolver) {
    if (resolver instanceof HeaderResolver) {
      return "header";
    } else if (resolver instanceof ParamResolver) {
      return "param";
    } else if (resolver instanceof CookieResolver) {
      return "cookie";
    }
    // shouldn't happen but don't error
    return "unknown";
  }

  class SelectedValue {
    private final String name;
    private final String value;
    private final String source;

    public SelectedValue(String name, String value, String source) {
      this.name = name;
      this.value = value;
      this.source = source;
    }

    public String getName() {
      return name;
    }

    public String getValue() {
      return value;
    }

    public String getSource() {
      return source;
    }
  }
}
