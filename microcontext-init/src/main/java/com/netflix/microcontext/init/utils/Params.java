package com.netflix.microcontext.init.utils;

import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.spectator.api.Spectator;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.CheckForNull;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.common.StringList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings("unused")
public class Params {

  public static final Pattern COMMA = Pattern.compile(",");
  public static final Pattern JSON_CHAR = Pattern.compile("[\\[\\]\"]");
  private static final Logger logger = LoggerFactory.getLogger(Params.class);

  /**
   * @param s a comma delimited string
   * @return a list of the substrings removing empty values
   */
  public static Stream<String> split(@CheckForNull String s) {
    if (s == null) {
      return Stream.empty();
    }
    String trim = s.trim();
    // parse if json array
    if (trim.startsWith("[")) {
      Spectator.globalRegistry().counter("microcontext.locale.jsonarray").increment();
      trim = unJson(trim);
    }
    return Arrays.stream(COMMA.split(trim)).map(String::trim).filter(Params::notEmpty);
  }

  public static boolean notEmpty(@Nonnull String s) {
    return !s.isEmpty();
  }

  public static StringList stringList(@Nullable String[] value) {
    if (value == null) {
      return StringList.getDefaultInstance();
    }
    return StringList.newBuilder().addAllValues(Arrays.asList(value)).build();
  }

  public static String unJson(@Nonnull String jsonString) {
    try {
      return JSON_CHAR.matcher(jsonString).replaceAll("");
    } catch (Exception e) {
      logger.error("Could not parse locale", e);
      return "";
    }
  }

  public static StringList stringList(@Nullable Enumeration<String> enumeration) {
    if (enumeration == null) {
      return StringList.getDefaultInstance();
    }
    StringList.Builder builder = StringList.newBuilder();
    while (enumeration.hasMoreElements()) {
      builder.addValues(enumeration.nextElement());
    }
    return builder.build();
  }

  public static Optional<String> firstValue(
      @Nonnull String key, @Nonnull Map<String, StringList> map) {
    return firstValue(map.getOrDefault(key, StringList.getDefaultInstance()));
  }

  public static Optional<String> firstValue(@Nullable StringList stringList) {
    if (stringList == null || stringList.getValuesCount() == 0) {
      return Optional.empty();
    }
    return Optional.of(stringList.getValues(0));
  }

  /**
   * finds the string values for a given header key, splits by comma, trims and returns flattened
   * list of strings
   */
  public static Set<String> flattenedStrings(String key, HeaderResolver headerResolver) {
    return headerResolver.getAll(key).stream().flatMap(Params::split).collect(Collectors.toSet());
  }
}
