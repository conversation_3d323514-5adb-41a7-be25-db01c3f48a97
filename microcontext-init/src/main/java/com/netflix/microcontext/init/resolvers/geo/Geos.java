package com.netflix.microcontext.init.resolvers.geo;

import com.google.type.LatLng;
import com.google.type.LatLng.Builder;
import com.netflix.geoclient.GeoData;
import com.netflix.grpc.shaded.com.google.common.collect.Maps;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.headers.Headers;
import com.netflix.type.protogen.BasicTypes.ISOCountry;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.Models;
import netflix.context.geo.GeoContext;
import netflix.context.geo.IpAddress;
import netflix.context.visit.VisitContext;

public class Geos {

  /** ISO-3166, two-letter code for the country */
  public static final String ATTR_COUNTRY_CODE = "country_code";

  /** ISO-3166, two-letter code for the state, province, or region */
  public static final String ATTR_REGION_CODE = "region_code";

  /** the latitude that IP address maps to */
  static final String ATTR_LAT = "lat";

  /** the longitude that IP address maps to */
  static final String ATTR_LONG = "long";

  /** the city (within a 50-mile radius) */
  public static final String ATTR_CITY = "city";

  public static final String ATTR_IANA_TIMEZONE = "iana_timezone";

  /** the zip code */
  public static final String ATTR_ZIP = "zip";

  // Network attributes
  /** the network that the IP address belongs to */
  public static final String ATTR_NETWORK = "network";

  /** dradis regions per cloud operating mode encoded as a Short */
  public static final String ATTR_DRADIS_REGIONS = "dradisRegions";

  /** dradis site name */
  public static final String ATTR_DRADIS_SITE = "dradisSite";

  /** the IP address related to this response */
  public static final String ATTR_IP_ADDRESS = "ipaddress";

  /** the network connection type the IP address belongs to */
  public static final String ATTR_NETWORK_TYPE = "network_type";

  /** "true" if real_country is mapped to country using OctoberSky (e.g. PR to US) */
  public static final String ATTR_IS_COUNTRY_MAPPED = "is_country_mapped";

  /**
   * The autonomous system number of the request. On the internet, an ISP officially registers an
   * ASN as a form of identification. These numbers are written either as simple integers, or in the
   * form x.y, where x and y are 16-bit numbers. see: <a
   * href="http://tools.ietf.org/html/rfc1930">http://tools.ietf.org/html/rfc1930</a>
   */
  public static final String ATTR_ASNUM = "asnum";

  /** use {@link #ATTR_ASNUM} instead */
  @Deprecated public static final String ATTR_ANUM = "asnum";

  /** domain that the IP address belongs to */
  public static final String ATTR_DOMAIN = "domain";

  /** company that the IP address belongs to */
  public static final String ATTR_COMPANY = "company";

  /**
   * Only populated if this {@link ISOCountry} was overridden, may be useful for clients wanting to
   * log this.
   */
  public static final String ATTR_REAL_COUNTRY = "real_country";

  /**
   * Only populated if this {@link ISOCountry} was overridden, may be useful for clients wanting to
   * log this.
   */
  public static final String ATTR_REAL_IP_ADDRESS = "real_ipaddress";

  /** "true" if this is a fallback */
  public static final String ATTR_IS_FALLBACK = "is_fallback";

  /** "true" if this is an override */
  public static final String ATTR_IS_OVERRIDE = "is_override";

  /** override reason */
  public static final String ATTR_OVERRIDE_REASON = "override_reason";

  /**
   * The presence of this attribute indicates this is a blocked proxy. Consumers doing actual
   * blocking shouldn't care about the contents of the field; just its presence.
   */
  public static final String ATTR_BLOCKED_PROXY = "blocked_proxy";

  private static final Set<String> EXTRA_ATTRIBUTES;

  static {
    EXTRA_ATTRIBUTES = new HashSet<>();
    EXTRA_ATTRIBUTES.add(ATTR_OVERRIDE_REASON);
    EXTRA_ATTRIBUTES.add(ATTR_DRADIS_REGIONS);
    EXTRA_ATTRIBUTES.add(ATTR_DRADIS_SITE);
    EXTRA_ATTRIBUTES.add(ATTR_NETWORK);
    EXTRA_ATTRIBUTES.add(GeoData.ATTR_CITY_KEY);
    EXTRA_ATTRIBUTES.add(GeoData.ATTR_SUBDIVISION_KEY);
    EXTRA_ATTRIBUTES.add(GeoData.ATTR_TIMEZONE);
    EXTRA_ATTRIBUTES.add(GeoData.ATTR_METRO_KEY);
    EXTRA_ATTRIBUTES.add(GeoData.ATTR_NF_IP_PROVIDER);
    EXTRA_ATTRIBUTES.add(GeoData.ATTR_DMA);
  }

  /** crutch to convert the geocontext back to the attribute map during migration */
  @SuppressWarnings("unused")
  public static Map<String, String> attributeMap(@Nonnull GeoContext geo) {
    HashMap<String, String> attributes = Maps.newHashMap();
    attributes.putAll(geo.getExtraAttributesMap());
    attributes.put(ATTR_COUNTRY_CODE, geo.getCountry().getId());
    geo.getOptionalRegionCode().ifPresent(s -> attributes.put(ATTR_REGION_CODE, s));
    geo.getOptionalCity().ifPresent(s -> attributes.put(ATTR_CITY, s));
    if (geo.hasIpAddress()) {
      attributes.put(ATTR_IP_ADDRESS, geo.getIpAddress().getAddress());
    }
    if (geo.hasRealIpAddress()) {
      attributes.put(ATTR_REAL_IP_ADDRESS, geo.getRealIpAddress().getAddress());
    }
    geo.getOptionalAsn().ifPresent(s -> attributes.put(ATTR_ASNUM, s));
    if (geo.hasRealCountry()) {
      attributes.put(ATTR_REAL_COUNTRY, geo.getRealCountry().getId());
    }
    if (geo.getBlockedProxy()) {
      attributes.put(ATTR_BLOCKED_PROXY, "1");
    }
    geo.getOptionalDomain().ifPresent(s -> attributes.put(ATTR_DOMAIN, s));
    if (geo.getOverridden()) {
      attributes.put(ATTR_IS_OVERRIDE, "true");
    }
    if (geo.getCountryMapped()) {
      attributes.put(ATTR_IS_COUNTRY_MAPPED, "true");
    }
    if (geo.getFallback()) {
      attributes.put(ATTR_IS_FALLBACK, "true");
    }
    if (geo.hasCoordinates()) {
      final LatLng coordinates = geo.getCoordinates();
      attributes.put(ATTR_LONG, String.valueOf(coordinates.getLongitude()));
      attributes.put(ATTR_LAT, String.valueOf(coordinates.getLatitude()));
    }
    geo.getOptionalZip().ifPresent(s -> attributes.put(ATTR_ZIP, s));
    geo.getOptionalIanaTimezone().ifPresent(s -> attributes.put(ATTR_IANA_TIMEZONE, s));
    geo.getOptionalCompany().ifPresent(s -> attributes.put(ATTR_COMPANY, s));
    geo.getOptionalNetworkType().ifPresent(s -> attributes.put(ATTR_NETWORK_TYPE, s));
    return attributes;
  }

  public static GeoContext context(@Nonnull Map<String, String> attributes) {
    return context(null, attributes, null);
  }

  public static GeoContext context(
      @Nullable HeaderResolver headers, @Nonnull Map<String, String> attributes) {
    return context(headers, attributes, null);
  }

  public static GeoContext context(
      @Nullable HeaderResolver headers,
      @Nonnull Map<String, String> attributes,
      @Nullable VisitContext visitContext) {
    GeoContext.Builder builder =
        GeoContext.newBuilder()
            .setRegionCode(attributes.get(ATTR_REGION_CODE))
            .setCity(attributes.get(ATTR_CITY))
            .setZip(attributes.get(ATTR_ZIP))
            .setIanaTimezone(attributes.get(ATTR_IANA_TIMEZONE))
            .setAsn(attributes.get(ATTR_ASNUM))
            .setFallback(Boolean.parseBoolean(attributes.get(ATTR_IS_FALLBACK)))
            .setOverridden(Boolean.parseBoolean(attributes.get(ATTR_IS_OVERRIDE)))
            .setDomain(attributes.get(ATTR_DOMAIN))
            .setCompany(attributes.get(ATTR_COMPANY))
            .setNetworkType(attributes.get(ATTR_NETWORK_TYPE))
            .setCountryMapped(Boolean.parseBoolean(attributes.get(ATTR_IS_COUNTRY_MAPPED)));

    // replace value from visit context into geocontext
    if (visitContext != null) {
      visitContext.getOptionalOverrideIanaTimezone().ifPresent(builder::setIanaTimezone);
    }
    // override timezone with header value
    overrideTimezone(headers, builder);

    String ipAddress = attributes.get(ATTR_IP_ADDRESS);
    if (ipAddress != null) {
      builder.setIpAddress(IpAddress.newBuilder().setAddress(ipAddress).build());
    }
    String country = attributes.get(ATTR_COUNTRY_CODE);
    if (country != null) {
      builder.setCountry(Models.country(country));
    }
    String realCountry = attributes.get(ATTR_REAL_COUNTRY);
    if (realCountry != null) {
      builder.setRealCountry(Models.country(realCountry));
    }
    String realIpAddress = attributes.get(ATTR_REAL_IP_ADDRESS);
    if (realIpAddress != null) {
      builder.setRealIpAddress(IpAddress.newBuilder().setAddress(realIpAddress).build());
    }
    if (attributes.containsKey(ATTR_LONG) || attributes.containsKey(ATTR_LAT)) {
      final Builder latLngBuilder = LatLng.newBuilder();
      Optional.ofNullable(attributes.get(ATTR_LONG))
          .flatMap(Geos::safeParseDouble)
          .ifPresent(latLngBuilder::setLongitude);
      Optional.ofNullable(attributes.get(ATTR_LAT))
          .flatMap(Geos::safeParseDouble)
          .ifPresent(latLngBuilder::setLatitude);
      builder.setCoordinates(latLngBuilder);
    }
    if (attributes.containsKey(ATTR_BLOCKED_PROXY)) {
      builder.setBlockedProxy(true);
    }

    // stuff a few things into extra attributes, so we can determine usage
    EXTRA_ATTRIBUTES.forEach(
        attr -> {
          String value = attributes.get(attr);
          if (value != null) {
            builder.putExtraAttributes(attr, value);
          }
        });

    return builder.build();
  }

  private static Optional<Double> safeParseDouble(@Nonnull String value) {
    try {
      return Optional.of(Double.parseDouble(value));
    } catch (NumberFormatException e) {
      return Optional.empty();
    }
  }

  /**
   * We allow clients to override the GeoIP-based timezone ID with their device's local timezone ID
   * in order to provide more accurate timezone-based user experiences, like Live Events.
   */
  public static void overrideTimezone(
      @Nullable HeaderResolver headers, @Nonnull GeoContext.Builder builder) {
    getOverrideTimezoneHeader(headers).ifPresent(builder::setIanaTimezone);
  }

  public static Optional<String> getOverrideTimezoneHeader(@Nullable HeaderResolver headers) {
    if (headers == null) {
      return Optional.empty();
    }
    return headers.get(Headers.TIMEZONEID_OVERRIDE).flatMap(Geos::normalizeZoneId);
  }

  @Nonnull
  public static Optional<String> normalizeZoneId(String zoneId) {
    try {
      return Optional.of(ZoneId.of(zoneId).getId());
    } catch (Exception ignored) {
    }
    return Optional.empty();
  }
}
