package com.netflix.microcontext.init.serializers;

import com.google.protobuf.InvalidProtocolBufferException;
import com.netflix.grpc.shaded.com.google.common.annotations.VisibleForTesting;
import com.netflix.spectator.api.Spectator;
import java.util.Base64;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.Context;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Serializers {

  private static final Logger logger = LoggerFactory.getLogger(Serializers.class);
  private static final String CONTEXT_SERIALIZATION_PROPS_SEPARATOR_REGEX = "\\|";
  private static final String CONTEXT_SERIALIZATION_PROPS_SEPARATOR = "|";
  public static final String version;

  static {
    String v = "unknown";
    try {
      v = Serializers.class.getPackage().getImplementationVersion();
    } catch (Exception e) {
      logger.error("Could not get package version", e);
    }
    version = v;
  }

  public static String toString(@Nonnull Context context) {
    return Base64.getEncoder().encodeToString(context.toByteArray());
  }

  /** Will optionally unwrap the Request Context prefixes from the string prior to parsing */
  public static Optional<Context> fromRawString(@Nullable String s) {
    return Optional.ofNullable(s)
        .map(Serializers::unwrapRequestContext)
        .flatMap(Serializers::parseFrom);
  }

  public static Optional<Context> fromString(@Nullable String s) {
    return Optional.ofNullable(s).flatMap(Serializers::parseFrom);
  }

  private static Optional<Context> parseFrom(@Nonnull String s) {
    try {
      return Optional.ofNullable(Context.parseFrom(Base64.getDecoder().decode(s)));
    } catch (InvalidProtocolBufferException ignore) {
    } catch (Throwable t) {
      logger.error("Could not deserialize string {}", s, t);
      Spectator.globalRegistry()
          .counter("microcontext.serialize.error", "jarVersion", version, "from", "string");
    }
    return Optional.empty();
  }

  public static byte[] toBytes(@Nonnull Context context) {
    return context.toByteArray();
  }

  public static Optional<Context> fromBytes(@Nullable byte[] b) {
    return Optional.ofNullable(b).flatMap(Serializers::parseFrom);
  }

  private static Optional<Context> parseFrom(@Nonnull byte[] b) {
    try {
      return Optional.ofNullable(Context.parseFrom(b));
    } catch (InvalidProtocolBufferException ignore) {
    } catch (Throwable t) {
      logger.error("Could not parse bytes", t);
      Spectator.globalRegistry()
          .counter("microcontext.serialize.error", "jarVersion", version, "from", "bytes");
    }
    return Optional.empty();
  }

  /**
   * Optionally removes the prefixes that are added in request context headers if present, this is a
   * helper for cutting and pasting from debug output
   */
  @VisibleForTesting
  static String unwrapRequestContext(String rawSerializedForm) {
    try {
      String[] properties = rawSerializedForm.split(CONTEXT_SERIALIZATION_PROPS_SEPARATOR_REGEX);
      if (properties.length < 4) {
        return rawSerializedForm;
      }

      if (properties.length > 4) {
        // Serialized data has the separator, so we should concat them again
        StringBuilder dataBuilder = new StringBuilder();
        for (int i = 3; i < properties.length; i++) {
          String fragment = properties[i];
          if (dataBuilder.length() > 0) {
            dataBuilder.append(CONTEXT_SERIALIZATION_PROPS_SEPARATOR);
          }
          dataBuilder.append(fragment);
        }
        return dataBuilder.toString();
      }

      return properties[3];
    } catch (Throwable t) {
      logger.warn("Could not parse microcontext", t);
      return rawSerializedForm;
    }
  }
}
