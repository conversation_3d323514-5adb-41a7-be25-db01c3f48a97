package com.netflix.microcontext.init.resolvers;

import com.google.protobuf.UInt32Value;
import com.netflix.grpc.shaded.com.google.common.annotations.VisibleForTesting;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.headers.Headers;
import com.netflix.microcontext.init.params.ParamNames;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.request.CookieResolver;
import com.netflix.microcontext.init.resolvers.tiers.WebTierResolver;
import com.netflix.microcontext.init.utils.Metrics;
import com.netflix.microcontext.init.utils.Params;
import com.netflix.microcontext.init.utils.ResolverSelector;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Spectator;
import com.netflix.type.protogen.BasicTypes.Locale;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.client.CapabilitiesContext;
import netflix.context.client.ClientContext;
import netflix.context.client.ClientContext.Builder;
import netflix.context.client.FeatureCapability;
import netflix.context.client.FeatureCapabilityUnion;
import netflix.context.client.InteractiveOriginal;
import netflix.context.client.InteractiveOriginalUnion;
import netflix.context.client.LocalizationCapability;
import netflix.context.client.LocalizationCapabilityUnion;
import netflix.context.client.TitleCapability;
import netflix.context.client.TitleCapabilityUnion;
import netflix.context.client.category.AndroidCategoryDetails;
import netflix.context.client.category.BrowserCategoryDetails;
import netflix.context.client.category.ClientCategory;
import netflix.context.client.category.TVCategoryDetails;
import netflix.context.client.flavor.ClientFlavor;
import netflix.context.client.formfactor.ClientFormFactor;
import netflix.context.client.tier.DeviceTier;
import netflix.context.common.Version;
import netflix.context.device.DeviceContext;
import netflix.context.device.DeviceContextOrBuilder;
import netflix.context.visit.WebClientDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ClientResolvers {

  private static final Logger logger = LoggerFactory.getLogger(ClientResolvers.class);

  private static final Map<String, ClientFlavor> clientFlavorCache;
  private static final Map<String, ClientFormFactor> formFactorCache;
  private static final Map<Pattern, ClientFlavor> clientHostFlavorCache;
  private static final Id CLIENT_FLAVOR =
      Spectator.globalRegistry().createId("microcontext.clientflavor.resolve");
  private static final ResolverSelector SELECTOR = new ResolverSelector();
  private static final Id RESOLVER_ID =
      Spectator.globalRegistry().createId("microcontext.resolver.client");
  public static final Counter DT_HEADER =
      Spectator.globalRegistry().counter("microcontext.device.tier.resolve", "source", "header");
  public static final Counter DT_AGENT =
      Spectator.globalRegistry().counter("microcontext.device.tier.resolve", "source", "agent");
  public static final Counter DT_NONE =
      Spectator.globalRegistry().counter("microcontext.device.tier.resolve", "source", "none");

  static {
    Map<String, ClientFlavor> bu = new HashMap<>();
    for (ClientFlavor value : ClientFlavor.values()) {
      // store as lowercase
      bu.put(value.name().toLowerCase(), value);
    }
    bu.put("samurai", ClientFlavor.ANDROID);
    // TODO KK 3/14: Remove when tvui boot client updates dna client to send "darwin"
    bu.put("uibootmember", ClientFlavor.DARWIN);
    clientFlavorCache = Collections.unmodifiableMap(bu);

    Map<String, ClientFormFactor> map = new HashMap<>();
    map.put("phone", ClientFormFactor.PHONE);
    map.put("tablet", ClientFormFactor.TABLET);
    map.put("pad", ClientFormFactor.TABLET);
    formFactorCache = Collections.unmodifiableMap(map);

    // as a UI flavor backup, infer from incoming host
    Map<Pattern, ClientFlavor> hostMap = new HashMap<>();
    Function<String, Pattern> createPattern =
        (hostPrefix) ->
            Pattern.compile(
                String.format("%s.(staging|prod).(ftl|cloud|dradis).netflix.com", hostPrefix),
                Pattern.CASE_INSENSITIVE);
    hostMap.put(createPattern.apply("android(-.+)?.?(ngp)?"), ClientFlavor.ANDROID);
    hostMap.put(createPattern.apply("ios(-.+)?.?(ngp)?"), ClientFlavor.ARGO);
    hostMap.put(createPattern.apply("web(-.+)?"), ClientFlavor.AKIRA);
    hostMap.put(createPattern.apply("nrdp(-.+)?"), ClientFlavor.TV_OTHER);
    clientHostFlavorCache = Collections.unmodifiableMap(hostMap);
  }

  /**
   * @deprecated use {@link #resolve(ClientResolverRequest)}
   */
  @Deprecated
  public static Optional<ClientContext> resolve(HeaderResolver headerResolver) {
    return resolve(ClientResolverRequest.builder().setHeaderResolver(headerResolver).build());
  }

  /**
   * @deprecated use {@link #resolve(ClientResolverRequest)}
   */
  @Deprecated
  public static Optional<ClientContext> resolve(
      HeaderResolver headerResolver, ParamResolver paramResolver) {
    return resolve(
        ClientResolverRequest.builder()
            .setHeaderResolver(headerResolver)
            .setParamResolver(paramResolver)
            .build());
  }

  /**
   * @deprecated use {@link #resolve(ClientResolverRequest)}
   */
  @Deprecated
  public static Optional<ClientContext> resolve(
      @Nonnull HeaderResolver headerResolver,
      @Nonnull ParamResolver paramResolver,
      @Nonnull DeviceContext deviceContext) {
    return resolve(
        ClientResolverRequest.builder()
            .setHeaderResolver(headerResolver)
            .setParamResolver(paramResolver)
            .setDeviceContext(deviceContext)
            .build());
  }

  public static Optional<ClientContext> resolve(
      @Nonnull HeaderResolver headerResolver,
      @Nonnull ParamResolver paramResolver,
      @Nonnull DeviceContextOrBuilder deviceContext,
      @Nonnull WebClientDetails clientDetails) {
    return resolve(
        ClientResolverRequest.builder()
            .setHeaderResolver(headerResolver)
            .setParamResolver(paramResolver)
            .setDeviceContext(deviceContext)
            .setClientDetails(clientDetails)
            .build());
  }

  public static Optional<ClientContext> resolve(
      @Nonnull HeaderResolver headerResolver,
      @Nonnull ParamResolver paramResolver,
      boolean resolveCapabilities,
      @Nonnull DeviceContextOrBuilder deviceContext) {
    return resolve(
        ClientResolverRequest.builder()
            .setHeaderResolver(headerResolver)
            .setParamResolver(paramResolver)
            .setResolveCapabilities(resolveCapabilities)
            .setDeviceContext(deviceContext)
            .build());
  }

  public static Optional<ClientContext> resolve(
      @Nonnull HeaderResolver headerResolver,
      @Nonnull ParamResolver paramResolver,
      boolean resolveCapabilities,
      @Nonnull DeviceContextOrBuilder deviceContext,
      @Nonnull WebClientDetails clientDetails) {
    return resolve(
        ClientResolverRequest.builder()
            .setHeaderResolver(headerResolver)
            .setParamResolver(paramResolver)
            .setResolveCapabilities(resolveCapabilities)
            .setDeviceContext(deviceContext)
            .setClientDetails(clientDetails)
            .build());
  }

  public static Optional<ClientContext> resolve(ClientResolverRequest request) {
    ClientContext.Builder clientBuilder = ClientContext.newBuilder();
    final AtomicBoolean initialized = new AtomicBoolean();

    try {
      final HeaderResolver headerResolver = request.getHeaderResolver();
      final ParamResolver paramResolver = request.getParamResolver();
      Optional<ClientFlavor> clientFlavor =
          headerResolver
              .getFirst(Headers.ALL_UI_FLAVORS)
              .flatMap(ClientResolvers::maybeClientFlavor);
      if (!clientFlavor.isPresent()) {
        clientFlavor =
            headerResolver
                .getFirst(ParamNames.ALL_CLIENT_TYPES)
                .flatMap(ClientResolvers::maybeClientFlavor);
      }
      final Optional<String> host = headerResolver.getFirst(Headers.HOST_HEADERS);
      final Optional<ClientFlavor> device = clientFlavor(request.getDeviceContext());
      if (clientFlavor.isPresent()) {
        Spectator.globalRegistry().counter(CLIENT_FLAVOR.withTag("source", "uiflavor")).increment();
        initialized.compareAndSet(false, true);
        updateBuilder(clientBuilder, clientFlavor.get());
      } else if (device.isPresent() && device.get() != ClientFlavor.UNSPECIFIED) {
        Spectator.globalRegistry().counter(CLIENT_FLAVOR.withTag("source", "device")).increment();
        initialized.compareAndSet(false, true);
        updateBuilder(clientBuilder, device.get());
      } else if (isWebHostGroup(request.getHostGroup())) {
        Spectator.globalRegistry()
            .counter(CLIENT_FLAVOR.withTag("source", "webhostgroup"))
            .increment();
        initialized.compareAndSet(false, true);
        updateBuilder(clientBuilder, ClientFlavor.AKIRA);
      } else if (host.isPresent()) {
        initialized.compareAndSet(false, true);
        final Optional<ClientFlavor> byHost = clientFlavorByHost(host.get());
        if (byHost.isPresent()) {
          Spectator.globalRegistry().counter(CLIENT_FLAVOR.withTag("source", "host")).increment();
          updateBuilder(clientBuilder, byHost.get());
        } else {
          Spectator.globalRegistry()
              .counter(CLIENT_FLAVOR.withTag("source", "default"))
              .increment();
          updateBuilder(clientBuilder, ClientFlavor.UNSPECIFIED);
        }
      }
      // TODO use UserAgent to resolve if clientflavor could not be resolved

      SELECTOR
          .selectValue(
              "appVersion",
              Headers.ALL_APP_VERSIONS,
              headerResolver,
              ParamNames.ALL_APP_VERSIONS,
              paramResolver)
          .map(ClientResolvers::version)
          .map(version -> cleanupAppVersion(version, clientBuilder.getClientCategory()))
          .ifPresent(
              value -> {
                initialized.compareAndSet(false, true);
                clientBuilder.setAppVersion(value);
              });

      Optional<String> osVersion = headerResolver.getFirst(Headers.ALL_OS_VERSIONS);
      if (!osVersion.isPresent()) {
        osVersion = paramResolver.getFirst(ParamNames.ALL_OS_VERSIONS);
      }
      if (!osVersion.isPresent()) {
        osVersion = request.getClientDetails().getOsDetails().getOptionalVersion();
      }

      osVersion
          .map(ClientResolvers::version)
          .ifPresent(
              value -> {
                initialized.compareAndSet(false, true);
                clientBuilder.setOsVersion(value);
              });

      Optional<String> sdkVersion = headerResolver.get(Headers.SDK_VERSION);
      if (!sdkVersion.isPresent()) {
        sdkVersion = paramResolver.getFirst(ParamNames.ALL_SDK_VERSIONS);
      }
      sdkVersion
          .map(ClientResolvers::version)
          .ifPresent(
              value -> {
                initialized.compareAndSet(false, true);
                clientBuilder.setSdkVersion(value);
              });
      Optional<ClientFormFactor> clientFormFactor =
          headerResolver.getFirst(Headers.ALL_FORM_FACTORS).map(ClientResolvers::clientFormFactor);
      if (!clientFormFactor.isPresent()) {
        clientFormFactor = clientFormFactorFromDevice(request.getDeviceContext());
      }
      clientFormFactor.ifPresent(
          value -> {
            initialized.compareAndSet(false, true);
            clientBuilder.setClientFormFactor(value);
          });

      if (request.isResolveCapabilities()) {
        CapabilitiesContext capabilities =
            capabilities(headerResolver, request.getDeviceContext(), clientBuilder);
        if (capabilities != CapabilitiesContext.getDefaultInstance()) {
          initialized.compareAndSet(false, true);
          clientBuilder.setCapabilities(capabilities);
        }
      }
      Optional<String> osName = headerResolver.getFirst(Headers.ALL_OS_NAMES);
      if (!osName.isPresent()) {
        osName = paramResolver.get(ParamNames.OS_NAME);
      }
      if (!osName.isPresent()) {
        osName = request.getClientDetails().getOsDetails().getOptionalFamily();
      }
      osName.ifPresent(
          value -> {
            initialized.compareAndSet(false, true);
            clientBuilder.setOsName(value);
          });

      deviceTier(
              headerResolver,
              paramResolver,
              request.getCookieResolver(),
              request.getClientDetails(),
              request.isDisableWebTier())
          .ifPresent(
              value -> {
                initialized.compareAndSet(false, true);
                clientBuilder.setTier(value);
              });

      final boolean initCategoryDetails =
          categoryDetails(request, headerResolver, paramResolver, clientBuilder);
      if (initCategoryDetails) {
        initialized.compareAndSet(false, true);
      }
      Metrics.incrementResultSuccess(RESOLVER_ID);
    } catch (Exception e) {
      Metrics.incrementResultException(RESOLVER_ID);
      logger.warn("Could not resolve client", e);
    }
    if (initialized.get()) {
      return Optional.of(clientBuilder.build());
    } else {
      return Optional.empty();
    }
  }

  private static Optional<DeviceTier> deviceTier(
      HeaderResolver headerResolver,
      ParamResolver paramResolver,
      CookieResolver cookieResolver,
      WebClientDetails webClientDetails,
      boolean disableWebTier) {
    final Optional<DeviceTier> deviceTier =
        headerResolver.get(Headers.DEVICE_TIER).flatMap(ClientResolvers::convertDeviceTier);
    if (deviceTier.isPresent()) {
      DT_HEADER.increment();
      return deviceTier;
    }
    if (disableWebTier) {
      DT_NONE.increment();
      return Optional.empty();
    }
    final Optional<DeviceTier> webDeviceTier =
        WebTierResolver.resolveDeviceTier(
            headerResolver, paramResolver, cookieResolver, webClientDetails);
    if (webDeviceTier.isPresent()) {
      DT_AGENT.increment();
      return webDeviceTier;
    } else {
      DT_NONE.increment();
      return Optional.empty();
    }
  }

  private static Optional<DeviceTier> convertDeviceTier(@Nonnull String value) {
    if (!value.isEmpty()) {
      switch (value.toUpperCase()) {
        case "1":
        case "INNOVATION":
          return Optional.of(DeviceTier.INNOVATION);
        case "2":
        case "FOLLOWER":
          return Optional.of(DeviceTier.FOLLOWER);
        case "3":
        case "MAINTENANCE":
          return Optional.of(DeviceTier.MAINTENANCE);
        case "4":
        case "UNSUPPORTED":
          return Optional.of(DeviceTier.UNSUPPORTED);
      }
    }
    return Optional.empty();
  }

  private static boolean categoryDetails(
      ClientResolverRequest request,
      HeaderResolver headerResolver,
      ParamResolver paramResolver,
      Builder clientBuilder) {
    final ClientCategory clientCategory = clientBuilder.getClientCategory();

    final Optional<BrowserCategoryDetails> browserCategoryDetails =
        buildBrowserDetails(headerResolver, paramResolver, request.getClientDetails());
    if (clientCategory == ClientCategory.WEB && browserCategoryDetails.isPresent()) {
      clientBuilder.setBrowserDetails(browserCategoryDetails.get());
      return true;
    }

    final Optional<AndroidCategoryDetails> androidCategoryDetails =
        buildAndroidDetails(headerResolver);
    if (clientCategory == ClientCategory.ANDROID && androidCategoryDetails.isPresent()) {
      clientBuilder.setAndroidDetails(androidCategoryDetails.get());
      return true;
    }

    final Optional<TVCategoryDetails> tvCategoryDetails =
        buildTVDetails(headerResolver, paramResolver);
    if (clientCategory == ClientCategory.TV && tvCategoryDetails.isPresent()) {
      clientBuilder.setTvDetails(tvCategoryDetails.get());
      return true;
    }
    return false;
  }

  private static boolean isWebHostGroup(@Nullable String hostGroup) {
    // this includes website and website-nq hosts
    return hostGroup != null && (hostGroup.startsWith("website") || hostGroup.equals("shakti"));
  }

  @VisibleForTesting
  public static Version cleanupAppVersion(Version version, ClientCategory clientCategory) {
    // For TVUI we want to clean up the version string which might include ab strings
    if (clientCategory == ClientCategory.TV) {
      String versionString = version.getVersion();
      if (versionString.contains("nrdjs")) {
        int lastDashIndex = versionString.lastIndexOf('-');
        if (lastDashIndex != -1) {
          String lastChunk = versionString.substring(lastDashIndex + 1);
          if (!lastChunk.contains("nrdjs")) {
            versionString = versionString.substring(0, lastDashIndex);
          }
        }
      }
      return Version.newBuilder().setVersion(versionString).build();
    }
    return version;
  }

  private static Optional<ClientFormFactor> clientFormFactorFromDevice(
      @Nullable DeviceContextOrBuilder deviceContext) {
    if (deviceContext != null) {
      switch (deviceContext.getHardwareMajorCategory()) {
        case MOBILE_TABLET:
        case MOBILE:
          return Optional.of(ClientFormFactor.PHONE);
        case TABLET:
          return Optional.of(ClientFormFactor.TABLET);
      }
    }
    return Optional.empty();
  }

  public static void updateBuilder(@Nonnull Builder clientBuilder, @Nonnull ClientFlavor value) {
    clientBuilder.setClientFlavor(value).setClientCategory(clientCategory(value));
  }

  @Nonnull
  private static Optional<ClientFlavor> clientFlavor(
      @Nullable DeviceContextOrBuilder deviceContext) {
    if (deviceContext != null) {
      switch (deviceContext.getClientPlatform()) {
        case APPLE:
          return Optional.of(ClientFlavor.ARGO);
        case HTML5:
          return Optional.of(ClientFlavor.AKIRA);
        case ANDROID_MOBILE:
          return Optional.of(ClientFlavor.ANDROID);
        case NRDP:
          return Optional.of(ClientFlavor.TV_OTHER);
      }
    }
    return Optional.empty();
  }

  @Nonnull
  private static ClientCategory clientCategory(@Nonnull ClientFlavor clientFlavor) {
    switch (clientFlavor) {
      case AKIRA:
      case FAKIRA:
        return ClientCategory.WEB;
      case ANDROID:
      case TREX:
        return ClientCategory.ANDROID;
      case ARGO:
      case IOS_LEGACY:
      case BUTTERFLY:
        return ClientCategory.IOS;
      case ATV_FUJI:
      case ATV_HOPPER:
      case ATV_ECLIPSE:
        return ClientCategory.ATV;
      case DARWIN:
      case ECLIPSE:
      case TV_OTHER:
        return ClientCategory.TV;
      case WINDOWS_GOTHAM:
      case WINDOWS_PX:
      case WINDOWS_WILDCAT:
        return ClientCategory.WIN;
      default:
        return ClientCategory.UNSPECIFIED;
    }
  }

  private static ClientFormFactor clientFormFactor(@Nonnull String formFactor) {
    return formFactorCache.getOrDefault(formFactor.toLowerCase(), ClientFormFactor.UNSPECIFIED);
  }

  /**
   * @deprecated use {@link #maybeClientFlavor(String)}
   */
  @Deprecated
  public static ClientFlavor clientFlavor(@Nonnull String uiFlavor) {
    return clientFlavorCache.getOrDefault(uiFlavor.toLowerCase(), ClientFlavor.UNSPECIFIED);
  }

  public static Optional<ClientFlavor> maybeClientFlavor(@Nonnull String uiFlavor) {
    return Optional.ofNullable(clientFlavorCache.get(uiFlavor.toLowerCase()));
  }

  public static Optional<ClientFlavor> clientFlavorByHost(@Nullable String host) {
    if (host == null) {
      return Optional.empty();
    }
    return clientHostFlavorCache.entrySet().stream()
        .filter(entry -> entry.getKey().matcher(host).matches())
        .findAny()
        .map(Map.Entry::getValue);
  }

  public static Version version(@Nonnull String v) {
    return Version.newBuilder().setVersion(v).build();
  }

  public static @Nonnull List<Locale> locales(@Nonnull String l) {
    return Params.split(l)
        .map(id -> Locale.newBuilder().setId(id).build())
        .collect(Collectors.toList());
  }

  /**
   * @deprecated use {@link #capabilities(HeaderResolver, DeviceContextOrBuilder, Builder)}
   */
  @Deprecated
  public static CapabilitiesContext capabilities(@Nonnull HeaderResolver headerResolver) {
    return capabilities(headerResolver, null, null);
  }

  public static CapabilitiesContext capabilities(
      @Nonnull HeaderResolver headerResolver,
      @Nullable DeviceContextOrBuilder deviceContext,
      @Nullable Builder clientBuilder) {
    Set<LocalizationCapabilityUnion> localizationFeatures =
        buildLocalizationFeatures(headerResolver);
    Set<InteractiveOriginalUnion> interactiveOriginals = buildInteractiveOriginals(headerResolver);
    Set<TitleCapabilityUnion> titleCapabilities = buildTitleCapabilities(headerResolver);
    Set<FeatureCapabilityUnion> featureCapabilities = buildFeatureCapabilities(headerResolver);

    if (interactiveOriginals.isEmpty()
        && titleCapabilities.isEmpty()
        && localizationFeatures.isEmpty()
        && featureCapabilities.isEmpty()) {
      // Use platform capability resolvers if overrides are not specified
      ClientCategory category =
          Optional.ofNullable(clientBuilder)
              .map(Builder::getClientCategory)
              .orElse(ClientCategory.UNSPECIFIED);
      if (category == ClientCategory.TV) {
        Integer memoryRange =
            Optional.ofNullable(deviceContext)
                .map(DeviceContextOrBuilder::getMemoryRange)
                .map(UInt32Value::getValue)
                .orElse(0);
        return TVCapabilityResolver.getTvCapabilities(memoryRange);
      } else if (category == ClientCategory.ANDROID || category == ClientCategory.IOS) {
        return DefaultPlatformCapabilityResolver.defaultMobileCapabilities();
      } else if (category == ClientCategory.WEB) {
        return DefaultPlatformCapabilityResolver.defaultWebCapabilities();
      }
      return CapabilitiesContext.getDefaultInstance();
    }

    return CapabilitiesContext.newBuilder()
        .addAllLocalizationCapabilities(localizationFeatures)
        .addAllInteractiveOriginals(interactiveOriginals)
        .addAllTitleCapabilities(titleCapabilities)
        .addAllFeatureCapabilities(featureCapabilities)
        .build();
  }

  @VisibleForTesting
  public static Set<FeatureCapabilityUnion> buildFeatureCapabilities(
      HeaderResolver headerResolver) {
    return Params.flattenedStrings(Headers.FEATURE_CAPABILITIES, headerResolver).stream()
        .map(
            str -> {
              FeatureCapability featureCapability = FeatureCapability.valueOf(str);
              FeatureCapabilityUnion.Builder featureCapabilityBldr =
                  FeatureCapabilityUnion.newBuilder();
              if (!featureCapability.equals(FeatureCapability.UNRECOGNIZED)) {
                featureCapabilityBldr.setEnumValue(featureCapability);
              } else {
                featureCapabilityBldr.setUnkownValue(str);
              }
              return featureCapabilityBldr.build();
            })
        .collect(Collectors.toSet());
  }

  @VisibleForTesting
  public static Set<TitleCapabilityUnion> buildTitleCapabilities(HeaderResolver headerResolver) {
    return Params.flattenedStrings(Headers.TITLE_CAPABILITIES, headerResolver).stream()
        .map(
            str -> {
              TitleCapabilityUnion.Builder titleCapabilityUnion = TitleCapabilityUnion.newBuilder();
              try {
                TitleCapability titleCapability = TitleCapability.valueOf(str);
                titleCapabilityUnion.setEnumValue(titleCapability);
              } catch (IllegalArgumentException e) {
                titleCapabilityUnion.setUnkownValue(str);
              }
              return titleCapabilityUnion.build();
            })
        .collect(Collectors.toSet());
  }

  @VisibleForTesting
  public static Set<InteractiveOriginalUnion> buildInteractiveOriginals(
      HeaderResolver headerResolver) {
    return Params.flattenedStrings(Headers.INTERACTIVE_ORIGINALS, headerResolver).stream()
        .map(
            str -> {
              InteractiveOriginalUnion.Builder interactiveOriginalsUnion =
                  InteractiveOriginalUnion.newBuilder();
              try {
                InteractiveOriginal interactiveOriginal = InteractiveOriginal.valueOf(str);
                interactiveOriginalsUnion.setEnumValue(interactiveOriginal);
              } catch (IllegalArgumentException e) {
                interactiveOriginalsUnion.setUnknownValue(str);
              }
              return interactiveOriginalsUnion.build();
            })
        .collect(Collectors.toSet());
  }

  @VisibleForTesting
  public static Set<LocalizationCapabilityUnion> buildLocalizationFeatures(
      HeaderResolver headerResolver) {
    return Params.flattenedStrings(Headers.LOCALIZATION_FEATURES, headerResolver).stream()
        .map(
            str -> {
              LocalizationCapabilityUnion.Builder localizationBldr =
                  LocalizationCapabilityUnion.newBuilder();
              try {
                LocalizationCapability localizationCapability = LocalizationCapability.valueOf(str);
                localizationBldr.setEnumValue(localizationCapability);
              } catch (IllegalArgumentException e) {
                localizationBldr.setUnkownValue(str);
              }
              return localizationBldr.build();
            })
        .collect(Collectors.toSet());
  }

  public static Optional<TVCategoryDetails> buildTVDetails(
      HeaderResolver headerResolver, ParamResolver paramResolver) {
    Optional<String> nrdAppVersion = paramResolver.getFirst(ParamNames.ALL_NRD_APP_VERSIONS);
    if (!nrdAppVersion.isPresent()) {
      nrdAppVersion = headerResolver.get(Headers.SDK_VERSION);
    }

    Optional<String> uiVersion = paramResolver.get(ParamNames.UI_SEM_VER);
    if (!uiVersion.isPresent()) {
      uiVersion = headerResolver.get(Headers.APP_VERSION);
    }

    if (nrdAppVersion.isPresent() || uiVersion.isPresent()) {
      final TVCategoryDetails.Builder builder = TVCategoryDetails.newBuilder();
      nrdAppVersion.map(ClientResolvers::version).ifPresent(builder::setNrdAppVersion);
      uiVersion.map(ClientResolvers::version).ifPresent(builder::setUiVersion);
      return Optional.of(builder.build());
    }
    return Optional.empty();
  }

  public static Optional<BrowserCategoryDetails> buildBrowserDetails(
      HeaderResolver headerResolver,
      ParamResolver paramResolver,
      @Nonnull WebClientDetails clientDetails) {
    final Optional<String> browserName;
    if (headerResolver.contains(Headers.BROWSER_NAME)) {
      browserName = headerResolver.get(Headers.BROWSER_NAME);
    } else if (paramResolver.contains(ParamNames.BROWSER_NAME)) {
      browserName = paramResolver.get(ParamNames.BROWSER_NAME);
    } else {
      browserName = clientDetails.getUserAgentDetails().getOptionalFamily();
    }
    final Optional<String> browserVersion;
    if (headerResolver.contains(Headers.BROWSER_VERSION)) {
      browserVersion = headerResolver.get(Headers.BROWSER_VERSION);
    } else if (paramResolver.contains(ParamNames.BROWSER_VERSION)) {
      browserVersion = paramResolver.get(ParamNames.BROWSER_VERSION);
    } else {
      browserVersion = clientDetails.getUserAgentDetails().getOptionalVersion();
    }

    if (browserName.isPresent() || browserVersion.isPresent()) {
      BrowserCategoryDetails.Builder details = BrowserCategoryDetails.newBuilder();
      browserName.ifPresent(details::setBrowserName);
      browserVersion.ifPresent(details::setBrowserVersion);
      return Optional.of(details.build());
    }
    return Optional.empty();
  }

  public static Optional<AndroidCategoryDetails> buildAndroidDetails(
      HeaderResolver headerResolver) {
    return headerResolver
        .get(Headers.INSTALLER_SOURCE)
        .map(source -> AndroidCategoryDetails.newBuilder().setInstallerSource(source).build());
  }
}
