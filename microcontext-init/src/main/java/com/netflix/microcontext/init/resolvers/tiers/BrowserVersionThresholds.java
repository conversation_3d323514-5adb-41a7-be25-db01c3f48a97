package com.netflix.microcontext.init.resolvers.tiers;

import com.netflix.microcontext.init.utils.ComparableVersion;

/**
 * Encapsulates the version thresholds for a browser or platform.
 *
 * <p>This class holds both the INNOVATION and MAINTENANCE thresholds for a specific browser or
 * platform. These thresholds are used to determine the appropriate device tier (INNOVATION,
 * MAINTENANCE, or UNSUPPORTED) based on the browser's version.
 *
 * <p>The classification logic is as follows:
 *
 * <ul>
 *   <li>Versions >= innovation threshold: INNOVATION tier
 *   <li>Versions >= maintenance threshold but < innovation threshold: MAINTENANCE tier
 *   <li>Versions < maintenance threshold: UNSUPPORTED tier
 * </ul>
 */
public class BrowserVersionThresholds {
  private final ComparableVersion innovation;
  private final ComparableVersion maintenance;

  /**
   * Creates a new BrowserVersionThresholds with the specified innovation and maintenance
   * thresholds.
   *
   * @param innovation The minimum version for INNOVATION tier
   * @param maintenance The minimum version for MAINTENANCE tier
   */
  private BrowserVersionThresholds(ComparableVersion innovation, ComparableVersion maintenance) {
    this.innovation = innovation;
    this.maintenance = maintenance;
  }

  /**
   * Creates a new BrowserVersionThresholds with the specified innovation and maintenance
   * thresholds.
   *
   * @param innovation The minimum version for INNOVATION tier as a string (e.g., "109.0")
   * @param maintenance The minimum version for MAINTENANCE tier as a string (e.g., "106.0")
   */
  public static BrowserVersionThresholds of(String innovation, String maintenance) {
    return new BrowserVersionThresholds(
        ComparableVersion.of(innovation), ComparableVersion.of(maintenance));
  }

  public static BrowserVersionThresholds of(String innovation) {
    return new BrowserVersionThresholds(
        ComparableVersion.of(innovation), ComparableVersion.DEFAULT);
  }

  /**
   * Gets the minimum version for INNOVATION tier.
   *
   * @return The minimum version for INNOVATION tier
   */
  public ComparableVersion getInnovation() {
    return innovation;
  }

  /**
   * Gets the minimum version for MAINTENANCE tier.
   *
   * @return The minimum version for MAINTENANCE tier
   */
  public ComparableVersion getMaintenance() {
    return maintenance;
  }
}
