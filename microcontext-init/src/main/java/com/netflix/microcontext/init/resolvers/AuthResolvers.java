package com.netflix.microcontext.init.resolvers;

import static com.netflix.passport.introspect.PassportIdentityFactory.REQUEST_CONTEXT_PASSPORT_CONTEXT_NAME;

import com.netflix.appregistry.models.protogen.AppType;
import com.netflix.appregistry.models.protogen.AppType.AppTypeName;
import com.netflix.microcontext.init.utils.Passports;
import com.netflix.passport.introspect.PassportDeviceClaims;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.passport.protobuf.Passport;
import com.netflix.server.context.ContextSerializationException;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.type.protogen.BasicTypes.DeviceType;
import java.util.Base64;
import java.util.Optional;
import javax.annotation.CheckForNull;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.auth.Auth;
import netflix.context.auth.AuthContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AuthResolvers {

  private static final Logger logger = LoggerFactory.getLogger(AuthResolvers.class);

  private AuthResolvers() {}

  public static Optional<AuthContext> resolve() {
    return resolve(getPassportIdentity().orElse(null));
  }

  public static Optional<AuthContext> resolve(@CheckForNull PassportIdentity identity) {
    if (identity == null) {
      return Optional.empty();
    }
    return Optional.of(convert(identity));
  }

  public static Optional<AuthContext> resolve(@CheckForNull Passport passport) {
    if (passport != null) {
      return Passports.passportIdentity(passport).map(AuthResolvers::convert);
    }
    return Optional.empty();
  }

  public static AuthContext convert(@Nonnull PassportIdentity identity) {
    AuthContext.Builder builder =
        AuthContext.newBuilder()
            .setVisitorDeviceId(identity.getVisitorDeviceId())
            .setEsn(identity.getEsn());
    identity
        .getDeviceTypeId()
        .map(id -> DeviceType.newBuilder().setId(id).build())
        .ifPresent(builder::setDeviceType);
    getAppId(identity).ifPresent(builder::setAppId);
    getAppType(identity).ifPresent(builder::setAppType);
    currentAuth(identity).ifPresent(builder::setCurrentAuth);
    return builder.build();
  }

  private static Optional<Auth> currentAuth(PassportIdentity identity) {
    return identity
        .getProfileId()
        .map(
            profileId ->
                Auth.newBuilder()
                    .setCustomerId(profileId)
                    .setCustomerGuid(identity.getProfileGuid().orElse(""))
                    .setAccountOwnerId(identity.getAccountId())
                    .setAccountOwnerGuid(identity.getAccountGuid())
                    .build());
  }

  private static String getPassport() {
    try {
      return CurrentRequestContext.get().getContext(REQUEST_CONTEXT_PASSPORT_CONTEXT_NAME);
    } catch (ContextSerializationException e) {
      logger.error("Could not get passport", e);
      return null;
    }
  }

  private static Optional<AppType> getAppType(PassportIdentity passport) {
    if (passport instanceof PassportDeviceClaims) {
      try {
        return ((PassportDeviceClaims) passport)
            .getAppTypeIndex()
            .map(AppTypeName::forNumber)
            .map(n -> AppType.newBuilder().setName(n).build());
      } catch (Throwable t) {
        logger.info("Error parsing apptypeindex", t);
      }
    }
    return Optional.empty();
  }

  public static Optional<String> getAppId(PassportIdentity identity) {
    return identity.getAppId().filter(appId -> !appId.isEmpty());
  }

  public static void setPassport(Passport passport) {
    setPassport(passport, CurrentRequestContext.get());
  }

  public static String encodePassport(@Nonnull Passport passport) {
    return Base64.getUrlEncoder().encodeToString(passport.toByteArray());
  }

  public static Optional<AuthContext> decodePassport(@Nullable String passport) {
    if (passport != null && !passport.isEmpty()) {
      return Passports.passportIdentity(passport).map(AuthResolvers::convert);
    }
    return Optional.empty();
  }

  public static void setPassport(Passport passport, @Nonnull RequestContext requestContext) {
    setPassport(encodePassport(passport), requestContext);
  }

  public static void setPassport(String passport) {
    setPassport(passport, CurrentRequestContext.get());
  }

  public static void setPassport(String passport, @Nonnull RequestContext requestContext) {
    requestContext.addContext(REQUEST_CONTEXT_PASSPORT_CONTEXT_NAME, passport);
  }

  public static void clearPassport() {
    CurrentRequestContext.get().removeContext(REQUEST_CONTEXT_PASSPORT_CONTEXT_NAME);
  }

  private static Optional<PassportIdentity> getPassportIdentity() {
    return Passports.passportIdentity(getPassport());
  }
}
