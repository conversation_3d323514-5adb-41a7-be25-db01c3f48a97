package com.netflix.microcontext.init.headers;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nonnull;
import org.springframework.http.HttpHeaders;

public class SpringHeaderResolver implements HeaderResolver {

  private final HttpHeaders headers;

  public SpringHeaderResolver(@Nonnull HttpHeaders headers) {
    this.headers = headers;
  }

  @Override
  public Optional<String> get(@Nonnull String name) {
    return Optional.ofNullable(headers.getFirst(name));
  }

  @Nonnull
  @Override
  public List<String> getAll(@Nonnull String name) {
    final List<String> value = headers.get(name);
    return value != null ? value : Collections.emptyList();
  }

  @Override
  public boolean contains(@Nonnull String name) {
    return headers.containsKey(name);
  }
}
