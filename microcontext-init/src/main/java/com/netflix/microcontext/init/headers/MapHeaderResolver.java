package com.netflix.microcontext.init.headers;

import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.utils.Params;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Nonnull;
import netflix.context.common.StringList;

public class MapHeaderResolver implements HeaderResolver, ParamResolver {

  private final Map<String, StringList> headersMap;
  private static final HeaderResolver EMPTY = new MapHeaderResolver(Collections.emptyMap());

  private MapHeaderResolver(@Nonnull Map<String, StringList> headersMap) {
    this.headersMap = headersMap;
  }

  public static MapHeaderResolver of(@Nonnull Map<String, StringList> headersMap) {
    return new MapHeaderResolver(headersMap);
  }

  @Override
  public Optional<String> get(@Nonnull String name) {
    return Optional.ofNullable(headersMap.get(name)).flatMap(Params::firstValue);
  }

  @Nonnull
  @Override
  public List<String> getAll(@Nonnull String name) {
    StringList stringList = headersMap.get(name);
    if (stringList == null) {
      return Collections.emptyList();
    }
    return stringList.getValuesList();
  }

  @Override
  public boolean contains(@Nonnull String name) {
    StringList stringList = headersMap.get(name);
    return stringList != null && stringList.getValuesCount() > 0;
  }

  public static HeaderResolver empty() {
    return EMPTY;
  }
}
