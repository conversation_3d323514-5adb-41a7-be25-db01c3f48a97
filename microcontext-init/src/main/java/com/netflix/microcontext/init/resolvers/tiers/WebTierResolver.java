package com.netflix.microcontext.init.resolvers.tiers;

import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.request.CookieResolver;
import com.netflix.microcontext.init.resolvers.VisitResolvers;
import com.netflix.microcontext.init.utils.ComparableVersion;
import com.netflix.microcontext.init.utils.ResolverSelector;
import java.util.Optional;
import java.util.regex.Pattern;
import javax.annotation.Nonnull;
import netflix.context.client.tier.DeviceTier;
import netflix.context.visit.WebClientDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Resolves device tier information based on client details and capabilities.
 *
 * <p>The WebTierResolver is responsible for determining the appropriate device tier (INNOVATION,
 * MAINTENANCE, or UNSUPPORTED) for various client devices and browsers based on their capabilities,
 * versions, and other characteristics. This classification helps in providing the appropriate
 * feature set and experience to different clients.
 *
 * <p>The resolver supports multiple client types:
 *
 * <ul>
 *   <li><b>In-app clients</b>: Native Netflix applications on Android and iOS platforms
 *   <li><b>Mobile browsers</b>: Web browsers on mobile devices (Android, iOS)
 *   <li><b>Desktop browsers</b>: Web browsers on desktop platforms (Chrome, Firefox, Safari, etc.)
 * </ul>
 *
 * <p>For each client type, version thresholds are defined to determine the tier:
 *
 * <ul>
 *   <li><b>INNOVATION</b>: Latest versions with full feature support
 *   <li><b>MAINTENANCE</b>: Older but supported versions with core feature support
 *   <li><b>UNSUPPORTED</b>: Versions too old or platforms not officially supported
 * </ul>
 *
 * <p>The resolution process involves:
 *
 * <ol>
 *   <li>Extracting client information from request parameters, headers, and cookies
 *   <li>Determining the client type (in-app, mobile browser, desktop browser)
 *   <li>Validating version requirements for the specific client type
 *   <li>Applying appropriate tier logic based on version thresholds
 * </ol>
 *
 * <p>This class uses a modular approach with specialized methods for different client types to
 * improve maintainability and make the code easier to update as new browser versions and platforms
 * are released.
 */
public class WebTierResolver {

  private static final Logger logger = LoggerFactory.getLogger(WebTierResolver.class);

  // Regex patterns for user agent parsing
  private static final Pattern ARGO = Pattern.compile("(Argo)/(\\d+) CFNetwork");

  /**
   * Resolves the device tier based on client information extracted from the request.
   *
   * <p>This is the main entry point for device tier resolution. It processes the request
   * parameters, headers, and cookies to extract client information, then determines the appropriate
   * device tier based on the client type and version.
   *
   * <p>The method handles exceptions gracefully, logging errors and returning an empty Optional if
   * tier resolution fails for any reason.
   *
   * @param headerResolver Provides access to HTTP request headers
   * @param paramResolver Provides access to HTTP request parameters
   * @param cookieResolver Provides access to HTTP request cookies
   * @param webClientDetails Contains parsed information about the client's browser and OS
   * @return Optional containing the resolved DeviceTier (INNOVATION, MAINTENANCE, or UNSUPPORTED),
   *     or empty if unable to determine the tier
   */
  public static Optional<DeviceTier> resolveDeviceTier(
      HeaderResolver headerResolver,
      ParamResolver paramResolver,
      CookieResolver cookieResolver,
      WebClientDetails webClientDetails) {
    try {
      return determineDeviceTier(
          determineResolverRequest(
              headerResolver, paramResolver, cookieResolver, webClientDetails));
    } catch (Exception e) {
      logger.error("Error resolving device tier", e);
      return Optional.empty();
    }
  }

  /**
   * Container for client request information needed for tier resolution.
   *
   * <p>This class encapsulates all the information extracted from the client request that is needed
   * to determine the appropriate device tier. It includes flags for in-app status, version
   * information, and browser details.
   */
  static class ResolverRequest {
    private final boolean isInApp;
    private final boolean isInStub;
    private final ComparableVersion inAppSwVersion;
    private final String netflixClientPlatform;
    private final BrowserInfo browserInfo;

    static final ResolverRequest EMPTY = new ResolverRequest();

    private ResolverRequest() {
      this.isInApp = false;
      this.isInStub = false;
      this.inAppSwVersion = ComparableVersion.DEFAULT;
      this.netflixClientPlatform = "";
      this.browserInfo = BrowserInfo.EMPTY;
    }

    ResolverRequest(
        boolean isInApp,
        boolean isInStub,
        @Nonnull ComparableVersion inAppSwVersion,
        String netflixClientPlatform,
        @Nonnull BrowserInfo browserInfo) {
      this.isInApp = isInApp;
      this.isInStub = isInStub;
      this.inAppSwVersion = inAppSwVersion;
      this.netflixClientPlatform = netflixClientPlatform;
      this.browserInfo = browserInfo;
    }
  }

  /**
   * Extracts client information from request parameters and headers.
   *
   * <p>This method processes the HTTP request to extract all relevant client information needed for
   * tier resolution, including:
   *
   * <ul>
   *   <li>ESN (Electronic Serial Number) for device identification
   *   <li>In-app status flags
   *   <li>Software version information
   *   <li>User agent and browser details
   * </ul>
   *
   * <p>The extracted information is encapsulated in a ResolverRequest object for further
   * processing.
   */
  private static ResolverRequest determineResolverRequest(
      HeaderResolver headerResolver,
      ParamResolver paramResolver,
      CookieResolver cookieResolver,
      WebClientDetails webClientDetails) {
    ResolverSelector resolverSelector = ResolverSelector.instance();
    String esnParam =
        resolverSelector.selectSimple("esn", paramResolver, cookieResolver).orElse("");
    String inappParam = paramResolver.get("inapp").orElse(null);
    String inappCookie = cookieResolver.get("inapp").orElse(null);
    String swVersionParam =
        resolverSelector.selectSimple("sw_version", paramResolver, cookieResolver).orElse("0.0");
    String instubParam = paramResolver.get("instub").orElse(null);
    String netflixClientPlatform = paramResolver.get("netflixClientPlatform").orElse("");

    boolean isInApp = (inappParam != null && isValidEsn(esnParam)) || inappCookie != null;
    String userAgent = headerResolver.get(VisitResolvers.USER_AGENT).orElse("");
    BrowserInfo browserInfo = BrowserInfo.parseBrowserInfo(userAgent, webClientDetails);

    boolean isInStub =
        instubParam != null
            && (esnParam.startsWith("NFANDROID1-PRV-S-L3-")
                || esnParam.startsWith("NFANDROIDD-PRV-S-L3-"));

    ComparableVersion inAppSwVersion = parseVersion(swVersionParam);

    return new ResolverRequest(
        isInApp, isInStub, inAppSwVersion, netflixClientPlatform, browserInfo);
  }

  /**
   * Determines the device tier based on client information.
   *
   * <p>This is the core logic for tier resolution. It follows these steps:
   *
   * <ol>
   *   <li>Validates that the request contains necessary information
   *   <li>Checks if version requirements are met for the client type
   *   <li>Identifies the client type (in-app, mobile browser, desktop browser)
   *   <li>Delegates to specialized methods for tier resolution based on client type
   * </ol>
   *
   * <p>The method returns an Optional containing the resolved DeviceTier, or empty if the client
   * type cannot be determined.
   *
   * @param request The ResolverRequest containing client information
   * @return Optional containing the resolved DeviceTier, or empty if unable to determine
   */
  static Optional<DeviceTier> determineDeviceTier(ResolverRequest request) {
    if (request == null || request == ResolverRequest.EMPTY) {
      return Optional.empty();
    }

    BrowserInfo browserInfo = request.browserInfo;

    // Check for valid versions first
    if (shouldCheckOsVersion(request) && browserInfo.getOsVersion().isEmpty()) {
      return Optional.empty();
    }

    if (shouldCheckBrowserVersion(request) && browserInfo.getBrowserVersion().isEmpty()) {
      return Optional.empty();
    }

    // Determine client type and apply appropriate tier logic
    if (isInAppClient(request)) {
      return resolveInAppTier(request);
    } else if (isMobileBrowser(request)) {
      return resolveMobileBrowserTier(request);
    } else if (isDesktopBrowser(request)) {
      return resolveDesktopBrowserTier(request);
    } else if (isUnsupportedClient(request)) {
      return Optional.of(DeviceTier.UNSUPPORTED);
    }

    return Optional.empty();
  }

  /** Checks if OS version validation is required for this client. */
  private static boolean shouldCheckOsVersion(ResolverRequest request) {
    BrowserInfo browser = request.browserInfo;
    boolean isInApp = request.isInApp;

    return (isInApp && (browser.isAndroid() || browser.isIOS() || browser.isWindowsPhone()))
        || (!isInApp && browser.isMobile());
  }

  /** Checks if browser version validation is required for this client. */
  private static boolean shouldCheckBrowserVersion(ResolverRequest request) {
    BrowserInfo browser = request.browserInfo;
    return !request.isInApp && !browser.isMobile();
  }

  /** Determines if the client is an in-app client. */
  private static boolean isInAppClient(ResolverRequest request) {
    BrowserInfo browser = request.browserInfo;
    String userAgent = browser.getUserAgent();

    return request.isInApp || ARGO.matcher(userAgent).find();
  }

  /** Determines if the client is a mobile browser. */
  private static boolean isMobileBrowser(ResolverRequest request) {
    return !request.isInApp && request.browserInfo.isMobile();
  }

  /** Determines if the client is a desktop browser. */
  private static boolean isDesktopBrowser(ResolverRequest request) {
    return !request.isInApp && !request.browserInfo.isMobile();
  }

  /** Determines if the client is an unsupported type. */
  private static boolean isUnsupportedClient(ResolverRequest request) {
    BrowserInfo browser = request.browserInfo;
    String platform = request.netflixClientPlatform;
    boolean isInApp = request.isInApp;

    boolean isWindowsClient = (isInApp && browser.isWindows()) || "windowsWebView".equals(platform);
    boolean isIE = !isInApp && !browser.isMobile() && browser.isIE();

    return isWindowsClient || isIE;
  }

  /** Resolves tier for in-app clients. */
  private static Optional<DeviceTier> resolveInAppTier(ResolverRequest request) {
    BrowserInfo browser = request.browserInfo;
    String userAgent = browser.getUserAgent();

    // Android stub app
    if (request.isInApp && browser.isAndroid() && request.isInStub) {
      ComparableVersion threshold = VersionThresholds.INAPP_ANDROID_STUB;
      return !threshold.isEmpty()
          ? Optional.of(compareVersion(threshold, request.inAppSwVersion))
          : Optional.of(DeviceTier.INNOVATION);
    }

    // Android flagship app
    if (request.isInApp && browser.isAndroid() && !request.isInStub) {
      ComparableVersion threshold = VersionThresholds.INAPP_ANDROID_FLAGSHIP;
      return !threshold.isEmpty()
          ? Optional.of(compareVersion(threshold, request.inAppSwVersion))
          : Optional.of(DeviceTier.INNOVATION);
    }

    // iOS app
    if ((request.isInApp && browser.isIOS()) || ARGO.matcher(userAgent).find()) {
      ComparableVersion threshold = VersionThresholds.INAPP_IOS;
      return !threshold.isEmpty()
          ? Optional.of(compareVersion(threshold, request.inAppSwVersion))
          : Optional.of(DeviceTier.FOLLOWER);
    }

    return Optional.of(DeviceTier.UNSUPPORTED);
  }

  /** Resolves tier for mobile browser clients. */
  private static Optional<DeviceTier> resolveMobileBrowserTier(ResolverRequest request) {
    BrowserInfo browser = request.browserInfo;

    // Android Chrome
    if (browser.isAndroid() && browser.isChrome()) {
      return Optional.of(
          compareVersionWithThresholds(
              browser.getBrowserVersion(), VersionThresholds.ANDROID_CHROME));
    }

    // Android Firefox
    if (browser.isAndroid() && browser.isFirefox()) {
      return Optional.of(
          compareVersionWithThresholds(
              browser.getBrowserVersion(), VersionThresholds.ANDROID_FIREFOX));
    }

    // Generic Android browser
    if (browser.isAndroid()) {
      return Optional.of(
          compareVersionWithThresholds(browser.getOsVersion(), VersionThresholds.ANDROID_BROWSER));
    }

    // iOS browser
    if (browser.isIOS()) {
      return Optional.of(
          compareVersionWithThresholds(browser.getOsVersion(), VersionThresholds.IOS_BROWSER));
    }

    return Optional.of(DeviceTier.UNSUPPORTED);
  }

  /** Resolves tier for desktop browser clients. */
  private static Optional<DeviceTier> resolveDesktopBrowserTier(ResolverRequest request) {
    BrowserInfo browser = request.browserInfo;

    // Safari
    if (browser.isSafari()) {
      return Optional.of(
          compareVersionWithThresholds(
              browser.getBrowserVersion(), VersionThresholds.DESKTOP_SAFARI));
    }

    // Chrome
    if (browser.isChrome()) {
      return Optional.of(
          compareVersionWithThresholds(
              browser.getBrowserVersion(), VersionThresholds.DESKTOP_CHROME));
    }

    // Firefox
    if (browser.isFirefox()) {
      return Optional.of(
          compareVersionWithThresholds(
              browser.getBrowserVersion(), VersionThresholds.DESKTOP_FIREFOX));
    }

    // Opera
    if (browser.isOpera()) {
      return Optional.of(
          compareVersionWithThresholds(
              browser.getBrowserVersion(), VersionThresholds.DESKTOP_OPERA));
    }

    // Edge
    if (browser.isEdgeOSS()) {
      return Optional.of(
          compareVersionWithThresholds(
              browser.getBrowserVersion(), VersionThresholds.DESKTOP_EDGE));
    }

    return Optional.of(DeviceTier.UNSUPPORTED);
  }

  /** Validates if the ESN is in a recognized format. */
  private static boolean isValidEsn(String esn) {
    if (esn == null) return false;
    return esn.startsWith("NFANDROID")
        || esn.startsWith("NFAPPL")
        || esn.startsWith("NFWRTM")
        || esn.startsWith("NFANDROID1-AMAZO")
        || esn.startsWith("NFANDROIDD-AMAZO")
        || esn.startsWith("NFWPH")
        || esn.startsWith("NFUWA");
  }

  /** Safely parses a version string into a ComparableVersion. */
  private static ComparableVersion parseVersion(String version) {
    try {
      return ComparableVersion.of(version);
    } catch (Exception e) {
      return ComparableVersion.DEFAULT;
    }
  }

  /** Compares a version against a threshold to determine tier. */
  private static DeviceTier compareVersion(ComparableVersion threshold, ComparableVersion version) {
    if (version.compareTo(threshold) >= 0) {
      return DeviceTier.INNOVATION;
    } else {
      return DeviceTier.MAINTENANCE;
    }
  }

  /** Compares a version against browser version thresholds to determine tier. */
  static DeviceTier compareVersionWithThresholds(
      ComparableVersion version, BrowserVersionThresholds thresholds) {
    if (version.compareTo(thresholds.getInnovation()) >= 0) {
      return DeviceTier.INNOVATION;
    } else if (!thresholds.getMaintenance().isEmpty()
        && version.compareTo(thresholds.getMaintenance()) >= 0) {
      return DeviceTier.MAINTENANCE;
    } else {
      return DeviceTier.UNSUPPORTED;
    }
  }
}
