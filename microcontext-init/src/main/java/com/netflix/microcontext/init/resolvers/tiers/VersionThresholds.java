package com.netflix.microcontext.init.resolvers.tiers;

import static com.netflix.microcontext.init.resolvers.tiers.BrowserVersionThresholds.of;
import static com.netflix.microcontext.init.utils.ComparableVersion.DEFAULT;

import com.netflix.microcontext.init.utils.ComparableVersion;

/**
 * Defines version thresholds for different platforms and browsers.
 *
 * <p>This class contains constants that define the minimum version requirements for various
 * platforms and browsers to be classified into different device tiers (INNOVATION, MAINTENANCE, or
 * UNSUPPORTED).
 *
 * <p>These thresholds are used by the WebTierResolver to determine the appropriate device tier for
 * client devices based on their capabilities and versions.
 *
 * <h2>How to Update Version Thresholds</h2>
 *
 * <p>This class is designed to be updated by external teams when browser support policies change.
 * Follow these guidelines when updating version thresholds:
 *
 * <h3>Understanding Tier Classification</h3>
 *
 * <ul>
 *   <li><b>INNOVATION tier</b>: Browsers with versions >= INNOVATION threshold receive the latest
 *       features and full support.
 *   <li><b>MAINTENANCE tier</b>: Browsers with versions >= MAINTENANCE threshold but < INNOVATION
 *       threshold receive core functionality but may not get the newest features.
 *   <li><b>UNSUPPORTED tier</b>: Browsers with versions < MAINTENANCE threshold are considered
 *       unsupported.
 * </ul>
 *
 * <p><b>Important</b>: All version comparisons are <b>inclusive</b>, meaning a browser with a
 * version exactly matching a threshold will be included in the higher tier. For example, if Chrome
 * version X exactly matches the DESKTOP_CHROME_INNOVATION threshold, it will be classified as
 * INNOVATION tier.
 *
 * <h3>Updating Process</h3>
 *
 * <ol>
 *   <li>Identify the browser/platform constant that needs updating
 *   <li>Update the version thresholds using the BrowserVersionThresholds.of() method:
 *       <ul>
 *         <li>For major version only: BrowserVersionThresholds.of("majorVersion",
 *             "maintenanceMajorVersion")
 *         <li>For major.minor version: BrowserVersionThresholds.of("majorVersion.minorVersion",
 *             "maintenanceMajorVersion.maintenanceMinorVersion")
 *         <li>For empty/unspecified maintenance thresholds: Use ComparableVersion.EMPTY as the
 *             second parameter
 *       </ul>
 *   <li>Always ensure INNOVATION threshold is >= MAINTENANCE threshold for the same browser
 *   <li>Test changes with different browser versions to verify correct tier assignment
 * </ol>
 *
 * <h3>Special Cases</h3>
 *
 * <ul>
 *   <li>Setting a threshold to ComparableVersion.EMPTY has special meaning:
 *       <ul>
 *         <li>For in-app thresholds: May default to a specific tier (see comments)
 *         <li>For maintenance thresholds: Browsers below innovation threshold will be UNSUPPORTED
 *       </ul>
 *   <li>For new browser support, add new constants following the existing naming pattern
 * </ul>
 */
public class VersionThresholds {

  /*
   * In-app client thresholds
   */

  public static final ComparableVersion INAPP_ANDROID_FLAGSHIP = DEFAULT;
  public static final ComparableVersion INAPP_ANDROID_STUB = DEFAULT;
  public static final ComparableVersion INAPP_IOS = DEFAULT;

  /*
   * Android browser thresholds
   */

  public static final BrowserVersionThresholds ANDROID_CHROME = of("109", "106");
  public static final BrowserVersionThresholds ANDROID_FIREFOX = of("115", "111");
  public static final BrowserVersionThresholds ANDROID_BROWSER = of("5");

  /*
   * iOS browser thresholds
   */

  public static final BrowserVersionThresholds IOS_BROWSER = of("14.5", "12");

  /*
   * Desktop browser thresholds
   */

  public static final BrowserVersionThresholds DESKTOP_SAFARI = of("14.1", "12");
  public static final BrowserVersionThresholds DESKTOP_CHROME = of("109", "106");
  public static final BrowserVersionThresholds DESKTOP_FIREFOX = of("115", "111");
  public static final BrowserVersionThresholds DESKTOP_OPERA = of("109", "92");
  public static final BrowserVersionThresholds DESKTOP_EDGE = of("122");
}
