package com.netflix.microcontext.init.headers;

import static com.netflix.microcontext.init.params.Params.list;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class Headers {

  public static final String MICROCONTEXT_HEADER = "x-netflix.microcontext";

  // Languages and locales
  public static final String LOCALES = "x-netflix.context.locales";
  public static final String LEGACY_LOCALE = "x-netflix.request.client.languages";
  public static final String LEGACY_LANGUAGE = "language";
  public static final List<String> ALL_LOCALES = list(LOCALES, LEGACY_LOCALE, LEGACY_LANGUAGE);
  public static final String ACCEPT_LANGUAGE = "accept-language";

  // UI Flavors
  public static final String UI_FLAVOR = "x-netflix.context.ui-flavor";
  public static final String ANDROID_UI_FLAVOR_LEGACY = "x-netflix.clienttype";
  public static final String ARGO_UI_FLAVOR_LEGACY = "x-netflix.client.type";
  public static final List<String> ALL_UI_FLAVORS =
      list(UI_FLAVOR, ARGO_UI_FLAVOR_LEGACY, ANDROID_UI_FLAVOR_LEGACY);

  // App Versions
  public static final String APP_VERSION = "x-netflix.context.app-version";
  public static final String LEGACY_IOS_APP_VERSION = "x-netflix.client.appversion";
  public static final String LEGACY_ANDROID_APP_VERSION = "x-netflix.appver";
  public static final String TVUI_VERSION = "x-netflix.context.nrd-app-version";
  public static final String WEB_UI_APP_VERSION = "x-netflix.uiversion";
  public static final List<String> ALL_APP_VERSIONS =
      list(
          APP_VERSION,
          LEGACY_IOS_APP_VERSION,
          LEGACY_ANDROID_APP_VERSION,
          TVUI_VERSION,
          WEB_UI_APP_VERSION);

  // OS Versions
  public static final String OS_VERSION = "x-netflix.context.os-version";
  public static final String LEGACY_IOS_OS_VERSION = "x-netflix.client.iosversion";
  public static final String LEGACY_ANDROID_OS_VERSION = "x-netflix.androidapi";
  public static final String LEGACY_WEB_OS_VERSION = "x-netflix.osversion";
  public static final String LEGACY_WEB_OS_VERSION_MISSPELL = "x-xetflix.osversion";
  public static final List<String> ALL_OS_VERSIONS =
      list(
          OS_VERSION,
          LEGACY_IOS_OS_VERSION,
          LEGACY_ANDROID_OS_VERSION,
          LEGACY_WEB_OS_VERSION,
          LEGACY_WEB_OS_VERSION_MISSPELL);

  // OS Names
  public static final String OS_NAME = "x-netflix.context.os-name";
  public static final String LEGACY_WEB_OS_NAME = "x-netflix.osname";
  public static final List<String> ALL_OS_NAMES = list(OS_NAME, LEGACY_WEB_OS_NAME);

  // SDK Versions
  public static final String SDK_VERSION = "x-netflix.context.sdk-version";

  // Form Factors
  public static final String FORM_FACTOR = "x-netflix.context.form-factor";
  public static final String IOS_FORM_FACTOR_LEGACY = "x-netflix.client.idiom";
  public static final String ANDROID_FORM_FACTOR_LEGACY = "x-netflix.deviceformfactor";
  public static final List<String> ALL_FORM_FACTORS =
      list(FORM_FACTOR, IOS_FORM_FACTOR_LEGACY, ANDROID_FORM_FACTOR_LEGACY);

  // Interactive
  public static final String LOCALIZATION_FEATURES = "x-netflix.context.localization-features";
  public static final String INTERACTIVE_ORIGINALS = "x-netflix.context.interactive-originals";
  public static final String FEATURE_CAPABILITIES = "x-netflix.context.feature-capabilities";
  public static final String TITLE_CAPABILITIES = "x-netflix.context.title-capabilities";
  public static final String UX_CONFIG_HEADER = "x-netflix.context.ux-config-id";
  public static final String IXMS_SUPPORTED_EXPERIENCES =
      "x-netflix.context.ixms.supported-experiences";
  public static final String IXMS_SERVICE_VERSION = "x-netflix.context.ixms.service-version";
  public static final String IXMS_UI_SPEC_VERSION = "x-netflix.context.ixms.ui-spec-version";

  // Hosts
  public static final String X_HOST = "x-netflix.client-host";
  public static final String HOST = "host";
  public static List<String> HOST_HEADERS = list(X_HOST, HOST);

  // Browser
  public static final String BROWSER_NAME = "x-netflix.browsername";
  public static final String BROWSER_VERSION = "x-netflix.browserversion";
  public static final List<String> BROWSER_HEADERS = list(BROWSER_NAME, BROWSER_VERSION);

  // Android
  public static final String INSTALLER_SOURCE = "x-netflix.context.android.installer-source";

  // ESN
  public static final String FTL_ESN = "x-netflix.client.ftl.esn";
  public static final String ESN = "x-netflix.esn";
  public static final String CLIENT_ESN = "x-netflix.client.esn";
  public static final List<String> ALL_ESNS = list(ESN, CLIENT_ESN, FTL_ESN);

  // Device Tier
  public static final String DEVICE_TIER = "x-netflix.context.dt";

  // Misc
  public static final String X_FORWARDED_PROTO_HEADER = "x-forwarded-proto";
  public static final String REGION_LOAD_TEST = "x-netflix.region-load-test";
  public static final String PASSPORT = "x-passport";

  // Specified by clients with their local device's timezone id value, to override the GeoIP-based
  // timezone id.
  // This enables us to provide more accurate timezone-based user experiences (i.e. live events).
  public static final String TIMEZONEID_OVERRIDE = "x-netflix.request.client.timezoneid";

  public static List<String> combine(List<String> first, List<String> addAfter) {
    List<String> list = new ArrayList<>(first);
    list.addAll(addAfter);
    return Collections.unmodifiableList(list);
  }
}
