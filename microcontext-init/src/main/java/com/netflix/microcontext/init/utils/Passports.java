package com.netflix.microcontext.init.utils;

import static com.netflix.passport.introspect.PassportIdentityFactory.REQUEST_CONTEXT_PASSPORT_CONTEXT_NAME;

import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.headers.Headers;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.passport.introspect.PassportIdentityFactory;
import com.netflix.passport.introspect.PassportIntrospectionException;
import com.netflix.passport.protobuf.Passport;
import com.netflix.server.context.ContextSerializationException;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Spectator;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;

public class Passports {

  private static final PassportIdentityFactory passportIdentityFactory =
      new PassportIdentityFactory(Spectator.globalRegistry());

  public static Optional<String> passport(
      @Nonnull HeaderResolver resolver, @Nonnull RequestContext requestContext) {
    final Optional<String> header = passport(resolver);
    if (header.isPresent()) {
      return header;
    }
    return passport(requestContext);
  }

  public static Optional<String> passport(@Nonnull HeaderResolver headerResolver) {
    return headerResolver.get(Headers.PASSPORT);
  }

  public static Optional<String> passport(@Nonnull RequestContext requestContext) {
    try {
      return Optional.ofNullable(requestContext.getContext(REQUEST_CONTEXT_PASSPORT_CONTEXT_NAME));
    } catch (ContextSerializationException e) {
      return Optional.empty();
    }
  }

  public static Optional<PassportIdentity> passportIdentity(
      @Nonnull HeaderResolver headerResolver, @Nonnull RequestContext requestContext) {
    return passportIdentity(passport(headerResolver, requestContext).orElse(null));
  }

  public static Optional<PassportIdentity> passportIdentity(@Nullable String passport) {
    if (passport != null && !passport.isEmpty()) {
      try {
        return Optional.of(passportIdentityFactory.createPassportIdentity(passport));
      } catch (PassportIntrospectionException ignored) {
      }
    }
    return Optional.empty();
  }

  public static Optional<PassportIdentity> passportIdentity(@Nullable Passport passport) {
    if (passport != null) {
      try {
        return Optional.of(passportIdentityFactory.createPassportIdentity(passport));
      } catch (PassportIntrospectionException ignored) {
      }
    }
    return Optional.empty();
  }
}
