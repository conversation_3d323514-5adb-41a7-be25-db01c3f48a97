package com.netflix.microcontext.init.utils;

import java.util.Objects;
import javax.annotation.Nonnull;

public class ComparableVersion implements Comparable<ComparableVersion> {
  private final int major;
  private final int minor;
  private final int patch;

  public static final ComparableVersion DEFAULT = new ComparableVersion(0, 0, 0);

  public static ComparableVersion of(int major) {
    return new ComparableVersion(major, 0, 0);
  }

  public static ComparableVersion of(int major, int minor) {
    return new ComparableVersion(major, minor, 0);
  }

  public static ComparableVersion of(int major, int minor, int patch) {
    return new ComparableVersion(major, minor, patch);
  }

  public static ComparableVersion of(String major, String minor, String patch) {
    return new ComparableVersion(safeParseInt(major), safeParseInt(minor), safeParseInt(patch));
  }

  public static ComparableVersion of(String v) {
    if (v == null || v.isEmpty()) {
      return DEFAULT;
    }
    String noDash = v.split("-")[0];
    String[] parts = noDash.split("\\.");
    if (parts.length < 1 || parts.length > 3) {
      return DEFAULT;
    }
    int major = safeParseInt(parts[0]);
    int minor = parts.length >= 2 ? safeParseInt(parts[1]) : 0;
    int patch = parts.length == 3 ? safeParseInt(parts[2]) : 0;
    return new ComparableVersion(major, minor, patch);
  }

  private static int safeParseInt(String s) {
    try {
      final int i = Integer.parseInt(s);
      if (i >= 0) {
        return i;
      }
    } catch (NumberFormatException ignored) {
    }
    return 0;
  }

  private ComparableVersion(int major, int minor, int patch) {
    this.major = major;
    this.minor = minor;
    this.patch = patch;
  }

  public int getMajor() {
    return major;
  }

  public int getMinor() {
    return minor;
  }

  public int getPatch() {
    return patch;
  }

  @Override
  public String toString() {
    return major + "." + minor + "." + patch;
  }

  @Override
  public boolean equals(Object o) {
    if (!(o instanceof ComparableVersion)) return false;
    ComparableVersion that = (ComparableVersion) o;
    return major == that.major && minor == that.minor && patch == that.patch;
  }

  @Override
  public int hashCode() {
    return Objects.hash(major, minor, patch);
  }

  public boolean isEmpty() {
    return this.equals(DEFAULT);
  }

  @Override
  public int compareTo(@Nonnull ComparableVersion other) {
    if (this.equals(other)) {
      return 0;
    }
    if (this.getMajor() > other.getMajor()) {
      return 1;
    } else if (this.getMajor() == other.getMajor()) {
      if (this.getMinor() > other.getMinor()) {
        return 1;
      } else if (this.getMinor() == other.getMinor()) {
        return Integer.compare(this.getPatch(), other.getPatch());
      } else {
        return -1;
      }
    } else {
      return -1;
    }
  }
}
