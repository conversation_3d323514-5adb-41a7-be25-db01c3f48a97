package com.netflix.microcontext.init.resolvers;

import static com.netflix.microcontext.init.headers.Headers.ACCEPT_LANGUAGE;
import static com.netflix.microcontext.init.resolvers.locale.CountryLocaleUtil.convertLocaleList;

import com.ibm.icu.util.NFHttpAdapter;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.headers.Headers;
import com.netflix.microcontext.init.resolvers.locale.CountryLocaleUtil;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Spectator;
import com.netflix.type.ISOCountry;
import com.netflix.type.proto.Locales;
import com.netflix.type.protogen.BasicTypes.Country;
import com.netflix.type.protogen.BasicTypes.Locale;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.CheckForNull;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.Context;
import netflix.context.Models;
import netflix.context.geo.GeoContext;
import netflix.context.user.User;
import netflix.context.user.UserContext;

public class BaseResolvers {

  private static final ConcurrentHashMap<String, Counter> counterCache = new ConcurrentHashMap<>();

  private static final List<Locale> DEFAULT = Collections.singletonList(Locales.toProtobuf("en"));

  public static Context.Builder base(
      @Nonnull Context.Builder builder,
      @Nullable UserContext userContext,
      @Nullable GeoContext geoContext,
      @Nonnull RequestContext requestContext,
      HeaderResolver headerResolver) {
    Country country = BaseResolvers.country(geoContext, userContext, requestContext.getCountry());
    return builder
        .setRequestId(requestContext.getRequestId())
        .setCountry(country)
        .addAllLocales(BaseResolvers.locale(headerResolver, userContext, requestContext, country));
  }

  public static Context.Builder base(
      @Nullable UserContext userContext,
      @Nullable GeoContext geoContext,
      @Nonnull RequestContext requestContext,
      HeaderResolver headerResolver) {
    return base(Context.newBuilder(), userContext, geoContext, requestContext, headerResolver);
  }

  /**
   * Resolves a locale based on the provided inputs
   *
   * <pre>
   * Order of precendence:
   * 1) Headers {@link Headers#ALL_LOCALES}
   * 2) Primary language of the profile {@link User#getPrimaryLanguage()}
   * 3) Accept Language header
   * 4) Request Context {@link RequestContext#getLocaleList()}
   * 5) Country default {@link CountryLocaleUtil#resolveCountry(Country)}
   * 6) Last resort default value of "en"
   * </pre>
   *
   * @return a list of resolved locales
   */
  public static List<Locale> locale(
      HeaderResolver headerResolver,
      @Nullable UserContext userContext,
      RequestContext requestContext,
      Country country) {
    final Optional<List<Locale>> locale = localeHeader(headerResolver);
    if (locale.isPresent()) {
      increment("header");
      return locale.get();
    }
    if (userContext != null
        && userContext.hasCurrentUser()
        && userContext.getCurrentUser().hasPrimaryLanguage()) {
      increment("userContext");
      return Collections.singletonList(
          Locales.toProtobuf(userContext.getCurrentUser().getBoxedPrimaryLanguage()));
    }
    final Optional<List<Locale>> localeAccept = localeAccept(headerResolver);
    if (localeAccept.isPresent()) {
      increment("accept");
      return localeAccept.get();
    }
    final Optional<List<Locale>> localeContext = localeContext(requestContext);
    if (localeContext.isPresent()) {
      increment("context");
      return localeContext.get();
    }
    final Optional<List<Locale>> countryLocale = CountryLocaleUtil.resolveCountry(country);
    if (countryLocale.isPresent()) {
      increment("country");
      return countryLocale.get();
    }
    increment("empty");
    return DEFAULT;
  }

  private static void increment(String source) {
    counterCache.computeIfAbsent(source, BaseResolvers::makeCounter).increment();
  }

  private static Counter makeCounter(String source) {
    return Spectator.globalRegistry().counter("microcontext.locale.resolver", "source", source);
  }

  public static Optional<List<Locale>> localeContext(RequestContext requestContext) {
    return Optional.ofNullable(requestContext.getLocaleList()).map(ClientResolvers::locales);
  }

  public static Optional<List<Locale>> localeAccept(HeaderResolver headerResolver) {
    try {
      return headerResolver
          .get(ACCEPT_LANGUAGE)
          .map(s -> convertLocaleList(NFHttpAdapter.convertHttpAcceptLanguages(s)));
    } catch (Exception e) {
      return Optional.empty();
    }
  }

  public static Optional<List<Locale>> localeHeader(HeaderResolver headerResolver) {
    Optional<String> header = headerResolver.getFirst(Headers.ALL_LOCALES);
    if (header.isPresent()) {
      List<Locale> locales = ClientResolvers.locales(header.get());
      if (!locales.isEmpty()) {
        return Optional.of(locales);
      }
    }
    return Optional.empty();
  }

  @Deprecated
  public static Country country(
      @CheckForNull final GeoContext geoContext, @Nullable ISOCountry requestContext) {
    return country(geoContext, null, requestContext);
  }

  public static Country country(
      @CheckForNull final GeoContext geoContext,
      @Nullable UserContext userContext,
      @Nullable ISOCountry requestContext) {
    if (geoContext != null && !geoContext.getFallback() && geoContext.hasCountry()) {
      return geoContext.getCountry();
    }

    if (requestContext != null) {
      return Models.country(requestContext.getId());
    }

    if (userContext != null && userContext.hasCurrentUser()) {
      final User currentUser = userContext.getCurrentUser();
      if (currentUser.hasRecentViewingCountry()) {
        return currentUser.getRecentViewingCountry();
      }
      if (currentUser.hasSignupCountry()) {
        return currentUser.getSignupCountry();
      }
      if (currentUser.hasRegistrationCountry()) {
        return currentUser.getRegistrationCountry();
      }
    }

    // TODO parse headers for a default
    return Models.country("US");
  }
}
