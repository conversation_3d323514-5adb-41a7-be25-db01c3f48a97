package com.netflix.microcontext.init.utils;

import java.util.Optional;
import javax.annotation.Nullable;

public class NumberUtils {
  public static Optional<Long> parseLong(@Nullable String number) {
    if (number != null && !number.isEmpty()) {
      try {
        return Optional.of(Long.parseLong(number));
      } catch (NumberFormatException ignored) {
      }
    }
    return Optional.empty();
  }
}
