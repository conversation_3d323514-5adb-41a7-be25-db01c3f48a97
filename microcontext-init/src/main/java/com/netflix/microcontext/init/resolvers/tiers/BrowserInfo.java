package com.netflix.microcontext.init.resolvers.tiers;

import com.netflix.microcontext.init.utils.ComparableVersion;
import javax.annotation.Nonnull;
import netflix.context.visit.OSDetails;
import netflix.context.visit.SemVer;
import netflix.context.visit.UserAgentDetails;
import netflix.context.visit.WebClientDetails;

public class BrowserInfo {
  private String userAgent = "";
  private boolean mobile = false;
  private boolean android = false;
  private boolean ios = false;
  private boolean windows = false;
  private boolean windowsPhone = false;
  private boolean chrome = false;
  private boolean firefox = false;
  private boolean safari = false;
  private boolean opera = false;
  private boolean ie = false;
  private boolean edgeOSS = false;
  private ComparableVersion osVersion = ComparableVersion.DEFAULT;
  private ComparableVersion browserVersion = ComparableVersion.DEFAULT;

  public static final BrowserInfo EMPTY = new BrowserInfo();

  private BrowserInfo() {}

  @Nonnull
  public String getUserAgent() {
    return userAgent;
  }

  public boolean isAndroid() {
    return android;
  }

  public boolean isIOS() {
    return ios;
  }

  public ComparableVersion getBrowserVersion() {
    return browserVersion;
  }

  @SuppressWarnings("deprecation")
  private void setBrowserVersion(UserAgentDetails details) {
    if (details.hasSemVer()) {
      this.browserVersion = version(details.getSemVer());
    } else {
      this.browserVersion = ComparableVersion.of(details.getBoxedVersion());
    }
  }

  private static ComparableVersion version(SemVer semVer) {
    return ComparableVersion.of(semVer.getMajor(), semVer.getMinor(), semVer.getPatch());
  }

  public boolean isWindows() {
    return windows;
  }

  public boolean isWindowsPhone() {
    return windowsPhone;
  }

  public boolean isMobile() {
    return mobile;
  }

  public boolean isChrome() {
    return chrome;
  }

  public boolean isFirefox() {
    return firefox;
  }

  public boolean isIE() {
    return ie;
  }

  public boolean isSafari() {
    return safari;
  }

  public boolean isOpera() {
    return opera;
  }

  public ComparableVersion getOsVersion() {
    return osVersion;
  }

  @SuppressWarnings("deprecation")
  private void setOsVersion(OSDetails details) {
    if (details.hasSemVer()) {
      this.osVersion = version(details.getSemVer());
    } else {
      this.osVersion = ComparableVersion.of(details.getBoxedVersion());
    }
  }

  public boolean isEdgeOSS() {
    return edgeOSS;
  }

  public static BrowserInfo parseBrowserInfo(
      @Nonnull String userAgent, @Nonnull WebClientDetails details) {
    BrowserInfo browserInfo = new BrowserInfo();

    browserInfo.userAgent = userAgent;
    browserInfo.setOsVersion(details.getOsDetails());
    browserInfo.setBrowserVersion(details.getUserAgentDetails());

    // Check for mobile platforms
    details
        .getOsDetails()
        .getOptionalFamily()
        .ifPresent(
            osFamily -> {
              if (osFamily.equalsIgnoreCase("Android")) {
                browserInfo.android = true;
                browserInfo.mobile = true;
              } else if (osFamily.equalsIgnoreCase("iOS")) {
                browserInfo.ios = true;
                browserInfo.mobile = true;
              } else if (osFamily.equalsIgnoreCase("Windows Phone")) {
                browserInfo.windowsPhone = true;
                browserInfo.mobile = true;
              }
              // FIXME is windows phone also windows?
              if (osFamily.startsWith("Windows")) {
                browserInfo.windows = true;
              }
            });

    String uaFamily = details.getUserAgentDetails().getOptionalFamily().orElse("");
    // Extract browser information
    if (uaFamily.equalsIgnoreCase("Chrome")) {
      browserInfo.chrome = true;
    } else if (uaFamily.startsWith("Firefox")) {
      browserInfo.firefox = true;
    } else if (uaFamily.equalsIgnoreCase("Safari") && !userAgent.contains("Chrome")) {
      browserInfo.safari = true;
    } else if (uaFamily.equalsIgnoreCase("IE")) {
      browserInfo.ie = true;
    } else if (uaFamily.equalsIgnoreCase("Edge OSS")) {
      browserInfo.edgeOSS = true;
    } else if (uaFamily.equalsIgnoreCase("Opera")) {
      browserInfo.opera = true;
    }

    return browserInfo;
  }
}
