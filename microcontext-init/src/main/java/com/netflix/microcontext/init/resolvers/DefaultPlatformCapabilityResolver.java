package com.netflix.microcontext.init.resolvers;

import java.util.stream.Collectors;
import java.util.stream.Stream;
import netflix.context.client.CapabilitiesContext;
import netflix.context.client.FeatureCapability;
import netflix.context.client.FeatureCapabilityUnion;
import netflix.context.client.LocalizationCapability;
import netflix.context.client.LocalizationCapabilityUnion;
import netflix.context.client.TitleCapability;
import netflix.context.client.TitleCapabilityUnion;

/**
 * CapabilitiesContext is a <b>deprecated</b> way to pass device capability information. <br>
 * In NodeQuark, this was passed via BFF DNA context. This can also be passed via capability
 * headers, e.g. <code>x-netflix.context.title-capabilities</code>.
 *
 * <p>The web capability definition comes from the Shakti BFF (see go/klEanF). The mobile
 * capabilitiy definition matches the current GraphQL implementation.
 */
public class DefaultPlatformCapabilityResolver {
  private static final CapabilitiesContext DEFAULT_WEB_CAPABILITIES =
      CapabilitiesContext.newBuilder()
          .addLocalizationCapabilities(
              LocalizationCapabilityUnion.newBuilder()
                  .setEnumValue(LocalizationCapability.defaultKidsProfile)
                  .build())
          .addAllTitleCapabilities(
              Stream.of(
                      TitleCapability.episodeOrdering,
                      TitleCapability.episodeSkipping,
                      TitleCapability.episodicAvailabilityMessage,
                      TitleCapability.episodicNewBadge,
                      TitleCapability.flattenedShow,
                      TitleCapability.hiddenEpisodeNumbers,
                      TitleCapability.seasonOrdering)
                  .map(
                      capability ->
                          TitleCapabilityUnion.newBuilder().setEnumValue(capability).build())
                  .collect(Collectors.toSet()))
          .addFeatureCapabilities(
              // TODO: sync with Web team on supportsTop10 rule
              FeatureCapabilityUnion.newBuilder()
                  .setEnumValue(FeatureCapability.supportsTop10)
                  .build())
          .addFeatureCapabilities(
              FeatureCapabilityUnion.newBuilder()
                  .setEnumValue(FeatureCapability.supportsTop10Kids)
                  .build())
          .build();

  private static final CapabilitiesContext DEFAULT_MOBILE_CAPABILITIES =
      CapabilitiesContext.newBuilder()
          .addLocalizationCapabilities(
              LocalizationCapabilityUnion.newBuilder()
                  .setEnumValue(LocalizationCapability.defaultKidsProfile)
                  .build())
          .addAllTitleCapabilities(
              Stream.of(
                      TitleCapability.episodeOrdering,
                      TitleCapability.seasonOrdering,
                      TitleCapability.hiddenEpisodeNumbers,
                      TitleCapability.episodicNewBadge,
                      TitleCapability.episodicAvailabilityMessage,
                      TitleCapability.episodeSkipping,
                      TitleCapability.SUPPORTS_ALT_SEASON_LABEL)
                  .map(
                      capability ->
                          TitleCapabilityUnion.newBuilder().setEnumValue(capability).build())
                  .collect(Collectors.toSet()))
          .addFeatureCapabilities(
              FeatureCapabilityUnion.newBuilder()
                  .setEnumValue(FeatureCapability.supportsTop10)
                  .build())
          .addFeatureCapabilities(
              FeatureCapabilityUnion.newBuilder()
                  .setEnumValue(FeatureCapability.supportsTop10Kids)
                  .build())
          .build();

  public static CapabilitiesContext defaultWebCapabilities() {
    return DEFAULT_WEB_CAPABILITIES;
  }

  public static CapabilitiesContext defaultMobileCapabilities() {
    return DEFAULT_MOBILE_CAPABILITIES;
  }
}
