package com.netflix.microcontext;

import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.request.CookieResolver;
import com.netflix.microcontext.init.resolvers.BaseResolvers;
import com.netflix.microcontext.init.resolvers.ClientResolverRequest;
import com.netflix.microcontext.init.resolvers.ClientResolvers;
import com.netflix.microcontext.init.resolvers.GeoResolvers;
import com.netflix.microcontext.init.resolvers.UserResolvers;
import com.netflix.microcontext.init.resolvers.VideoResolvers;
import com.netflix.microcontext.init.utils.Metrics;
import com.netflix.microcontext.init.utils.Passports;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Spectator;
import com.netflix.type.ISOCountry;
import java.util.Map;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.Context;
import netflix.context.client.ClientContext;
import netflix.context.device.DeviceContext;
import netflix.context.geo.GeoContext;
import netflix.context.user.UserContext;
import netflix.context.visit.VisitContext;
import netflix.context.visit.WebClientDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MicrocontextInitializer {

  private static final Logger logger = LoggerFactory.getLogger(MicrocontextInitializer.class);

  private static final Id INIT_ID = Spectator.globalRegistry().createId("microcontext.init");

  public static Context init(
      @Nonnull HeaderResolver headerResolver,
      @Nonnull ParamResolver paramResolver,
      @Nonnull RequestContext requestContext) {
    return init(
        headerResolver,
        paramResolver,
        CookieResolver.EMPTY,
        Passports.passportIdentity(headerResolver, requestContext).orElse(null),
        GeoResolvers.geoAttributes(requestContext),
        null,
        requestContext,
        null,
        null,
        null,
        null,
        false,
        false);
  }

  @Nonnull
  public static Context init(
      @Nonnull HeaderResolver headerResolver,
      @Nonnull ParamResolver paramResolver,
      @Nonnull CookieResolver cookieResolver,
      @Nullable PassportIdentity passportIdentity,
      @Nullable Map<String, String> geoAttributes,
      @Nullable ISOCountry signupCountry,
      @Nonnull RequestContext requestContext,
      @Nullable DeviceContext deviceContext,
      @Nullable VisitContext visitContext,
      @Nullable UserContext userContext,
      @Nullable String hostGroup,
      boolean setUser,
      boolean disableWebTier) {
    return init(
        headerResolver,
        passportIdentity,
        GeoResolvers.resolve(
            headerResolver,
            geoAttributes,
            GeoResolvers.lastResortCountry(userContext, signupCountry),
            visitContext),
        requestContext,
        deviceContext,
        visitContext,
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(headerResolver)
                    .setParamResolver(paramResolver)
                    .setDeviceContext(deviceContext)
                    .setClientDetails(
                        visitContext == null
                            ? WebClientDetails.getDefaultInstance()
                            : visitContext.getUserAgent().getClientDetails())
                    .setHostGroup(hostGroup)
                    .setCookieResolver(cookieResolver)
                    .setDisableWebTier(disableWebTier)
                    .build())
            .orElse(null),
        userContext,
        setUser);
  }

  @Nonnull
  public static Context init(
      @Nonnull HeaderResolver headerResolver,
      @Nullable PassportIdentity passportIdentity,
      @Nullable GeoContext geoContext,
      @Nonnull RequestContext requestContext,
      @Nullable DeviceContext deviceContext,
      @Nullable VisitContext visitContext,
      @Nullable ClientContext clientContext,
      @Nullable final UserContext userContext,
      boolean setUser) {
    final UserContext resolvedUser =
        userContext == null ? UserResolvers.requestContext().orElse(null) : userContext;
    if (logger.isDebugEnabled()) {
      logger.debug(" Passport:{}", passportIdentity);
      logger.debug(" Geo     :{}", geoContext);
      logger.debug(" Device  :{}", deviceContext);
      logger.debug(" Visit   :{}", visitContext);
      logger.debug(" User    :{}", resolvedUser);
    }

    try {
      Context.Builder builder =
          BaseResolvers.base(resolvedUser, geoContext, requestContext, headerResolver);
      if (visitContext != null) {
        builder.setVisit(visitContext);
      }
      if (deviceContext != null) {
        builder.setDevice(deviceContext);
      }
      if (clientContext != null) {
        builder.setClient(clientContext);
      }
      VideoResolvers.resolve(requestContext).ifPresent(builder::setVideo);
      if (resolvedUser != null && setUser) {
        if (passportIdentity != null
            && passportIdentity.getProfileId().isPresent()
            && resolvedUser.getCurrentUser().getId() == passportIdentity.getProfileId().get()) {
          builder.setUser(resolvedUser);
        } else {
          logger.info("Mismatch between user {} and passport {}", resolvedUser, passportIdentity);
        }
      }
      if (logger.isDebugEnabled()) {
        logger.debug(" Resolved Context {}", builder);
      }
      Metrics.incrementResultSuccess(INIT_ID);
      return builder.build();
    } catch (Exception e) {
      logger.error("Error creating microcontext", e);
      Metrics.incrementResultException(INIT_ID);
      return Context.getDefaultInstance();
    }
  }

  public static Context init() {
    return init(
        HeaderResolver.EMPTY,
        ParamResolver.EMPTY,
        CookieResolver.EMPTY,
        null,
        null,
        null,
        CurrentRequestContext.get(),
        null,
        null,
        null,
        null,
        false,
        false);
  }
}
