package com.netflix.microcontext.resolver.user;

import com.google.common.base.Strings;
import com.netflix.membership.MembershipStatus;
import com.netflix.subscriber.types.protogen.Membership;
import com.netflix.subscriber.types.protogen.Tester;
import com.netflix.subscriber.types.protogen.Tester.Types;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import javax.annotation.Nonnull;
import netflix.context.Models;
import netflix.context.user.User;
import netflix.context.user.UserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UserConverter {

  private static final Logger logger = LoggerFactory.getLogger(UserConverter.class);

  private UserConverter() {}

  @Nonnull
  public static UserContext userContext(@Nonnull AccountProfileRemote account) {
    return UserContext.newBuilder().setCurrentUser(UserConverter.convert(account)).build();
  }

  public static User.Builder convert(AccountProfileRemote account) {
    final User.Builder builder = User.newBuilder();
    try {
      builder
          .setExperienceType(account.getExperienceTypeEnum())
          .setMembershipStatus(account.getMembershipStatusEnum())
          .setIsTester(account.getTesterFlagsList().contains(Tester.Types.TESTER))
          .setHasProfileLockPinEnabled(
              account.getOptionalYouthMaturityPinEnabled().orElse(account.hasProfileAccessPin()))
          .setIsPinEnabled(account.getOptionalYouthMaturityPinEnabled().orElse(false))
          .setFallback(account.getOptionalIsFallback().orElse(false))
          .setSopType(account.getSopType());

      if (account.hasProfileCreationTime()) {
        builder.setProfileCreationTime(account.getProfileCreationTime());
      }

      account.getOptionalProfileId().ifPresent(builder::setId);
      account.getOptionalProfileGuid().ifPresent(builder::setGuid);
      account.getOptionalAccountOwnerId().ifPresent(builder::setOwnerId);
      account.getOptionalAccountOwnerGuid().ifPresent(builder::setOwnerGuid);
      account.getOptionalUcid().ifPresent(builder::setUcid);

      Membership.Status membershipStatusEnum = account.getMembershipStatusEnum();
      MembershipStatus membershipStatus = MembershipStatus.fromProto(membershipStatusEnum);
      builder.setCurrentMember(membershipStatus.isCurrentMember());
      builder.setActiveOrHold(membershipStatus.isActiveOrOnHold());
      account
          .getOptionalCountryOfSignup()
          .map(Models::country)
          .ifPresent(builder::setSignupCountry);
      account
          .getOptionalCountryOfRegistration()
          .map(Models::country)
          .ifPresent(builder::setRegistrationCountry);
      account
          .getOptionalRecentViewingCountry()
          .map(Models::country)
          .ifPresent(builder::setRecentViewingCountry);
      account.getOptionalPrimaryLang().ifPresent(builder::setPrimaryLanguage);
      account.getOptionalMaturityLevel().ifPresent(builder::setMaturityLevel);
      account.getOptionalProfileName().ifPresent(builder::setProfileName);
      account.getOptionalPlanId().ifPresent(builder::setPlanId);
      account.getOptionalHasAutoPlayback().ifPresent(builder::setIsAutoplayEnabled);
    } catch (Throwable t) {
      logger.error("Could not convert user {}", account, t);
    }
    return builder;
  }

  public static AccountProfileRemote convert(User user) {
    AccountProfileRemote.Builder builder =
        AccountProfileRemote.newBuilder()
            .setExperienceTypeEnum(user.getExperienceType())
            .setExperienceType(user.getExperienceType().name())
            .setMembershipStatusEnum(user.getMembershipStatus())
            .setMembershipStatus(user.getMembershipStatus().name())
            .setYouthMaturityPinEnabled(
                user.getIsPinEnabled() || user.getHasProfileLockPinEnabled())
            .setUcid(user.getBoxedUcid())
            .setIsFallback(user.getFallback())
            .setSopType(user.getSopType());

    // is tester
    if (user.getIsTester()) {
      builder.addTesterFlags(Types.TESTER);
    }

    if (user.hasProfileCreationTime()) {
      builder.setProfileCreationTime(user.getProfileCreationTime());
    }

    builder.setProfileId(user.getId());
    if (!Strings.isNullOrEmpty(user.getGuid())) {
      builder.setProfileGuid(user.getGuid());
    }
    user.getOptionalOwnerId().ifPresent(builder::setAccountOwnerId);
    user.getOptionalOwnerGuid().ifPresent(builder::setAccountOwnerGuid);

    if (user.hasSignupCountry()) {
      builder.setCountryOfSignup(user.getSignupCountry().getId());
    }
    if (user.hasRegistrationCountry()) {
      builder.setCountryOfRegistration(user.getRegistrationCountry().getId());
    }
    if (user.hasRecentViewingCountry()) {
      builder.setRecentViewingCountry(user.getRecentViewingCountry().getId());
    }
    user.getOptionalPrimaryLanguage().ifPresent(builder::setPrimaryLang);
    user.getOptionalMaturityLevel().ifPresent(builder::setMaturityLevel);
    user.getOptionalProfileName().ifPresent(builder::setProfileName);
    user.getOptionalPlanId().ifPresent(builder::setPlanId);
    builder.setHasAutoPlayback(user.getIsAutoplayEnabled());
    return builder.build();
  }

  public static UserContext updateUser(UserContext userContext) {
    // this will update a few fields such as activeorhold and currentmember
    return UserConverter.userContext(UserConverter.convert(userContext.getCurrentUser()));
  }
}
