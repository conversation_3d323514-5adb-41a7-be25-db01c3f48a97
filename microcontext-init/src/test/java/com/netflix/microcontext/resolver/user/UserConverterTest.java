package com.netflix.microcontext.resolver.user;

import static org.junit.Assert.*;

import com.netflix.subscriber.types.protogen.Membership.Status;
import com.netflix.subscriberservice.protogen.AccountProfileRemote;
import com.netflix.type.protogen.BasicTypes.Country;
import netflix.context.user.User;
import netflix.context.user.UserContext;
import org.junit.Test;

public class UserConverterTest {

  @Test
  public void testConvert() {
    final User user =
        User.newBuilder()
            .setId(123)
            .setIsPinEnabled(true)
            .setMaturityLevel(1000000)
            .setMembershipStatus(Status.CURRENT_MEMBER)
            .setRegistrationCountry(Country.newBuilder().setId("US"))
            .build();
    final AccountProfileRemote converted = UserConverter.convert(user);
    assertEquals(user.getId(), converted.getProfileId().getValue());
    assertFalse(converted.hasProfileGuid());
    assertFalse(converted.hasAccountOwnerId());
    assertFalse(converted.hasAccountOwnerGuid());
    assertTrue(converted.getBoxedYouthMaturityPinEnabled());
    assertTrue(converted.hasMaturityLevel());
    assertTrue(converted.hasMembershipStatus());
    assertEquals(Status.CURRENT_MEMBER, converted.getMembershipStatusEnum());
    assertEquals("US", converted.getBoxedCountryOfRegistration());
    assertEquals(1000000, converted.getBoxedMaturityLevel().intValue());
    final User roundtrip = UserConverter.userContext(converted).getCurrentUser();
    assertEquals(123, roundtrip.getId());
    assertTrue(roundtrip.getGuid().isEmpty());
    assertFalse(roundtrip.hasOwnerId());
    assertFalse(roundtrip.hasOwnerGuid());
    assertTrue(roundtrip.getIsPinEnabled());
    assertTrue(roundtrip.hasMaturityLevel());
    assertEquals(1000000, roundtrip.getBoxedMaturityLevel().intValue());
    assertFalse((roundtrip.hasUcid()));
    assertFalse(roundtrip.hasProfileCreationTime());
    assertTrue(roundtrip.getActiveOrHold());
    assertEquals("US", roundtrip.getRegistrationCountry().getId());
  }

  @Test
  public void testUpdateUser() {
    final UserContext userContext =
        UserContext.newBuilder()
            .setCurrentUser(User.newBuilder().setMembershipStatus(Status.CURRENT_MEMBER).build())
            .build();
    final UserContext updated = UserConverter.updateUser(userContext);
    assertEquals(Status.CURRENT_MEMBER, updated.getCurrentUser().getMembershipStatus());
    assertTrue(updated.getCurrentUser().getCurrentMember());
    assertTrue(updated.getCurrentUser().getActiveOrHold());
  }
}
