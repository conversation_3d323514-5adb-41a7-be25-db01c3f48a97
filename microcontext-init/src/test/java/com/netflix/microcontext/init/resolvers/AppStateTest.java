package com.netflix.microcontext.init.resolvers;

import static org.junit.Assert.*;

import netflix.context.visit.AppState;
import org.junit.Test;

public class AppStateTest {

  @Test
  public void parseBad3() {
    assertEquals(AppState.APP_STATE_UNSPECIFIED, VisitResolvers.appState("notjson"));
  }

  @Test
  public void parse() {
    String header = "{\"appstate\":\"background\",\"reason\":\"unknown\"}";
    AppState value = VisitResolvers.appState(header);
    assertEquals(AppState.APP_STATE_BACKGROUND, value);
  }

  @Test
  public void parseDifferent() {
    String header = "{\"appState\":\"background\",\"reason\":\"unknown\"}";
    AppState value = VisitResolvers.appState(header);
    assertEquals(AppState.APP_STATE_BACKGROUND, value);
  }

  @Test
  public void parseDifferent2() {
    String header = "{\"appState\":\"Background\",\"reason\":\"unknown\"}";
    AppState value = VisitResolvers.appState(header);
    assertEquals(AppState.APP_STATE_BACKGROUND, value);
  }

  @Test
  public void parseDifferent3() {
    String header = "{\"appState\":\"\",\"reason\":\"unknown\"}";
    AppState value = VisitResolvers.appState(header);
    assertEquals(AppState.APP_STATE_UNSPECIFIED, value);
  }

  @Test
  public void parseBadHeader() {
    String header = "{\"noasfsappState\":\"background\",\"reason\":\"unknown\"}";
    AppState value = VisitResolvers.appState(header);
    assertEquals(AppState.APP_STATE_UNSPECIFIED, value);
  }
}
