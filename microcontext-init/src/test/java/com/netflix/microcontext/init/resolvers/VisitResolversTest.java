package com.netflix.microcontext.init.resolvers;

import static junit.framework.TestCase.assertTrue;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

import com.netflix.microcontext.init.headers.MapHeaderResolver;
import com.netflix.server.context.CurrentRequestContext;
import java.util.Collections;
import java.util.Map;
import netflix.context.common.StringList;
import netflix.context.visit.AppState;
import netflix.context.visit.RequestPriority;
import netflix.context.visit.UserAgent;
import netflix.context.visit.VisitContext;
import org.junit.Test;

public class VisitResolversTest {

  @Test
  public void visitWithRequestContext() {
    RequestPriority priority = RequestPriority.getDefaultInstance();
    VisitContext context =
        VisitResolvers.resolve(MapHeaderResolver.empty(), CurrentRequestContext.get(), priority);
    assertEquals(priority, context.getPriority());
  }

  @Test
  public void visitWithNoRequestContext() {
    VisitContext context = VisitResolvers.resolve(MapHeaderResolver.empty());
    assertFalse(context.hasPriority());
  }

  @Test
  public void testUserAgentEmpty() {
    VisitContext context = VisitResolvers.resolve(MapHeaderResolver.empty());
    assertFalse(context.hasUserAgent());
  }

  @Test
  public void testUserAgent() {
    Map<String, StringList> stringsMap =
        Collections.singletonMap(
            VisitResolvers.USER_AGENT, StringList.newBuilder().addValues("Mozilla/5.0").build());

    VisitContext context = VisitResolvers.resolve(MapHeaderResolver.of(stringsMap));
    assertTrue(context.hasUserAgent());
    assertEquals("Mozilla/5.0", context.getUserAgent().getValue());
  }

  @Test
  public void testUserAgentWithParameters() {
    Map<String, StringList> stringsMap =
        Collections.singletonMap(
            VisitResolvers.USER_AGENT, StringList.newBuilder().addValues("Mozilla/5.0").build());

    VisitContext context =
        VisitResolvers.resolve(
            MapHeaderResolver.of(stringsMap), CurrentRequestContext.get(), null, null);
    assertTrue(context.hasUserAgent());
    assertEquals("Mozilla/5.0", context.getUserAgent().getValue());
  }

  @Test
  public void testUserAgentWithParametersDefault() {
    Map<String, StringList> stringsMap =
        Collections.singletonMap(
            VisitResolvers.USER_AGENT, StringList.newBuilder().addValues("Mozilla/5.0").build());

    VisitContext context =
        VisitResolvers.resolve(
            MapHeaderResolver.of(stringsMap),
            CurrentRequestContext.get(),
            null,
            UserAgent.getDefaultInstance());
    assertTrue(context.hasUserAgent());
    assertEquals("Mozilla/5.0", context.getUserAgent().getValue());
  }

  @Test
  public void testDisplayName() {
    assertEquals(
        "Given AppState.APP_STATE_FOREGROUND, expect 'foreground'",
        "foreground",
        VisitResolvers.displayName(AppState.APP_STATE_FOREGROUND));
    assertEquals(
        "Given AppState.APP_STATE_BACKGROUND, expect 'background'",
        "background",
        VisitResolvers.displayName(AppState.APP_STATE_BACKGROUND));
    assertEquals(
        "Given AppState.APP_STATE_IDLE, expect 'idle'",
        "idle",
        VisitResolvers.displayName(AppState.APP_STATE_IDLE));
    assertEquals(
        "Given AppState.APP_STATE_UNKNOWN, expect 'unknown'",
        "unknown",
        VisitResolvers.displayName(AppState.APP_STATE_UNKNOWN));
    assertEquals(
        "Given AppState.APP_STATE_UNSPECIFIED, expect 'unspecified'",
        "unspecified",
        VisitResolvers.displayName(AppState.APP_STATE_UNSPECIFIED));
  }
}
