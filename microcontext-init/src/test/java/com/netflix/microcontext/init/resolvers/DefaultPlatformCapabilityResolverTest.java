package com.netflix.microcontext.init.resolvers;

import static org.junit.jupiter.api.Assertions.assertTrue;

import netflix.context.client.CapabilitiesContext;
import netflix.context.client.FeatureCapability;
import netflix.context.client.LocalizationCapability;
import netflix.context.client.TitleCapability;
import org.junit.jupiter.api.Test;

public class DefaultPlatformCapabilityResolverTest {

  @Test
  public void testDefaultWebCapabilities() {
    CapabilitiesContext webCapabilities =
        DefaultPlatformCapabilityResolver.defaultWebCapabilities();

    // Check localization capabilities
    assertTrue(
        webCapabilities.getLocalizationCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == LocalizationCapability.defaultKidsProfile));

    // Check title capabilities
    assertTrue(
        webCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodeOrdering));
    assertTrue(
        webCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodeSkipping));
    assertTrue(
        webCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodicAvailabilityMessage));
    assertTrue(
        webCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodicNewBadge));
    assertTrue(
        webCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.flattenedShow));
    assertTrue(
        webCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.hiddenEpisodeNumbers));
    assertTrue(
        webCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.seasonOrdering));

    // Check feature capabilities
    assertTrue(
        webCapabilities.getFeatureCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == FeatureCapability.supportsTop10));
    assertTrue(
        webCapabilities.getFeatureCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == FeatureCapability.supportsTop10Kids));
  }

  @Test
  public void testDefaultMobileCapabilities() {
    CapabilitiesContext mobileCapabilities =
        DefaultPlatformCapabilityResolver.defaultMobileCapabilities();

    // Check localization capabilities
    assertTrue(
        mobileCapabilities.getLocalizationCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == LocalizationCapability.defaultKidsProfile));

    // Check title capabilities
    assertTrue(
        mobileCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodeOrdering));
    assertTrue(
        mobileCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.seasonOrdering));
    assertTrue(
        mobileCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.hiddenEpisodeNumbers));
    assertTrue(
        mobileCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodicNewBadge));
    assertTrue(
        mobileCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodicAvailabilityMessage));
    assertTrue(
        mobileCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodeSkipping));
    assertTrue(
        mobileCapabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.SUPPORTS_ALT_SEASON_LABEL));

    // Check feature capabilities
    assertTrue(
        mobileCapabilities.getFeatureCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == FeatureCapability.supportsTop10));
    assertTrue(
        mobileCapabilities.getFeatureCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == FeatureCapability.supportsTop10Kids));
  }
}
