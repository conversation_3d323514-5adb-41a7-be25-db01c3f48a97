package com.netflix.microcontext.init.resolvers;

import static org.junit.Assert.*;

import com.netflix.server.context.RequestContext;
import java.util.Optional;
import netflix.context.video.VideoContext;
import org.junit.Test;

public class VideoResolversTest {

  @Test
  public void testResolveEmpty() {
    RequestContext requestContext = new RequestContext((String) null);
    final Optional<VideoContext> videoContext = VideoResolvers.resolve(requestContext);
    assertFalse(videoContext.isPresent());
  }

  @Test
  public void testResolveNoData() {
    RequestContext requestContext = new RequestContext((String) null);
    final Optional<VideoContext> videoContext = VideoResolvers.resolve(requestContext);
    assertFalse(videoContext.isPresent());
  }

  @Test
  public void testResolveContentPreview() {
    RequestContext requestContext = new RequestContext((String) null);
    UserResolvers.setContextPreviewId(1234L, requestContext);
    final Optional<VideoContext> videoContext = VideoResolvers.resolve(requestContext);
    assertTrue(videoContext.isPresent());
    assertEquals(1234L, videoContext.get().getContentPreviewAccount().getId());
  }
}
