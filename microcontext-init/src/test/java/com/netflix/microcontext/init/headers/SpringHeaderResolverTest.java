package com.netflix.microcontext.init.headers;

import static org.junit.jupiter.api.Assertions.*;

import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;

class SpringHeaderResolverTest {

  @Test
  void testEmpty() {
    final SpringHeaderResolver springHeaderResolver = new SpringHeaderResolver(HttpHeaders.EMPTY);
    final List<String> all = springHeaderResolver.getAll("foo");
    assertNotNull(all);
    assertEquals(0, all.size());

    final Optional<String> get = springHeaderResolver.get("foo");
    assertNotNull(get);
    assertFalse(get.isPresent());

    final boolean contains = springHeaderResolver.contains("foo");
    assertFalse(contains);
  }
}
