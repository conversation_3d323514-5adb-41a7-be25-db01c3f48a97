package com.netflix.microcontext.init.serializers;

import static org.junit.Assert.*;

import java.util.Optional;
import netflix.context.Context;
import org.junit.Test;

public class SerializersTest {

  private static final String DATA =
      "CiIKIDY0MUUyOTFFNUFDNTRGMzU5NTFGN0JDMTJDNkRCNUJDEgQKAkNBGgcKBWZyLUNBehYiCAoGOC42MS4wKgQKAjMxQAJIAlABigHFAQoECgJDQRIECgJDQRoECgJPTiILCglHRVJBTERUT04qCQoHcDB0IDFtMDoFCgNFU1RCDgoMNjcuNzAuNTIuMjM4UgUKAzU3N3IJCgdiZWxsLmNhigERCg9BbWVyaWNhL1Rvcm9udG+SAxMKDG5ldHdvcmtfdHlwZRIDZHNskgMTCg1kcmFkaXNSZWdpb25zEgIyNZIDFwoKZHJhZGlzU2l0ZRIJb3JkMDAxLml4kgMVCgduZXR3b3JrEgpiZWxsY2FuYWRhogFLCjwSDgoMNjcuNzAuNTIuMjM4GgQI4KUDIiIKIGNkMDhlMzE0OTRmOTUzMWY1NjBkNjRjNjk1NDczZGE5KgA6Cwif0vigBhCA/NkaqgGFAQoDCIQJImcKZU5GQU5EUk9JRDEtUFJWLVAtU0FNU1VTTS1HNzgxVy0xOTk3MC0wQTNCMzQ4RjI2QUMwQTgyQzFENTZDOTAxNTMwNzdCM0U2NjVGMTVGQzlERkVEN0E0RTY1NDQ2OTgxQzQ2MTg5KhESDwoNQU5EUk9JRF9QSE9ORTAHOAm6AegBClAIiPa79uzisIADEhpOSkFCTkUyQjJKQk1OTkZZWktIMllTWFFEURoKCIj2u/bs4rCAAyIcChpOSkFCTkUyQjJKQk1OTkZZWktIMllTWFFEURImCiQzYmZhM2QzNC0xNTk3LTQwYTAtOTcyOS0yNmQ0M2Q0NDVmZmEaZwplTkZBTkRST0lEMS1QUlYtUC1TQU1TVVNNLUc3ODFXLTE5OTcwLTBBM0IzNDhGMjZBQzBBODJDMUQ1NkM5MDE1MzA3N0IzRTY2NUYxNUZDOURGRUQ3QTRFNjU0NDY5ODFDNDYxODkiAwiECQ==";
  private static final String PREAMBLE =
      "1|com.netflix.microcontext.init.requestcontext.ContextContextSerializer|0|";

  @Test
  public void parseFrom() {
    String serialized = PREAMBLE + DATA;
    Optional<Context> context = Serializers.fromRawString(serialized);
    assertTrue(context.isPresent());
    assertNotNull(context.get());
    System.out.println("context: " + context.get());
  }

  @Test
  public void serialize() {
    final String microcontext =
        Serializers.toString(Context.newBuilder().setRequestId("foo").build());
    assertEquals("CgUKA2Zvbw==", microcontext);
    System.out.println(microcontext);
  }

  @Test
  public void unwrapRequestContext() {
    String serialized = PREAMBLE + DATA;
    String unwrapped = Serializers.unwrapRequestContext(serialized);
    assertEquals(DATA, unwrapped);
    System.out.println("context: " + serialized);
  }

  @Test
  public void unwrapRequestContextNoop() {
    String serialized = "foo";
    String unwrapped = Serializers.unwrapRequestContext(serialized);
    assertEquals("foo", serialized);
  }

  @Test
  public void unwrapRequestContextToomany() {
    String serialized = "foo|bar|baz";
    String unwrapped = Serializers.unwrapRequestContext(PREAMBLE + serialized);
    assertEquals(serialized, unwrapped);
  }
}
