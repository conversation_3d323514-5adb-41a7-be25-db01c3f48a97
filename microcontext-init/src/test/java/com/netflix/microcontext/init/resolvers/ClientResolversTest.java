package com.netflix.microcontext.init.resolvers;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.netflix.dcms.models.protogen.DseClientPlatformName;
import com.netflix.dcms.models.protogen.DseHardwareMajorCategory;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.headers.Headers;
import com.netflix.microcontext.init.headers.MapHeaderResolver;
import com.netflix.microcontext.init.params.ParamNames;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.request.InputResolver.SelectedValue;
import com.netflix.type.protogen.BasicTypes.Locale;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import netflix.context.client.*;
import netflix.context.client.category.BrowserCategoryDetails;
import netflix.context.client.category.ClientCategory;
import netflix.context.client.flavor.ClientFlavor;
import netflix.context.client.formfactor.ClientFormFactor;
import netflix.context.client.tier.DeviceTier;
import netflix.context.common.StringList;
import netflix.context.common.Version;
import netflix.context.device.DeviceContext;
import netflix.context.visit.WebClientDetails;
import org.junit.Assert;
import org.junit.Test;

public class ClientResolversTest {

  @Test
  public void resolveAppVersion() {
    final HeaderResolver headerResolver = mock(HeaderResolver.class);
    final ParamResolver paramResolver = mock(ParamResolver.class);
    when(headerResolver.selectFirst(any()))
        .thenReturn(Optional.of(new SelectedValue("some-name", "some-ui-version", "some-source")));

    Optional<ClientContext> actual =
        ClientResolvers.resolve(
            ClientResolverRequest.builder()
                .setHeaderResolver(headerResolver)
                .setParamResolver(paramResolver)
                .build());
    assertTrue(actual.isPresent());
    assertTrue(actual.get().hasAppVersion());
    assertEquals("some-ui-version", actual.get().getAppVersion().getVersion());

    verify(headerResolver).selectFirst(Headers.ALL_APP_VERSIONS);
  }

  @Test
  public void resolveAppVersionParam() {
    final HeaderResolver headerResolver = mock(HeaderResolver.class);
    final ParamResolver paramResolver = mock(ParamResolver.class);
    when(paramResolver.selectFirst(anyList()))
        .thenReturn(Optional.of(new SelectedValue("some-name", "some-ui-version", "some-source")));

    Optional<ClientContext> actual =
        ClientResolvers.resolve(
            ClientResolverRequest.builder()
                .setHeaderResolver(headerResolver)
                .setParamResolver(paramResolver)
                .build());
    assertTrue(actual.isPresent());
    assertTrue(actual.get().hasAppVersion());
    assertEquals("some-ui-version", actual.get().getAppVersion().getVersion());

    verify(headerResolver).selectFirst(Headers.ALL_APP_VERSIONS);
    verify(paramResolver).selectFirst(ParamNames.ALL_APP_VERSIONS);
  }

  @Test
  public void locales() {
    List<Locale> strings = ClientResolvers.locales("en , fr , kr ,");
    Assert.assertEquals(strings.toString(), 3, strings.size());
    Assert.assertEquals(Locale.newBuilder().setId("en").build(), strings.get(0));
    Assert.assertEquals(Locale.newBuilder().setId("fr").build(), strings.get(1));
    Assert.assertEquals(Locale.newBuilder().setId("kr").build(), strings.get(2));
  }

  @Test
  public void localesJson() {
    List<Locale> strings = ClientResolvers.locales("[\"en \",\"fr \",\"kr \"]");
    Assert.assertEquals(3, strings.size());
    Assert.assertEquals(Locale.newBuilder().setId("en").build(), strings.get(0));
    Assert.assertEquals(Locale.newBuilder().setId("fr").build(), strings.get(1));
    Assert.assertEquals(Locale.newBuilder().setId("kr").build(), strings.get(2));
  }

  @Test
  public void clientEmpty() {
    Optional<ClientContext> client = ClientResolvers.resolve(ClientResolverRequest.empty());
    assertFalse(client.isPresent());
  }

  @Test
  public void clientDeviceFallback() {
    Optional<ClientContext> client =
        ClientResolvers.resolve(
            ClientResolverRequest.builder()
                .setDeviceContext(
                    DeviceContext.newBuilder()
                        .setClientPlatform(DseClientPlatformName.ANDROID_MOBILE)
                        .build())
                .build());
    Assert.assertTrue(client.isPresent());
    Assert.assertEquals(ClientCategory.ANDROID, client.get().getClientCategory());
    Assert.assertEquals(ClientFlavor.ANDROID, client.get().getClientFlavor());
  }

  @Test
  public void clientBad() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder().setHeaderResolver(headers()).build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFlavor.UNSPECIFIED, client.getClientFlavor());
    Assert.assertEquals(ClientCategory.UNSPECIFIED, client.getClientCategory());
    Assert.assertEquals("30", client.getOsVersion().getVersion());
  }

  @Test
  public void clientFlavor() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.UI_FLAVOR, "akira"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFlavor.AKIRA, client.getClientFlavor());
    Assert.assertEquals(ClientCategory.WEB, client.getClientCategory());
  }

  @Test
  public void clientFlavorHostFallback() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.X_HOST, "android.prod.ftl.netflix.com"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFlavor.ANDROID, client.getClientFlavor());
    Assert.assertEquals(ClientCategory.ANDROID, client.getClientCategory());
  }

  @Test
  public void clientFlavorHostFallbackNgp() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.X_HOST, "ios.ngp.prod.cloud.netflix.com"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFlavor.ARGO, client.getClientFlavor());
    Assert.assertEquals(ClientCategory.IOS, client.getClientCategory());
  }

  @Test
  public void clientFlavorHostFallbackTvui() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.X_HOST, "nrdp.prod.cloud.netflix.com"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFlavor.TV_OTHER, client.getClientFlavor());
    Assert.assertEquals(ClientCategory.TV, client.getClientCategory());
  }

  @Test
  public void clientFlavorHostFallbackStaging() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.HOST, "nrdp-test.staging.cloud.netflix.com"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFlavor.TV_OTHER, client.getClientFlavor());
    Assert.assertEquals(ClientCategory.TV, client.getClientCategory());
  }

  @Test
  public void clientFlavorLegacy() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.ANDROID_UI_FLAVOR_LEGACY, "android"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFlavor.ANDROID, client.getClientFlavor());
    Assert.assertEquals(ClientCategory.ANDROID, client.getClientCategory());
  }

  @Test
  public void clientFlavorTrex() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.UI_FLAVOR, "trex"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFlavor.TREX, client.getClientFlavor());
    Assert.assertEquals(ClientCategory.ANDROID, client.getClientCategory());
  }

  @Test
  public void clientFlavorButterfly() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.UI_FLAVOR, "butterfly"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFlavor.BUTTERFLY, client.getClientFlavor());
    Assert.assertEquals(ClientCategory.IOS, client.getClientCategory());
  }

  @Test
  public void clientFlavorAndroid() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.ANDROID_UI_FLAVOR_LEGACY, "samurai"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFlavor.ANDROID, client.getClientFlavor());
    Assert.assertEquals(ClientCategory.ANDROID, client.getClientCategory());
  }

  @Test
  public void clientFlavorUIBootMember() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.UI_FLAVOR, "uiBootMeMber"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFlavor.DARWIN, client.getClientFlavor());
    Assert.assertEquals(ClientCategory.TV, client.getClientCategory());
  }

  @Test
  public void clientFlavorWebHostShaktiGroupFallback() {
    ClientContext client =
        ClientResolvers.resolve(ClientResolverRequest.builder().setHostGroup("shakti").build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFlavor.AKIRA, client.getClientFlavor());
    Assert.assertEquals(ClientCategory.WEB, client.getClientCategory());
  }

  @Test
  public void clientFlavorWebHostWebsiteGroupFallback() {
    ClientContext client =
        ClientResolvers.resolve(ClientResolverRequest.builder().setHostGroup("website-nq").build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFlavor.AKIRA, client.getClientFlavor());
    Assert.assertEquals(ClientCategory.WEB, client.getClientCategory());
  }

  @Test
  public void clientFlavorWebHostGarbageGroupFallback() {
    ClientContext client =
        ClientResolvers.resolve(ClientResolverRequest.builder().setHostGroup("fooodomain").build())
            .orElse(null);
    assertNull(client);
  }

  @Test
  public void clientFormFactor() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.FORM_FACTOR, "phone"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFormFactor.PHONE, client.getClientFormFactor());
  }

  @Test
  public void clientFormFactorHeaderDevice() {
    ClientContext client =
        ClientResolvers.resolve(
                of(Headers.FORM_FACTOR, "phone"),
                ParamResolver.EMPTY,
                DeviceContext.newBuilder()
                    .setHardwareMajorCategory(DseHardwareMajorCategory.TABLET),
                WebClientDetails.getDefaultInstance())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFormFactor.PHONE, client.getClientFormFactor());
  }

  @Test
  public void clientFormFactorDevice() {
    ClientContext client =
        ClientResolvers.resolve(
                HeaderResolver.EMPTY,
                ParamResolver.EMPTY,
                DeviceContext.newBuilder()
                    .setHardwareMajorCategory(DseHardwareMajorCategory.TABLET),
                WebClientDetails.getDefaultInstance())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFormFactor.TABLET, client.getClientFormFactor());
  }

  @Test
  public void clientFormFactorLegacy() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.IOS_FORM_FACTOR_LEGACY, "pad"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(ClientFormFactor.TABLET, client.getClientFormFactor());
  }

  @Test
  public void clientOsVersionLegacy() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.LEGACY_ANDROID_OS_VERSION, "30"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals("30", client.getOsVersion().getVersion());
  }

  @Test
  public void clientAppVersionLegacy() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.LEGACY_IOS_APP_VERSION, "1.1.1"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals("1.1.1", client.getAppVersion().getVersion());
  }

  @Test
  public void buildLocalizationFeatures() {
    Map<String, StringList> headersMap = new HashMap<>();
    headersMap.put(
        Headers.LOCALIZATION_FEATURES,
        StringList.newBuilder().addValues("defaultKidsProfile").build());
    MapHeaderResolver mapHeaderResolver = MapHeaderResolver.of(headersMap);
    Set<LocalizationCapabilityUnion> localizationCapabilities =
        ClientResolvers.buildLocalizationFeatures(mapHeaderResolver);
    LocalizationCapabilityUnion capability = localizationCapabilities.iterator().next();
    Assert.assertEquals(capability.getEnumValue(), LocalizationCapability.defaultKidsProfile);
  }

  @Test
  public void buildFeatureCapabilities() {
    Map<String, StringList> headersMap = new HashMap<>();
    headersMap.put(
        Headers.FEATURE_CAPABILITIES,
        StringList.newBuilder()
            .addValues("supportsTop10,supportsTop10Kids,supportsStudioBranding")
            .build());
    MapHeaderResolver mapHeaderResolver = MapHeaderResolver.of(headersMap);
    Set<FeatureCapabilityUnion> featureCapabilities =
        ClientResolvers.buildFeatureCapabilities(mapHeaderResolver);
    List<FeatureCapability> capabilities =
        featureCapabilities.stream()
            .map(FeatureCapabilityUnion::getEnumValue)
            .collect(Collectors.toList());
    Assert.assertTrue(capabilities.contains(FeatureCapability.supportsTop10));
    Assert.assertTrue(capabilities.contains(FeatureCapability.supportsTop10Kids));
    Assert.assertTrue(capabilities.contains(FeatureCapability.supportsStudioBranding));
  }

  @Test
  public void buildIxOriginals() {
    Map<String, StringList> headersMap = new HashMap<>();
    headersMap.put(
        Headers.INTERACTIVE_ORIGINALS,
        StringList.newBuilder()
            .addValues("pussinbook,minecraft,bandersnatch,bandersnatchPrePlay,badgeIconTest")
            .build());
    MapHeaderResolver mapHeaderResolver = MapHeaderResolver.of(headersMap);
    Set<InteractiveOriginalUnion> ixOriginals =
        ClientResolvers.buildInteractiveOriginals(mapHeaderResolver);
    List<InteractiveOriginal> originals =
        ixOriginals.stream()
            .map(InteractiveOriginalUnion::getEnumValue)
            .collect(Collectors.toList());
    Assert.assertTrue(originals.contains(InteractiveOriginal.pussinbook));
    Assert.assertTrue(originals.contains(InteractiveOriginal.minecraft));
    Assert.assertTrue(originals.contains(InteractiveOriginal.bandersnatch));
    Assert.assertTrue(originals.contains(InteractiveOriginal.bandersnatchPrePlay));
    Assert.assertTrue(originals.contains(InteractiveOriginal.badgeIconTest));
  }

  @Test
  public void buildTitleCapabilities() {
    Map<String, StringList> headersMap = new HashMap<>();
    headersMap.put(
        Headers.TITLE_CAPABILITIES,
        StringList.newBuilder()
            .addValues(
                "episodeOrdering,seasonOrdering,hiddenEpisodeNumbers,episodicNewBadge,episodicAvailabilityMessage,episodeSkipping,flattenedShow")
            .build());
    MapHeaderResolver mapHeaderResolver = MapHeaderResolver.of(headersMap);
    Set<TitleCapabilityUnion> featureCapabilities =
        ClientResolvers.buildTitleCapabilities(mapHeaderResolver);
    List<TitleCapability> capabilities =
        featureCapabilities.stream()
            .map(TitleCapabilityUnion::getEnumValue)
            .collect(Collectors.toList());
    Assert.assertTrue(capabilities.contains(TitleCapability.episodeOrdering));
    Assert.assertTrue(capabilities.contains(TitleCapability.seasonOrdering));
    Assert.assertTrue(capabilities.contains(TitleCapability.hiddenEpisodeNumbers));
    Assert.assertTrue(capabilities.contains(TitleCapability.episodicNewBadge));
    Assert.assertTrue(capabilities.contains(TitleCapability.episodicAvailabilityMessage));
    Assert.assertTrue(capabilities.contains(TitleCapability.episodeSkipping));
    Assert.assertTrue(capabilities.contains(TitleCapability.flattenedShow));
  }

  @Test
  public void unknownTitleCapabilities() {
    Map<String, StringList> headersMap = new HashMap<>();
    headersMap.put(
        Headers.TITLE_CAPABILITIES,
        StringList.newBuilder().addValues("unknown_capability").build());
    MapHeaderResolver mapHeaderResolver = MapHeaderResolver.of(headersMap);
    Set<TitleCapabilityUnion> featureCapabilities =
        ClientResolvers.buildTitleCapabilities(mapHeaderResolver);
    List<String> capabilities =
        featureCapabilities.stream()
            .map(TitleCapabilityUnion::getUnkownValue)
            .collect(Collectors.toList());
    Assert.assertTrue(capabilities.contains("unknown_capability"));
  }

  @Test
  public void testTVCategoryDetails() {
    Map<String, StringList> headers = new HashMap<>();
    headers.put(Headers.UI_FLAVOR, StringList.newBuilder().addValues("DARWIN").build());
    HeaderResolver headerResolver = MapHeaderResolver.of(headers);
    Map<String, StringList> params = new HashMap<>();
    params.put(ParamNames.NRD_APP_VERSION, StringList.newBuilder().addValues("2024.1.1").build());
    params.put(ParamNames.UI_SEM_VER, StringList.newBuilder().addValues("1.2.4").build());
    final ParamResolver paramResolver = MapHeaderResolver.of(params);
    final Optional<ClientContext> resolved =
        ClientResolvers.resolve(
            ClientResolverRequest.builder()
                .setHeaderResolver(headerResolver)
                .setParamResolver(paramResolver)
                .build());
    assertTrue(resolved.isPresent());
    final ClientContext clientContext = resolved.get();
    // in this case app version is equal to sdk version but we dropped the ui_sem_ver
    assertEquals(
        clientContext.getAppVersion().getVersion(), clientContext.getSdkVersion().getVersion());
    assertEquals(
        clientContext.getAppVersion().getVersion(),
        clientContext.getTvDetails().getNrdAppVersion().getVersion());
    // validate the ui version is populated to the ui_sem_ver value
    assertEquals("1.2.4", clientContext.getTvDetails().getUiVersion().getVersion());
  }

  @Test
  public void testCleanupAppVersionWithNrdjs() {
    Version versionWithoutAbTags =
        Version.newBuilder()
            .setVersion("UI-release-20250228_35539-gibbon-r100-darwinql-nrdjs_v3.10.172")
            .build();
    Version withoutTagsCleanedVersion =
        ClientResolvers.cleanupAppVersion(versionWithoutAbTags, ClientCategory.TV);
    assertEquals(
        "UI-release-20250228_35539-gibbon-r100-darwinql-nrdjs_v3.10.172",
        withoutTagsCleanedVersion.getVersion());
    Version versionWithAbTags =
        Version.newBuilder()
            .setVersion(
                "UI-release-20250228_35539-gibbon-r100-darwinql-nrdjs_v3.10.172-65250_3_66393_2")
            .build();
    Version withTagsCleanedVersion =
        ClientResolvers.cleanupAppVersion(versionWithAbTags, ClientCategory.TV);
    assertEquals(
        "UI-release-20250228_35539-gibbon-r100-darwinql-nrdjs_v3.10.172",
        withTagsCleanedVersion.getVersion());
  }

  @Test
  public void testCleanupAppVersionWithoutNrdjs() {
    Version versionWithoutAbTags =
        Version.newBuilder().setVersion("UI-release-20250228_35539-gibbon-r100-darwinql").build();
    Version withoutTagsCleanedVersion =
        ClientResolvers.cleanupAppVersion(versionWithoutAbTags, ClientCategory.TV);
    assertEquals(
        "UI-release-20250228_35539-gibbon-r100-darwinql", withoutTagsCleanedVersion.getVersion());
    Version versionWithAbTags =
        Version.newBuilder()
            .setVersion("UI-release-20250228_35539-gibbon-r100-darwinql-65250_3_66393_2")
            .build();
    Version withTagsCleanedVersion =
        ClientResolvers.cleanupAppVersion(versionWithAbTags, ClientCategory.TV);
    assertEquals(
        "UI-release-20250228_35539-gibbon-r100-darwinql-65250_3_66393_2",
        withTagsCleanedVersion.getVersion());
  }

  @Test
  public void deviceTierEmpty() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(MapHeaderResolver.empty())
                    .build())
            .orElse(null);
    assertNull(client);
  }

  @Test
  public void deviceTierGarbage() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.DEVICE_TIER, "foo"))
                    .build())
            .orElse(null);
    assertNull(client);
  }

  @Test
  public void deviceTierHeaderNumber() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.DEVICE_TIER, "1"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(DeviceTier.INNOVATION, client.getTier());
  }

  @Test
  public void deviceTierHeaderString() {
    ClientContext client =
        ClientResolvers.resolve(
                ClientResolverRequest.builder()
                    .setHeaderResolver(of(Headers.DEVICE_TIER, "INNOVATION"))
                    .build())
            .orElse(null);
    assertNotNull(client);
    Assert.assertEquals(DeviceTier.INNOVATION, client.getTier());
  }

  @Test
  public void buildBrowserDetailsFromHeader() {
    Map<String, StringList> headersMap = new HashMap<>();
    headersMap.put(Headers.BROWSER_NAME, StringList.newBuilder().addValues("Chrome").build());
    headersMap.put(Headers.BROWSER_VERSION, StringList.newBuilder().addValues("89.0").build());
    MapHeaderResolver headerResolver = MapHeaderResolver.of(headersMap);

    Optional<BrowserCategoryDetails> browserDetails =
        ClientResolvers.buildBrowserDetails(
            headerResolver, ParamResolver.EMPTY, WebClientDetails.getDefaultInstance());

    assertTrue(browserDetails.isPresent());
    assertEquals("Chrome", browserDetails.get().getBrowserName());
    assertEquals("89.0", browserDetails.get().getBrowserVersion());
  }

  @Test
  public void buildBrowserDetailsFromParams() {
    Map<String, StringList> paramsMap = new HashMap<>();
    paramsMap.put(ParamNames.BROWSER_NAME, StringList.newBuilder().addValues("Firefox").build());
    paramsMap.put(ParamNames.BROWSER_VERSION, StringList.newBuilder().addValues("78.0").build());
    MapHeaderResolver paramResolver = MapHeaderResolver.of(paramsMap);

    Optional<BrowserCategoryDetails> browserDetails =
        ClientResolvers.buildBrowserDetails(
            HeaderResolver.EMPTY, paramResolver, WebClientDetails.getDefaultInstance());

    assertTrue(browserDetails.isPresent());
    assertEquals("Firefox", browserDetails.get().getBrowserName());
    assertEquals("78.0", browserDetails.get().getBrowserVersion());
  }

  @Test
  public void capabilitiesWithLocalizationFeatures() {
    Map<String, StringList> headersMap = new HashMap<>();
    headersMap.put(
        Headers.LOCALIZATION_FEATURES,
        StringList.newBuilder().addValues("defaultKidsProfile").build());
    MapHeaderResolver headerResolver = MapHeaderResolver.of(headersMap);

    CapabilitiesContext capabilities = ClientResolvers.capabilities(headerResolver, null, null);

    assertFalse(capabilities.getLocalizationCapabilitiesList().isEmpty());
    assertTrue(
        capabilities.getLocalizationCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == LocalizationCapability.defaultKidsProfile));
  }

  private static MapHeaderResolver of(String k1, String v1) {
    return MapHeaderResolver.of(
        Collections.singletonMap(k1, StringList.newBuilder().addValues(v1).build()));
  }

  private static MapHeaderResolver headers() {
    Map<String, StringList> map = new HashMap<>();
    map.put(
        Headers.UI_FLAVOR,
        StringList.newBuilder().addValues("C69F99D9-2B82-537A-9E34-EB85B4E234A1").build());
    map.put(Headers.LEGACY_ANDROID_OS_VERSION, StringList.newBuilder().addValues("30").build());
    return MapHeaderResolver.of(map);
  }
}
