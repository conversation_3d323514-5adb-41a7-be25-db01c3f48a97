package com.netflix.microcontext.init.device;

import static com.netflix.microcontext.init.device.DeviceResolvers.SUPPORT_LEVEL;
import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.headers.Headers;
import com.netflix.microcontext.init.headers.MapHeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.params.Params;
import com.netflix.passport.introspect.PassportIdentity;
import java.util.*;
import netflix.context.common.StringList;
import netflix.context.device.DeviceContext;
import netflix.context.device.supportlevel.DeviceSupportLevel;
import org.junit.Before;
import org.junit.Test;

public class DeviceResolversTest {

  private PassportIdentity passport;
  private HeaderResolver headerResolver;
  private ParamResolver paramResolver;

  @Before
  public void setUp() {
    passport = mock(PassportIdentity.class);

    Map<String, StringList> params = new HashMap<>();
    Map<String, StringList> headers = new HashMap<>();
    params.put(Params.PARAM_ESN, StringList.newBuilder().addValues("PARAMS_ESN12345").build());
    headers.put(Headers.ESN, StringList.newBuilder().addValues("HEADER_ESN12345").build());

    headerResolver = MapHeaderResolver.of(headers);
    paramResolver = MapHeaderResolver.of(params);
  }

  @Test
  public void testResolveEsnHeaderEmptyThenParams() {
    when(passport.getEsn()).thenReturn(Optional.empty());

    Map<String, StringList> headerParams = new HashMap<>();
    headerParams.put(Headers.ESN, StringList.newBuilder().addValues("").build());
    headerResolver = MapHeaderResolver.of(headerParams);

    Map<String, StringList> paramParams = new HashMap<>();
    paramParams.put(Params.PARAM_E, StringList.newBuilder().addValues("PARAM_ESN12345").build());
    paramResolver = MapHeaderResolver.of(paramParams);

    Optional<String> esn = DeviceResolvers.resolveEsn(passport, headerResolver, paramResolver);

    assertTrue(esn.isPresent());
    assertEquals("PARAM_ESN12345", esn.get());
  }

  @Test
  public void testResolveEsnFromPassport() {
    when(passport.getEsn()).thenReturn(Optional.of("ESN12345"));

    Optional<String> esn = DeviceResolvers.resolveEsn(passport, headerResolver, paramResolver);

    assertTrue(esn.isPresent());
    assertEquals("ESN12345", esn.get());
  }

  @Test
  public void testResolveEsnFromHeader() {
    when(passport.getEsn()).thenReturn(Optional.empty());

    Optional<String> esn = DeviceResolvers.resolveEsn(passport, headerResolver, paramResolver);

    assertTrue(esn.isPresent());
    assertEquals("HEADER_ESN12345", esn.get());
  }

  @Test
  public void testResolveEsnFromParams() {
    when(passport.getEsn()).thenReturn(Optional.empty());

    // Set up the header with no valid ESN value to ensure it falls back to params
    Map<String, StringList> headerParams = new HashMap<>();
    headerParams.put(Headers.ESN, StringList.newBuilder().addValues("").build());
    headerResolver = MapHeaderResolver.of(headerParams);

    Map<String, StringList> paramParams = new HashMap<>();
    paramParams.put(Params.PARAM_E, StringList.newBuilder().addValues("PARAM_ESN12345").build());
    paramResolver = MapHeaderResolver.of(paramParams);

    Optional<String> esn = DeviceResolvers.resolveEsn(passport, headerResolver, paramResolver);

    assertTrue(esn.isPresent());
    assertEquals("PARAM_ESN12345", esn.get());
  }

  @Test
  public void testResolveEsnNotFound() {
    when(passport.getEsn()).thenReturn(Optional.empty());

    Map<String, StringList> params = new HashMap<>();
    headerResolver = MapHeaderResolver.of(params);
    paramResolver = MapHeaderResolver.of(params);

    Optional<String> esn = DeviceResolvers.resolveEsn(passport, headerResolver, paramResolver);

    assertFalse(esn.isPresent());
  }

  @Test
  public void testResolveEsnWithIncorrectKeys() {
    when(passport.getEsn()).thenReturn(Optional.empty());

    Map<String, StringList> params = new HashMap<>();
    params.put("incorrectKey", StringList.newBuilder().addValues("WRONG_ESN").build());
    headerResolver = MapHeaderResolver.of(params);
    paramResolver = MapHeaderResolver.of(params);

    Optional<String> esn = DeviceResolvers.resolveEsn(passport, headerResolver, paramResolver);

    assertFalse(esn.isPresent());
  }

  @Test
  public void resolveLower() {
    final DeviceContext deviceContext =
        DeviceResolvers.resolve("foo", 123, Collections.singletonMap(SUPPORT_LEVEL, "Unsupported"));
    assertEquals(DeviceSupportLevel.UNSUPPORTED, deviceContext.getSupportLevel());
    assertEquals("foo", deviceContext.getEsn().getValue());
    assertEquals(123, deviceContext.getType().getId());
    assertEquals(DeviceSupportLevel.UNSUPPORTED, deviceContext.getSupportLevel());
  }

  @Test
  public void resolveUpper() {
    final DeviceContext deviceContext =
        DeviceResolvers.resolve("foo", 123, Collections.singletonMap(SUPPORT_LEVEL, "Unsupported"));
    assertEquals("foo", deviceContext.getEsn().getValue());
    assertEquals(123, deviceContext.getType().getId());
    assertEquals(DeviceSupportLevel.UNSUPPORTED, deviceContext.getSupportLevel());
  }
}
