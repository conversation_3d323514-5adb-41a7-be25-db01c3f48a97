package com.netflix.microcontext.init.resolvers.tiers;

import static org.junit.Assert.*;

import com.netflix.microcontext.init.utils.ComparableVersion;
import org.junit.Test;

/** Tests for the BrowserVersionThresholds class. */
public class BrowserVersionThresholdsTest {

  @Test
  public void testOfWithTwoParameters() {
    BrowserVersionThresholds thresholds = BrowserVersionThresholds.of("10.5", "8.0");

    // Test innovation threshold
    assertEquals(10, thresholds.getInnovation().getMajor());
    assertEquals(5, thresholds.getInnovation().getMinor());
    assertEquals(0, thresholds.getInnovation().getPatch());

    // Test maintenance threshold
    assertEquals(8, thresholds.getMaintenance().getMajor());
    assertEquals(0, thresholds.getMaintenance().getMinor());
    assertEquals(0, thresholds.getMaintenance().getPatch());
  }

  @Test
  public void testOfWithOneParameter() {
    BrowserVersionThresholds thresholds = BrowserVersionThresholds.of("15.2");

    // Test innovation threshold
    assertEquals(15, thresholds.getInnovation().getMajor());
    assertEquals(2, thresholds.getInnovation().getMinor());
    assertEquals(0, thresholds.getInnovation().getPatch());

    // Test maintenance threshold (should be DEFAULT)
    assertEquals(ComparableVersion.DEFAULT, thresholds.getMaintenance());
    assertTrue(thresholds.getMaintenance().isEmpty());
  }

  @Test
  public void testWithInvalidVersions() {
    // Test with invalid innovation version
    BrowserVersionThresholds thresholds1 = BrowserVersionThresholds.of("invalid", "8.0");
    assertEquals(ComparableVersion.DEFAULT, thresholds1.getInnovation());
    assertEquals(8, thresholds1.getMaintenance().getMajor());

    // Test with invalid maintenance version
    BrowserVersionThresholds thresholds2 = BrowserVersionThresholds.of("10.5", "invalid");
    assertEquals(10, thresholds2.getInnovation().getMajor());
    assertEquals(ComparableVersion.DEFAULT, thresholds2.getMaintenance());
  }

  @Test
  public void testWithEmptyVersions() {
    // Test with empty innovation version
    BrowserVersionThresholds thresholds1 = BrowserVersionThresholds.of("", "8.0");
    assertEquals(ComparableVersion.DEFAULT, thresholds1.getInnovation());
    assertEquals(8, thresholds1.getMaintenance().getMajor());

    // Test with empty maintenance version
    BrowserVersionThresholds thresholds2 = BrowserVersionThresholds.of("10.5", "");
    assertEquals(10, thresholds2.getInnovation().getMajor());
    assertEquals(ComparableVersion.DEFAULT, thresholds2.getMaintenance());
  }
}
