package com.netflix.microcontext.init.utils;

import static org.junit.Assert.*;

import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.headers.Headers;
import com.netflix.microcontext.init.headers.MapHeaderResolver;
import com.netflix.microcontext.init.params.ParamNames;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.request.InputResolver.SelectedValue;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.DefaultRegistry;
import com.netflix.spectator.api.Id;
import com.netflix.spectator.api.Measurement;
import com.netflix.spectator.api.Registry;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import netflix.context.common.StringList;
import org.junit.Test;

public class ResolverSelectorTest {

  @Test
  public void testValueSelectorHeader() {
    Registry registry = new DefaultRegistry();
    ResolverSelector selector = new ResolverSelector(registry);
    HeaderResolver headerResolver =
        MapHeaderResolver.of(
            Collections.singletonMap(
                Headers.TVUI_VERSION, StringList.newBuilder().addValues("12.3").build()));
    ParamResolver paramResolver =
        MapHeaderResolver.of(
            Collections.singletonMap(
                ParamNames.NRD_APP_VERSION,
                StringList.newBuilder().addValues("2024.10.8").build()));
    final Optional<SelectedValue> appVersion =
        selector.select(
            "appVersion",
            Headers.ALL_APP_VERSIONS,
            headerResolver,
            ParamNames.ALL_APP_VERSIONS,
            paramResolver);
    assertTrue(appVersion.isPresent());
    final SelectedValue selectedValue = appVersion.get();
    assertEquals(Headers.TVUI_VERSION, selectedValue.getName());
    assertEquals("12.3", selectedValue.getValue());
    final List<Counter> collect = registry.counters().collect(Collectors.toList());
    assertEquals(1, collect.size());
    final Counter counter = collect.get(0);
    List<Measurement> measurements = new ArrayList<>();
    counter.measure().forEach(measurements::add);
    assertEquals(1, measurements.size());
    final Measurement measurement = measurements.get(0);
    final Id id = measurement.id();
    assertEquals("microcontext.resolver.selected", id.name());
    Map<String, String> tags = new HashMap<>();
    id.tags().forEach(tag -> tags.put(tag.key(), tag.value()));
    assertEquals(3, tags.size());
    assertEquals("header", tags.get("resolver"));
    assertEquals("appVersion", tags.get("field"));
    assertEquals(Headers.TVUI_VERSION, tags.get("selected"));
  }

  @Test
  public void testValueSelectorParam() {
    Registry registry = new DefaultRegistry();
    ResolverSelector selector = new ResolverSelector(registry);
    HeaderResolver headerResolver = HeaderResolver.EMPTY;
    ParamResolver paramResolver =
        MapHeaderResolver.of(
            Collections.singletonMap(
                ParamNames.NRD_APP_VERSION,
                StringList.newBuilder().addValues("2024.10.8").build()));
    final Optional<SelectedValue> appVersion =
        selector.select(
            "appVersion",
            Headers.ALL_APP_VERSIONS,
            headerResolver,
            ParamNames.ALL_APP_VERSIONS,
            paramResolver);
    assertTrue(appVersion.isPresent());
    final SelectedValue selectedValue = appVersion.get();
    assertEquals(ParamNames.NRD_APP_VERSION, selectedValue.getName());
    assertEquals("2024.10.8", selectedValue.getValue());
    final List<Counter> collect = registry.counters().collect(Collectors.toList());
    assertEquals(1, collect.size());
    final Counter counter = collect.get(0);
    List<Measurement> measurements = new ArrayList<>();
    counter.measure().forEach(measurements::add);
    assertEquals(1, measurements.size());
    final Measurement measurement = measurements.get(0);
    final Id id = measurement.id();
    assertEquals("microcontext.resolver.selected", id.name());
    Map<String, String> tags = new HashMap<>();
    id.tags().forEach(tag -> tags.put(tag.key(), tag.value()));
    assertEquals(3, tags.size());
    assertEquals("param", tags.get("resolver"));
    assertEquals("appVersion", tags.get("field"));
    assertEquals(ParamNames.NRD_APP_VERSION, tags.get("selected"));
  }

  @Test
  public void testValueSelectorEmpty() {
    Registry registry = new DefaultRegistry();
    ResolverSelector selector = new ResolverSelector(registry);
    HeaderResolver headerResolver = HeaderResolver.EMPTY;
    ParamResolver paramResolver = ParamResolver.EMPTY;
    final Optional<SelectedValue> appVersion =
        selector.select(
            "appVersion",
            Headers.ALL_APP_VERSIONS,
            headerResolver,
            ParamNames.ALL_APP_VERSIONS,
            paramResolver);
    assertFalse(appVersion.isPresent());
    final List<Counter> collect = registry.counters().collect(Collectors.toList());
    assertEquals(1, collect.size());
    final Counter counter = collect.get(0);
    List<Measurement> measurements = new ArrayList<>();
    counter.measure().forEach(measurements::add);
    assertEquals(1, measurements.size());
    final Measurement measurement = measurements.get(0);
    final Id id = measurement.id();
    assertEquals("microcontext.resolver.selected", id.name());
    Map<String, String> tags = new HashMap<>();
    id.tags().forEach(tag -> tags.put(tag.key(), tag.value()));
    assertEquals(3, tags.size());
    assertEquals("none", tags.get("resolver"));
    assertEquals("appVersion", tags.get("field"));
    assertEquals("miss", tags.get("selected"));
  }
}
