package com.netflix.microcontext.init.resolvers.locale;

import static org.junit.Assert.*;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

public class WebLocaleResolverTest {

  private static int testCount = 0;
  private static int passedTests = 0;

  public static void main(String[] args) {
    WebLocaleResolverTest test = new WebLocaleResolverTest();
    test.runAllTests();

    System.out.println("\n=== Test Results ===");
    System.out.println("Total tests: " + testCount);
    System.out.println("Passed: " + passedTests);
    System.out.println("Failed: " + (testCount - passedTests));

    if (passedTests == testCount) {
      System.out.println("All tests passed! ✅");
    } else {
      System.out.println("Some tests failed! ❌");
    }
  }

  private void runAllTests() {
    // Tests for getDeviceSupportedScripts
    runTest("testGetDeviceSupportedScripts_withValidParameter", this::testGetDeviceSupportedScripts_withValidParameter);
    runTest("testGetDeviceSupportedScripts_withEmptyParameter", this::testGetDeviceSupportedScripts_withEmptyParameter);
    runTest("testGetDeviceSupportedScripts_withMissingParameter", this::testGetDeviceSupportedScripts_withMissingParameter);
    runTest("testGetDeviceSupportedScripts_withWhitespace", this::testGetDeviceSupportedScripts_withWhitespace);

    // Tests for getDeviceSupportedLocales
    runTest("testGetDeviceSupportedLocales_withValidParameter", this::testGetDeviceSupportedLocales_withValidParameter);
    runTest("testGetDeviceSupportedLocales_withUnderscoreFormat", this::testGetDeviceSupportedLocales_withUnderscoreFormat);
    runTest("testGetDeviceSupportedLocales_withEmptyParameter", this::testGetDeviceSupportedLocales_withEmptyParameter);
    runTest("testGetDeviceSupportedLocales_withMissingParameter", this::testGetDeviceSupportedLocales_withMissingParameter);
    runTest("testGetDeviceSupportedLocales_withInvalidLocales", this::testGetDeviceSupportedLocales_withInvalidLocales);

    // Tests for getDeviceRequestedLocales
    runTest("testGetDeviceRequestedLocales_withBothParameterAndHeader", this::testGetDeviceRequestedLocales_withBothParameterAndHeader);
    runTest("testGetDeviceRequestedLocales_withOnlyParameter", this::testGetDeviceRequestedLocales_withOnlyParameter);
    runTest("testGetDeviceRequestedLocales_withOnlyHeader", this::testGetDeviceRequestedLocales_withOnlyHeader);
    runTest("testGetDeviceRequestedLocales_withEmptyInputs", this::testGetDeviceRequestedLocales_withEmptyInputs);

    // Tests for Accept-Language header parsing
    runTest("testAcceptLanguageHeader_singleLanguage", this::testAcceptLanguageHeader_singleLanguage);
    runTest("testAcceptLanguageHeader_singleLanguageWithWhitespace", this::testAcceptLanguageHeader_singleLanguageWithWhitespace);
    runTest("testAcceptLanguageHeader_singleLanguageWithQuality", this::testAcceptLanguageHeader_singleLanguageWithQuality);
    runTest("testAcceptLanguageHeader_multipleLanguagesWithQuality", this::testAcceptLanguageHeader_multipleLanguagesWithQuality);
    runTest("testAcceptLanguageHeader_sortedByWeight", this::testAcceptLanguageHeader_sortedByWeight);
    runTest("testAcceptLanguageHeader_emptyString", this::testAcceptLanguageHeader_emptyString);
    runTest("testAcceptLanguageHeader_whitespaceOnly", this::testAcceptLanguageHeader_whitespaceOnly);

    // Tests for locale parsing edge cases
    runTest("testLocaleParsingWithScript", this::testLocaleParsingWithScript);
    runTest("testLocaleParsingLanguageOnly", this::testLocaleParsingLanguageOnly);

    // Integration test
    runTest("testIntegrationScenario", this::testIntegrationScenario);
  }

  private void runTest(String testName, Runnable test) {
    testCount++;
    try {
      test.run();
      passedTests++;
      System.out.println("✅ " + testName);
    } catch (Exception e) {
      System.out.println("❌ " + testName + ": " + e.getMessage());
    }
  }

  private void assertEquals(Object expected, Object actual) {
    if (!Objects.equals(expected, actual)) {
      throw new RuntimeException("Expected: " + expected + ", but was: " + actual);
    }
  }

  private void assertTrue(boolean condition) {
    if (!condition) {
      throw new RuntimeException("Expected true, but was false");
    }
  }

  // Tests for getDeviceSupportedScripts
  private void testGetDeviceSupportedScripts_withValidParameter() {
    Map<String, String> parameters = new HashMap<>();
    parameters.put("suppScripts", "latn,arab,cyrl");
    List<String> result = WebLocaleResolver.getDeviceSupportedScripts(parameters);

    assertEquals(3, result.size());
    assertEquals("latn", result.get(0));
    assertEquals("arab", result.get(1));
    assertEquals("cyrl", result.get(2));
  }

  private void testGetDeviceSupportedScripts_withEmptyParameter() {
    Map<String, String> parameters = new HashMap<>();
    parameters.put("suppScripts", "");
    List<String> result = WebLocaleResolver.getDeviceSupportedScripts(parameters);

    assertTrue(result.isEmpty());
  }

  private void testGetDeviceSupportedScripts_withMissingParameter() {
    Map<String, String> parameters = new HashMap<>();
    List<String> result = WebLocaleResolver.getDeviceSupportedScripts(parameters);

    assertTrue(result.isEmpty());
  }

  private void testGetDeviceSupportedScripts_withWhitespace() {
    Map<String, String> parameters = new HashMap<>();
    parameters.put("suppScripts", " latn , arab , cyrl ");
    List<String> result = WebLocaleResolver.getDeviceSupportedScripts(parameters);

    assertEquals(3, result.size());
    assertEquals("latn", result.get(0));
    assertEquals("arab", result.get(1));
    assertEquals("cyrl", result.get(2));
  }

  // Tests for getDeviceSupportedLocales
  private void testGetDeviceSupportedLocales_withValidParameter() {
    Map<String, String> parameters = new HashMap<>();
    parameters.put("availableLocales", "en-US,es-MX,fr-FR,de-DE");
    List<Locale> result = WebLocaleResolver.getDeviceSupportedLocales(parameters);

    assertEquals(4, result.size());
    assertEquals("en-US", result.get(0).toLanguageTag());
    assertEquals("es-MX", result.get(1).toLanguageTag());
    assertEquals("fr-FR", result.get(2).toLanguageTag());
    assertEquals("de-DE", result.get(3).toLanguageTag());
  }

  private void testGetDeviceSupportedLocales_withUnderscoreFormat() {
    Map<String, String> parameters = new HashMap<>();
    parameters.put("availableLocales", "es_MX,en_US");
    List<Locale> result = WebLocaleResolver.getDeviceSupportedLocales(parameters);

    assertEquals(2, result.size());
    assertEquals("es-MX", result.get(0).toLanguageTag());
    assertEquals("en-US", result.get(1).toLanguageTag());
  }

  private void testGetDeviceSupportedLocales_withEmptyParameter() {
    Map<String, String> parameters = new HashMap<>();
    parameters.put("availableLocales", "");
    List<Locale> result = WebLocaleResolver.getDeviceSupportedLocales(parameters);

    assertTrue(result.isEmpty());
  }

  private void testGetDeviceSupportedLocales_withMissingParameter() {
    Map<String, String> parameters = new HashMap<>();
    List<Locale> result = WebLocaleResolver.getDeviceSupportedLocales(parameters);

    assertTrue(result.isEmpty());
  }

  private void testGetDeviceSupportedLocales_withInvalidLocales() {
    Map<String, String> parameters = new HashMap<>();
    parameters.put("availableLocales", "en-US,invalid-locale,es-MX");
    List<Locale> result = WebLocaleResolver.getDeviceSupportedLocales(parameters);

    // Should filter out invalid locales
    assertEquals(2, result.size());
    assertEquals("en-US", result.get(0).toLanguageTag());
    assertEquals("es-MX", result.get(1).toLanguageTag());
  }

  // Tests for getDeviceRequestedLocales
  private void testGetDeviceRequestedLocales_withBothParameterAndHeader() {
    Map<String, String> parameters = new HashMap<>();
    Map<String, String> headers = new HashMap<>();
    parameters.put("deviceLocale", "es-MX,en-US");
    headers.put("Accept-Language", "fr-FR;q=0.9,de-DE;q=0.8");

    List<Locale> result = WebLocaleResolver.getDeviceRequestedLocales(parameters, headers);

    // Should combine both sources
    assertEquals(4, result.size());
    assertTrue(result.stream().anyMatch(l -> "es-MX".equals(l.toLanguageTag())));
    assertTrue(result.stream().anyMatch(l -> "en-US".equals(l.toLanguageTag())));
    assertTrue(result.stream().anyMatch(l -> "fr-FR".equals(l.toLanguageTag())));
    assertTrue(result.stream().anyMatch(l -> "de-DE".equals(l.toLanguageTag())));
  }

  private void testGetDeviceRequestedLocales_withOnlyParameter() {
    Map<String, String> parameters = new HashMap<>();
    Map<String, String> headers = new HashMap<>();
    parameters.put("deviceLocale", "es-MX,en-US");

    List<Locale> result = WebLocaleResolver.getDeviceRequestedLocales(parameters, headers);

    // Should include parameter locales plus default en-US from missing header
    assertTrue(result.size() >= 2);
    assertTrue(result.stream().anyMatch(l -> "es-MX".equals(l.toLanguageTag())));
    assertTrue(result.stream().anyMatch(l -> "en-US".equals(l.toLanguageTag())));
  }

  private void testGetDeviceRequestedLocales_withOnlyHeader() {
    Map<String, String> parameters = new HashMap<>();
    Map<String, String> headers = new HashMap<>();
    headers.put("Accept-Language", "fr-FR;q=0.9,de-DE;q=0.8");

    List<Locale> result = WebLocaleResolver.getDeviceRequestedLocales(parameters, headers);

    assertEquals(2, result.size());
    assertEquals("fr-FR", result.get(0).toLanguageTag());
    assertEquals("de-DE", result.get(1).toLanguageTag());
  }

  private void testGetDeviceRequestedLocales_withEmptyInputs() {
    Map<String, String> parameters = new HashMap<>();
    Map<String, String> headers = new HashMap<>();
    List<Locale> result = WebLocaleResolver.getDeviceRequestedLocales(parameters, headers);

    // Should default to en-US
    assertEquals(1, result.size());
    assertEquals("en-US", result.get(0).toLanguageTag());
  }

  // Tests for Accept-Language header parsing (ported from acceptLanguageParser.test.js)
  private void testAcceptLanguageHeader_singleLanguage() {
    Map<String, String> parameters = new HashMap<>();
    Map<String, String> headers = new HashMap<>();
    headers.put("Accept-Language", "en-US");

    List<Locale> result = WebLocaleResolver.getDeviceRequestedLocales(parameters, headers);

    assertEquals(1, result.size());
    assertEquals("en-US", result.get(0).toLanguageTag());
  }

  private void testAcceptLanguageHeader_singleLanguageWithWhitespace() {
    Map<String, String> parameters = new HashMap<>();
    Map<String, String> headers = new HashMap<>();
    headers.put("Accept-Language", "  en-US ");

    List<Locale> result = WebLocaleResolver.getDeviceRequestedLocales(parameters, headers);

    assertEquals(1, result.size());
    assertEquals("en-US", result.get(0).toLanguageTag());
  }

  private void testAcceptLanguageHeader_singleLanguageWithQuality() {
    Map<String, String> parameters = new HashMap<>();
    Map<String, String> headers = new HashMap<>();
    headers.put("Accept-Language", "en-US;q=1");

    List<Locale> result = WebLocaleResolver.getDeviceRequestedLocales(parameters, headers);

    assertEquals(1, result.size());
    assertEquals("en-US", result.get(0).toLanguageTag());
  }

  private void testAcceptLanguageHeader_multipleLanguagesWithQuality() {
    Map<String, String> parameters = new HashMap<>();
    Map<String, String> headers = new HashMap<>();
    headers.put("Accept-Language", "fr-CH, fr;q=0.9, en;q=0.8, de;q=0.7, es-419;q=0.55");

    List<Locale> result = WebLocaleResolver.getDeviceRequestedLocales(parameters, headers);

    assertEquals(5, result.size());
    // Should be sorted by quality (highest first)
    assertEquals("fr-CH", result.get(0).toLanguageTag());
    assertEquals("fr", result.get(1).toLanguageTag());
    assertEquals("en", result.get(2).toLanguageTag());
    assertEquals("de", result.get(3).toLanguageTag());
    assertEquals("es-419", result.get(4).toLanguageTag());
  }

  private void testAcceptLanguageHeader_sortedByWeight() {
    Map<String, String> parameters = new HashMap<>();
    Map<String, String> headers = new HashMap<>();
    headers.put("Accept-Language", "de;q=0.7, fr-CH, fr;q=0.9, en;q=0.8");

    List<Locale> result = WebLocaleResolver.getDeviceRequestedLocales(parameters, headers);

    assertEquals(4, result.size());
    // Should be sorted by quality (highest first)
    assertEquals("fr-CH", result.get(0).toLanguageTag()); // q=1.0 (default)
    assertEquals("fr", result.get(1).toLanguageTag());    // q=0.9
    assertEquals("en", result.get(2).toLanguageTag());    // q=0.8
    assertEquals("de", result.get(3).toLanguageTag());    // q=0.7
  }

  private void testAcceptLanguageHeader_emptyString() {
    Map<String, String> parameters = new HashMap<>();
    Map<String, String> headers = new HashMap<>();
    headers.put("Accept-Language", "");

    List<Locale> result = WebLocaleResolver.getDeviceRequestedLocales(parameters, headers);

    // Should default to en-US
    assertEquals(1, result.size());
    assertEquals("en-US", result.get(0).toLanguageTag());
  }

  private void testAcceptLanguageHeader_whitespaceOnly() {
    Map<String, String> parameters = new HashMap<>();
    Map<String, String> headers = new HashMap<>();
    headers.put("Accept-Language", "  ");

    List<Locale> result = WebLocaleResolver.getDeviceRequestedLocales(parameters, headers);

    // Should default to en-US
    assertEquals(1, result.size());
    assertEquals("en-US", result.get(0).toLanguageTag());
  }

  // Tests for locale parsing edge cases (ported from index.test.js)
  private void testLocaleParsingWithScript() {
    Map<String, String> parameters = new HashMap<>();
    parameters.put("availableLocales", "es-Latn-MX");

    List<Locale> result = WebLocaleResolver.getDeviceSupportedLocales(parameters);

    assertEquals(1, result.size());
    Locale locale = result.get(0);
    assertEquals("es", locale.getLanguage());
    assertEquals("MX", locale.getCountry());
    assertEquals("es-Latn-MX", locale.toLanguageTag());
  }

  private void testLocaleParsingLanguageOnly() {
    Map<String, String> parameters = new HashMap<>();
    parameters.put("availableLocales", "en");

    List<Locale> result = WebLocaleResolver.getDeviceSupportedLocales(parameters);

    assertEquals(1, result.size());
    Locale locale = result.get(0);
    assertEquals("en", locale.getLanguage());
    assertEquals("", locale.getCountry());
    assertEquals("en", locale.toLanguageTag());
  }

  // Integration test combining all three functions
  private void testIntegrationScenario() {
    Map<String, String> parameters = new HashMap<>();
    Map<String, String> headers = new HashMap<>();

    // Set up a realistic scenario
    parameters.put("suppScripts", "latn,arab");
    parameters.put("availableLocales", "en-US,es-MX,ar-SA");
    parameters.put("deviceLocale", "es-MX");
    headers.put("Accept-Language", "es-MX;q=0.9,en-US;q=0.8,ar;q=0.7");

    List<String> scripts = WebLocaleResolver.getDeviceSupportedScripts(parameters);
    List<Locale> supportedLocales = WebLocaleResolver.getDeviceSupportedLocales(parameters);
    List<Locale> requestedLocales = WebLocaleResolver.getDeviceRequestedLocales(parameters, headers);

    // Verify scripts
    assertEquals(2, scripts.size());
    assertTrue(scripts.contains("latn"));
    assertTrue(scripts.contains("arab"));

    // Verify supported locales
    assertEquals(3, supportedLocales.size());
    assertTrue(supportedLocales.stream().anyMatch(l -> "en-US".equals(l.toLanguageTag())));
    assertTrue(supportedLocales.stream().anyMatch(l -> "es-MX".equals(l.toLanguageTag())));
    assertTrue(supportedLocales.stream().anyMatch(l -> "ar-SA".equals(l.toLanguageTag())));

    // Verify requested locales (should include both parameter and header locales)
    assertTrue(requestedLocales.size() >= 3);
    assertTrue(requestedLocales.stream().anyMatch(l -> "es-MX".equals(l.toLanguageTag())));
    assertTrue(requestedLocales.stream().anyMatch(l -> "en-US".equals(l.toLanguageTag())));
    assertTrue(requestedLocales.stream().anyMatch(l -> "ar".equals(l.toLanguageTag())));
  }
}

