package com.netflix.microcontext.init.resolvers.tiers;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.request.CookieResolver;
import com.netflix.microcontext.init.resolvers.VisitResolvers;
import com.netflix.microcontext.init.resolvers.tiers.WebTierResolver.ResolverRequest;
import com.netflix.microcontext.init.utils.ComparableVersion;
import java.util.Optional;
import netflix.context.client.tier.DeviceTier;
import netflix.context.visit.DeviceDetails;
import netflix.context.visit.OSDetails;
import netflix.context.visit.SemVer;
import netflix.context.visit.UserAgentDetails;
import netflix.context.visit.WebClientDetails;
import org.junit.Test;

public class WebTierResolverTest {

  @Test
  public void resolveDefault() {
    final Optional<DeviceTier> deviceTier =
        WebTierResolver.determineDeviceTier(ResolverRequest.EMPTY);
    assertNull(deviceTier.orElse(null));
  }

  @Test
  public void resolveAndroidBrowserInnovation() {
    final BrowserInfo browserInfo = BrowserInfoTest.getBrowserInfoAndroidBrowser();
    final ResolverRequest request =
        new ResolverRequest(false, false, ComparableVersion.DEFAULT, "", browserInfo);
    final Optional<DeviceTier> deviceTier = WebTierResolver.determineDeviceTier(request);
    assertTrue(deviceTier.isPresent());
    assertEquals(DeviceTier.INNOVATION, deviceTier.get());
  }

  @Test
  public void testResolveDeviceTierForAndroidInApp() {
    // Create mocks
    HeaderResolver headerResolver = mock(HeaderResolver.class);
    ParamResolver paramResolver = mock(ParamResolver.class);
    CookieResolver cookieResolver = mock(CookieResolver.class);

    // Get Android Chrome version that will be classified as INNOVATION
    int androidChromeMajor = VersionThresholds.ANDROID_CHROME.getInnovation().getMajor() + 1;
    int androidChromeMinor = VersionThresholds.ANDROID_CHROME.getInnovation().getMinor();

    // Setup WebClientDetails for Android device
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Chrome")
                    .setSemVer(
                        SemVer.newBuilder()
                            .setMajor(androidChromeMajor)
                            .setMinor(androidChromeMinor)
                            .setPatch(5555)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("Android")
                    .setSemVer(SemVer.newBuilder().setMajor(13).setMinor(0)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Generic Smartphone"))
            .build();

    // Setup mock responses
    when(headerResolver.get(VisitResolvers.USER_AGENT))
        .thenReturn(
            Optional.of(
                "Mozilla/5.0 (Linux; Android 13.0; Generic) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/"
                    + androidChromeMajor
                    + "."
                    + androidChromeMinor
                    + ".5555 Mobile Safari/537.36"));
    when(paramResolver.get("inapp")).thenReturn(Optional.of("1"));
    when(paramResolver.get("sw_version")).thenReturn(Optional.of("5.0.0"));
    when(paramResolver.get("esn")).thenReturn(Optional.of("NFANDROID1-PRV-P-L3-ABCDEF"));
    when(paramResolver.get("instub")).thenReturn(Optional.empty());
    when(paramResolver.get("netflixClientPlatform")).thenReturn(Optional.empty());
    when(cookieResolver.get(anyString())).thenReturn(Optional.empty());

    // Call the method under test
    Optional<DeviceTier> result =
        WebTierResolver.resolveDeviceTier(
            headerResolver, paramResolver, cookieResolver, webClientDetails);

    // Verify the result
    assertTrue(result.isPresent());
    assertEquals(DeviceTier.INNOVATION, result.get());

    // Verify interactions
    verify(headerResolver).get(VisitResolvers.USER_AGENT);
    verify(paramResolver).get("inapp");
    verify(paramResolver).get("instub");
    verify(paramResolver).get("netflixClientPlatform");
  }

  @Test
  public void resolveIOSInApp() {
    // Get iOS version that will be classified as INNOVATION
    int iosMajor = VersionThresholds.IOS_BROWSER.getInnovation().getMajor();
    int iosMinor = VersionThresholds.IOS_BROWSER.getInnovation().getMinor() + 1;

    // Create BrowserInfo for iOS
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Mobile Safari")
                    .setSemVer(SemVer.newBuilder().setMajor(15).setMinor(0)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("iOS")
                    .setSemVer(
                        SemVer.newBuilder().setMajor(iosMajor).setMinor(iosMinor).setPatch(1)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("iPhone"))
            .build();

    String userAgent =
        "Mozilla/5.0 (iPhone; CPU iPhone OS "
            + iosMajor
            + "_"
            + iosMinor
            + "_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1";
    BrowserInfo browserInfo = BrowserInfo.parseBrowserInfo(userAgent, webClientDetails);

    // Create ResolverRequest directly
    final ResolverRequest request =
        new ResolverRequest(true, false, ComparableVersion.of("15.0.0"), "", browserInfo);

    // Call determineDeviceTier directly
    final Optional<DeviceTier> deviceTier = WebTierResolver.determineDeviceTier(request);

    // Verify the result
    assertTrue(deviceTier.isPresent());
    assertEquals(DeviceTier.FOLLOWER, deviceTier.get());
  }

  @Test
  public void testResolveDeviceTierForDesktopChrome() {
    // Create mocks
    HeaderResolver headerResolver = mock(HeaderResolver.class);
    ParamResolver paramResolver = mock(ParamResolver.class);
    CookieResolver cookieResolver = mock(CookieResolver.class);

    // Get Chrome version that will be classified as INNOVATION
    int chromeMajor = VersionThresholds.DESKTOP_CHROME.getInnovation().getMajor() + 1;
    int chromeMinor = VersionThresholds.DESKTOP_CHROME.getInnovation().getMinor();

    // Setup WebClientDetails for Desktop Chrome
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Chrome")
                    .setSemVer(
                        SemVer.newBuilder()
                            .setMajor(chromeMajor)
                            .setMinor(chromeMinor)
                            .setPatch(0)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("Windows 10")
                    .setSemVer(SemVer.newBuilder().setMajor(10).setMinor(0)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Other"))
            .build();

    // Setup mock responses
    when(headerResolver.get(VisitResolvers.USER_AGENT))
        .thenReturn(
            Optional.of(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/"
                    + chromeMajor
                    + "."
                    + chromeMinor
                    + ".0.0 Safari/537.36"));
    when(paramResolver.get(anyString())).thenReturn(Optional.empty());
    when(cookieResolver.get(anyString())).thenReturn(Optional.empty());

    // Call the method under test
    Optional<DeviceTier> result =
        WebTierResolver.resolveDeviceTier(
            headerResolver, paramResolver, cookieResolver, webClientDetails);

    // Verify the result
    assertTrue(result.isPresent());
    assertEquals(DeviceTier.INNOVATION, result.get());

    // Verify interactions
    verify(headerResolver).get(VisitResolvers.USER_AGENT);
    verify(paramResolver).get("inapp");
    verify(paramResolver).get("instub");
    verify(paramResolver).get("netflixClientPlatform");
  }

  @Test
  public void testResolveDeviceTierForDesktopFirefox() {
    // Create mocks
    HeaderResolver headerResolver = mock(HeaderResolver.class);
    ParamResolver paramResolver = mock(ParamResolver.class);
    CookieResolver cookieResolver = mock(CookieResolver.class);

    // Get Firefox version that will be classified as MAINTENANCE
    int firefoxMajor = VersionThresholds.DESKTOP_FIREFOX.getInnovation().getMajor() - 1;
    int firefoxMinor = VersionThresholds.DESKTOP_FIREFOX.getInnovation().getMinor();

    // Ensure the version is still above MAINTENANCE threshold
    if (firefoxMajor < VersionThresholds.DESKTOP_FIREFOX.getMaintenance().getMajor()
        || (firefoxMajor == VersionThresholds.DESKTOP_FIREFOX.getMaintenance().getMajor()
            && firefoxMinor < VersionThresholds.DESKTOP_FIREFOX.getMaintenance().getMinor())) {
      firefoxMajor = VersionThresholds.DESKTOP_FIREFOX.getMaintenance().getMajor();
      firefoxMinor = VersionThresholds.DESKTOP_FIREFOX.getMaintenance().getMinor() + 1;
    }

    // Setup WebClientDetails for Desktop Firefox
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Firefox")
                    .setSemVer(SemVer.newBuilder().setMajor(firefoxMajor).setMinor(firefoxMinor)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("Mac OS X")
                    .setSemVer(SemVer.newBuilder().setMajor(10).setMinor(15).setPatch(7)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Other"))
            .build();

    // Setup mock responses
    when(headerResolver.get(VisitResolvers.USER_AGENT))
        .thenReturn(
            Optional.of(
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:"
                    + firefoxMajor
                    + "."
                    + firefoxMinor
                    + ") Gecko/20100101 Firefox/"
                    + firefoxMajor
                    + "."
                    + firefoxMinor));
    when(paramResolver.get(anyString())).thenReturn(Optional.empty());
    when(cookieResolver.get(anyString())).thenReturn(Optional.empty());

    // Call the method under test
    Optional<DeviceTier> result =
        WebTierResolver.resolveDeviceTier(
            headerResolver, paramResolver, cookieResolver, webClientDetails);

    // Verify the result
    assertTrue(result.isPresent());
    assertEquals(DeviceTier.MAINTENANCE, result.get());

    // Verify interactions
    verify(headerResolver).get(VisitResolvers.USER_AGENT);
    verify(paramResolver).get("inapp");
    verify(paramResolver).get("instub");
    verify(paramResolver).get("netflixClientPlatform");
  }

  @Test
  public void testResolveDeviceTierForUnsupportedBrowser() {
    // Create mocks
    HeaderResolver headerResolver = mock(HeaderResolver.class);
    ParamResolver paramResolver = mock(ParamResolver.class);
    CookieResolver cookieResolver = mock(CookieResolver.class);

    // Setup WebClientDetails for IE
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("IE")
                    .setSemVer(SemVer.newBuilder().setMajor(11).setMinor(0)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("Windows 10")
                    .setSemVer(SemVer.newBuilder().setMajor(10).setMinor(0)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Other"))
            .build();

    // Setup mock responses
    when(headerResolver.get(VisitResolvers.USER_AGENT))
        .thenReturn(
            Optional.of("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko"));
    when(paramResolver.get(anyString())).thenReturn(Optional.empty());
    when(cookieResolver.get(anyString())).thenReturn(Optional.empty());

    // Call the method under test
    Optional<DeviceTier> result =
        WebTierResolver.resolveDeviceTier(
            headerResolver, paramResolver, cookieResolver, webClientDetails);

    // Verify the result
    assertTrue(result.isPresent());
    assertEquals(DeviceTier.UNSUPPORTED, result.get());

    // Verify interactions
    verify(headerResolver).get(VisitResolvers.USER_AGENT);
    verify(paramResolver).get("inapp");
    verify(paramResolver).get("instub");
    verify(paramResolver).get("netflixClientPlatform");
  }

  @Test
  public void testResolveDeviceTierWithError() {
    // Create mocks
    HeaderResolver headerResolver = mock(HeaderResolver.class);
    ParamResolver paramResolver = mock(ParamResolver.class);
    CookieResolver cookieResolver = mock(CookieResolver.class);

    // Create a real WebClientDetails object (can't mock final classes)
    WebClientDetails webClientDetails = WebClientDetails.newBuilder().build();

    // Setup mock to throw exception
    when(headerResolver.get(anyString())).thenThrow(new RuntimeException("Test exception"));

    // Call the method under test
    Optional<DeviceTier> result =
        WebTierResolver.resolveDeviceTier(
            headerResolver, paramResolver, cookieResolver, webClientDetails);

    // Verify the result
    assertFalse(result.isPresent());

    // Verify interactions
    verify(headerResolver).get(VisitResolvers.USER_AGENT);
  }

  @Test
  public void resolveAndroidStubApp() {
    // Get Android Chrome version that will be classified as INNOVATION
    int androidChromeMajor = VersionThresholds.ANDROID_CHROME.getInnovation().getMajor() + 1;
    int androidChromeMinor = VersionThresholds.ANDROID_CHROME.getInnovation().getMinor();

    // Create BrowserInfo for Android
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Chrome")
                    .setSemVer(
                        SemVer.newBuilder()
                            .setMajor(androidChromeMajor)
                            .setMinor(androidChromeMinor)
                            .setPatch(5555)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("Android")
                    .setSemVer(SemVer.newBuilder().setMajor(13).setMinor(0)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Generic Smartphone"))
            .build();

    String userAgent =
        "Mozilla/5.0 (Linux; Android 13.0; Generic) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/"
            + androidChromeMajor
            + "."
            + androidChromeMinor
            + ".5555 Mobile Safari/537.36";
    BrowserInfo browserInfo = BrowserInfo.parseBrowserInfo(userAgent, webClientDetails);

    // Create ResolverRequest directly for Android stub app
    final ResolverRequest request =
        new ResolverRequest(true, true, ComparableVersion.of("5.0.0"), "", browserInfo);

    // Call determineDeviceTier directly
    final Optional<DeviceTier> deviceTier = WebTierResolver.determineDeviceTier(request);

    // Verify the result
    assertTrue(deviceTier.isPresent());
    assertEquals(DeviceTier.INNOVATION, deviceTier.get());
  }

  @Test
  public void resolveIOSBrowser() {
    // Get iOS version that will be classified as INNOVATION
    int iosMajor = VersionThresholds.IOS_BROWSER.getInnovation().getMajor();
    int iosMinor = VersionThresholds.IOS_BROWSER.getInnovation().getMinor() + 1;

    // Create BrowserInfo for iOS browser
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Mobile Safari")
                    .setSemVer(SemVer.newBuilder().setMajor(15).setMinor(0)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("iOS")
                    .setSemVer(
                        SemVer.newBuilder().setMajor(iosMajor).setMinor(iosMinor).setPatch(1)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("iPhone"))
            .build();

    String userAgent =
        "Mozilla/5.0 (iPhone; CPU iPhone OS "
            + iosMajor
            + "_"
            + iosMinor
            + "_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1";
    BrowserInfo browserInfo = BrowserInfo.parseBrowserInfo(userAgent, webClientDetails);

    // Create ResolverRequest directly for iOS browser (not in-app)
    final ResolverRequest request =
        new ResolverRequest(false, false, ComparableVersion.DEFAULT, "", browserInfo);

    // Call determineDeviceTier directly
    final Optional<DeviceTier> deviceTier = WebTierResolver.determineDeviceTier(request);

    // Verify the result
    assertTrue(deviceTier.isPresent());
    assertEquals(DeviceTier.INNOVATION, deviceTier.get());
  }

  @Test
  public void resolveDesktopSafari() {
    // Get Safari version that will be classified as INNOVATION
    int safariMajor = VersionThresholds.DESKTOP_SAFARI.getInnovation().getMajor() + 1;
    int safariMinor = VersionThresholds.DESKTOP_SAFARI.getInnovation().getMinor();

    // Create BrowserInfo for Desktop Safari
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Safari")
                    .setSemVer(SemVer.newBuilder().setMajor(safariMajor).setMinor(safariMinor)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("Mac OS X")
                    .setSemVer(SemVer.newBuilder().setMajor(10).setMinor(15).setPatch(7)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Other"))
            .build();

    String userAgent =
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/"
            + safariMajor
            + "."
            + safariMinor
            + " Safari/605.1.15";
    BrowserInfo browserInfo = BrowserInfo.parseBrowserInfo(userAgent, webClientDetails);

    // Create ResolverRequest directly for Desktop Safari
    final ResolverRequest request =
        new ResolverRequest(false, false, ComparableVersion.DEFAULT, "", browserInfo);

    // Call determineDeviceTier directly
    final Optional<DeviceTier> deviceTier = WebTierResolver.determineDeviceTier(request);

    // Verify the result
    assertTrue(deviceTier.isPresent());
    assertEquals(DeviceTier.INNOVATION, deviceTier.get());
  }

  @Test
  public void resolveDesktopOpera() {
    // Get Opera version that will be classified as INNOVATION
    int operaMajor = VersionThresholds.DESKTOP_OPERA.getInnovation().getMajor() + 1;
    int operaMinor = VersionThresholds.DESKTOP_OPERA.getInnovation().getMinor();

    // Create BrowserInfo for Desktop Opera
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Opera")
                    .setSemVer(SemVer.newBuilder().setMajor(operaMajor).setMinor(operaMinor)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("Windows 10")
                    .setSemVer(SemVer.newBuilder().setMajor(10).setMinor(0)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Other"))
            .build();

    String userAgent =
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 OPR/"
            + operaMajor
            + "."
            + operaMinor
            + ".0.0";
    BrowserInfo browserInfo = BrowserInfo.parseBrowserInfo(userAgent, webClientDetails);

    // Create ResolverRequest directly for Desktop Opera
    final ResolverRequest request =
        new ResolverRequest(false, false, ComparableVersion.DEFAULT, "", browserInfo);

    // Call determineDeviceTier directly
    final Optional<DeviceTier> deviceTier = WebTierResolver.determineDeviceTier(request);

    // Verify the result
    assertTrue(deviceTier.isPresent());
    assertEquals(DeviceTier.INNOVATION, deviceTier.get());
  }

  // NEW TEST: Test for Edge browser in resolveDesktopBrowserTier
  @Test
  public void resolveDesktopEdge() {
    // Get Edge version that will be classified as INNOVATION
    int edgeMajor = VersionThresholds.DESKTOP_EDGE.getInnovation().getMajor() + 1;
    int edgeMinor = VersionThresholds.DESKTOP_EDGE.getInnovation().getMinor();

    // Create BrowserInfo for Desktop Edge
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Edge OSS")
                    .setSemVer(SemVer.newBuilder().setMajor(edgeMajor).setMinor(edgeMinor)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("Windows 10")
                    .setSemVer(SemVer.newBuilder().setMajor(10).setMinor(0)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Other"))
            .build();

    String userAgent =
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 Edg/"
            + edgeMajor
            + "."
            + edgeMinor
            + ".0.0";
    BrowserInfo browserInfo = BrowserInfo.parseBrowserInfo(userAgent, webClientDetails);

    // Create ResolverRequest directly for Desktop Edge
    final ResolverRequest request =
        new ResolverRequest(false, false, ComparableVersion.DEFAULT, "", browserInfo);

    // Call determineDeviceTier directly
    final Optional<DeviceTier> deviceTier = WebTierResolver.determineDeviceTier(request);

    // Verify the result
    assertTrue(deviceTier.isPresent());
    assertEquals(DeviceTier.INNOVATION, deviceTier.get());
  }

  // NEW TEST: Test for Android Firefox in resolveMobileBrowserTier
  @Test
  public void resolveAndroidFirefoxBrowser() {
    // Get Firefox version that will be classified as INNOVATION
    int firefoxMajor = VersionThresholds.ANDROID_FIREFOX.getInnovation().getMajor() + 1;
    int firefoxMinor = VersionThresholds.ANDROID_FIREFOX.getInnovation().getMinor();

    // Create BrowserInfo for Android Firefox
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Firefox Mobile")
                    .setSemVer(SemVer.newBuilder().setMajor(firefoxMajor).setMinor(firefoxMinor)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("Android")
                    .setSemVer(SemVer.newBuilder().setMajor(13).setMinor(0)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Generic Smartphone"))
            .build();

    String userAgent =
        "Mozilla/5.0 (Android 13.0; Mobile; rv:"
            + firefoxMajor
            + "."
            + firefoxMinor
            + ") Gecko/"
            + firefoxMajor
            + "."
            + firefoxMinor
            + " Firefox/"
            + firefoxMajor
            + "."
            + firefoxMinor;
    BrowserInfo browserInfo = BrowserInfo.parseBrowserInfo(userAgent, webClientDetails);

    // Create ResolverRequest directly for Android Firefox
    final ResolverRequest request =
        new ResolverRequest(false, false, ComparableVersion.DEFAULT, "", browserInfo);

    // Call determineDeviceTier directly
    final Optional<DeviceTier> deviceTier = WebTierResolver.determineDeviceTier(request);

    // Verify the result
    assertTrue(deviceTier.isPresent());
    assertEquals(DeviceTier.INNOVATION, deviceTier.get());
  }

  // NEW TEST: Test for ARGO pattern matching in isInAppClient
  @Test
  public void resolveArgoClient() {
    // Create mocks
    HeaderResolver headerResolver = mock(HeaderResolver.class);
    ParamResolver paramResolver = mock(ParamResolver.class);
    CookieResolver cookieResolver = mock(CookieResolver.class);

    // Setup WebClientDetails for iOS device
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("CFNetwork")
                    .setSemVer(SemVer.newBuilder().setMajor(1).setMinor(0)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("iOS")
                    .setSemVer(SemVer.newBuilder().setMajor(15).setMinor(0)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("iPhone"))
            .build();

    // Setup mock responses with Argo pattern in user agent
    when(headerResolver.get(VisitResolvers.USER_AGENT))
        .thenReturn(Optional.of("Argo/123 CFNetwork/1333.0.4 Darwin/21.5.0"));
    when(paramResolver.get(anyString())).thenReturn(Optional.empty());
    when(cookieResolver.get(anyString())).thenReturn(Optional.empty());

    // Call the method under test
    Optional<DeviceTier> result =
        WebTierResolver.resolveDeviceTier(
            headerResolver, paramResolver, cookieResolver, webClientDetails);

    // Verify the result
    assertTrue(result.isPresent());
    assertEquals(DeviceTier.FOLLOWER, result.get());

    // Verify interactions
    verify(headerResolver).get(VisitResolvers.USER_AGENT);
    verify(paramResolver).get("inapp");
    verify(paramResolver).get("instub");
    verify(paramResolver).get("netflixClientPlatform");
  }

  // NEW TEST: Test for Windows client in isUnsupportedClient
  @Test
  public void resolveWindowsClient() {
    // Create WebClientDetails for Windows device
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Chrome")
                    .setSemVer(SemVer.newBuilder().setMajor(109).setMinor(0)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("Windows")
                    .setSemVer(SemVer.newBuilder().setMajor(10).setMinor(0)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Other"))
            .build();

    // Create user agent string
    String userAgent =
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36";

    // Parse browser info
    BrowserInfo browserInfo = BrowserInfo.parseBrowserInfo(userAgent, webClientDetails);

    // Create ResolverRequest directly for Windows client
    final ResolverRequest request =
        new ResolverRequest(
            true, false, ComparableVersion.of("5.0.0"), "windowsWebView", browserInfo);

    // Call determineDeviceTier directly
    final Optional<DeviceTier> deviceTier = WebTierResolver.determineDeviceTier(request);

    // Verify the result
    assertTrue(deviceTier.isPresent());
    assertEquals(DeviceTier.UNSUPPORTED, deviceTier.get());
  }

  // NEW TEST: Test for Windows WebView in isUnsupportedClient
  @Test
  public void resolveWindowsWebView() {
    // Create WebClientDetails for Windows WebView
    WebClientDetails webClientDetails =
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Edge OSS")
                    .setSemVer(SemVer.newBuilder().setMajor(109).setMinor(0)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("Windows") // Use "Windows" to ensure isWindows() returns true
                    .setSemVer(SemVer.newBuilder().setMajor(10).setMinor(0)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Other"))
            .build();

    // Create user agent string
    String userAgent =
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 Edg/109.0.1518.78";

    // Parse browser info
    BrowserInfo browserInfo = BrowserInfo.parseBrowserInfo(userAgent, webClientDetails);

    // Create ResolverRequest directly for Windows WebView
    final ResolverRequest request =
        new ResolverRequest(
            true, false, ComparableVersion.of("5.0.0"), "windowsWebView", browserInfo);

    // Call determineDeviceTier directly
    final Optional<DeviceTier> deviceTier = WebTierResolver.determineDeviceTier(request);

    // Verify the result
    assertTrue(deviceTier.isPresent());
    assertEquals(DeviceTier.UNSUPPORTED, deviceTier.get());
  }
}
