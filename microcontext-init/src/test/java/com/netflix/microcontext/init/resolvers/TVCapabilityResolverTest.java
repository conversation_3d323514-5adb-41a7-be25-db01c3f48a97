package com.netflix.microcontext.init.resolvers;

import static org.junit.jupiter.api.Assertions.assertTrue;

import netflix.context.client.CapabilitiesContext;
import netflix.context.client.FeatureCapability;
import netflix.context.client.LocalizationCapability;
import netflix.context.client.TitleCapability;
import org.junit.jupiter.api.Test;

public class TVCapabilityResolverTest {

  @Test
  public void testGetTvCapabilitiesWithHighMemoryRange() {
    // Test with memory range above the threshold
    CapabilitiesContext capabilities = TVCapabilityResolver.getTvCapabilities(150);

    // Check localization capabilities
    assertTrue(
        capabilities.getLocalizationCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == LocalizationCapability.defaultKidsProfile));

    // Check title capabilities
    assertTrue(
        capabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodeOrdering));
    assertTrue(
        capabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.seasonOrdering));
    assertTrue(
        capabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.hiddenEpisodeNumbers));
    assertTrue(
        capabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodicNewBadge));
    assertTrue(
        capabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodicAvailabilityMessage));
    assertTrue(
        capabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodeSkipping));

    // Check feature capabilities
    assertTrue(
        capabilities.getFeatureCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == FeatureCapability.supportsTop10));
    assertTrue(
        capabilities.getFeatureCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == FeatureCapability.supportsTop10Kids));
  }

  @Test
  public void testGetTvCapabilitiesWithLowMemoryRange() {
    // Test with memory range below the threshold
    CapabilitiesContext capabilities = TVCapabilityResolver.getTvCapabilities(50);

    // Check localization capabilities
    assertTrue(
        capabilities.getLocalizationCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == LocalizationCapability.defaultKidsProfile));

    // Check title capabilities
    assertTrue(
        capabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodeOrdering));
    assertTrue(
        capabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.seasonOrdering));
    assertTrue(
        capabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.hiddenEpisodeNumbers));
    assertTrue(
        capabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodicNewBadge));
    assertTrue(
        capabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodicAvailabilityMessage));
    assertTrue(
        capabilities.getTitleCapabilitiesList().stream()
            .anyMatch(cap -> cap.getEnumValue() == TitleCapability.episodeSkipping));

    // Check that feature capabilities do not include supportsTop10
    assertTrue(capabilities.getFeatureCapabilitiesList().isEmpty());
  }
}
