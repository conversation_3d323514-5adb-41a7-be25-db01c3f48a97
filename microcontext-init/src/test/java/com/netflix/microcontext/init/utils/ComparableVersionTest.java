package com.netflix.microcontext.init.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

public class ComparableVersionTest {
  @Test
  public void testVersionComparisonOnePart() {
    ComparableVersion v1 = ComparableVersion.of("1");
    assertEquals(1, v1.getMajor());
    assertEquals(0, v1.getMinor());
    assertEquals(0, v1.getPatch());
  }

  @Test
  public void testVersionComparisonTwoPart() {
    ComparableVersion v11 = ComparableVersion.of("1.1");
    assertTrue(v11.compareTo(ComparableVersion.of("1.2.0")) < 0);
    assertEquals(0, v11.compareTo(ComparableVersion.of("1.1.0")));
  }

  @Test
  public void testVersionComparisonThreePart() {
    ComparableVersion v111 = ComparableVersion.of(1, 1, 1);
    assertEquals(0, v111.compareTo(ComparableVersion.of("1.1.1-rc1-foo")));
    assertEquals(0, v111.compareTo(ComparableVersion.of("1.1.1-rc1-bar")));
  }

  @Test
  public void testNegatives() {
    assertEquals(ComparableVersion.DEFAULT, ComparableVersion.of("garbage"));
    assertEquals(ComparableVersion.DEFAULT, ComparableVersion.of(""));
    assertEquals(ComparableVersion.DEFAULT, ComparableVersion.of(null));
    assertEquals(ComparableVersion.DEFAULT, ComparableVersion.of("..."));
    assertEquals(ComparableVersion.DEFAULT, ComparableVersion.of("...---"));
    assertEquals(ComparableVersion.DEFAULT, ComparableVersion.of("1.2.3.4"));
  }

  @Test
  public void testStaticFactoryMethods() {
    // Test of(int major)
    ComparableVersion v1 = ComparableVersion.of(5);
    assertEquals(5, v1.getMajor());
    assertEquals(0, v1.getMinor());
    assertEquals(0, v1.getPatch());

    // Test of(int major, int minor)
    ComparableVersion v2 = ComparableVersion.of(5, 2);
    assertEquals(5, v2.getMajor());
    assertEquals(2, v2.getMinor());
    assertEquals(0, v2.getPatch());

    // Test of(int major, int minor, int patch)
    ComparableVersion v3 = ComparableVersion.of(5, 2, 3);
    assertEquals(5, v3.getMajor());
    assertEquals(2, v3.getMinor());
    assertEquals(3, v3.getPatch());
  }

  @Test
  public void testToString() {
    ComparableVersion v1 = ComparableVersion.of(1, 2, 3);
    assertEquals("1.2.3", v1.toString());

    ComparableVersion v2 = ComparableVersion.of(10);
    assertEquals("10.0.0", v2.toString());

    ComparableVersion v3 = ComparableVersion.of(7, 8);
    assertEquals("7.8.0", v3.toString());

    ComparableVersion v4 = ComparableVersion.DEFAULT;
    assertEquals("0.0.0", v4.toString());
  }

  @Test
  public void testEqualsAndHashCode() {
    ComparableVersion v1 = ComparableVersion.of(1, 2, 3);
    ComparableVersion v2 = ComparableVersion.of(1, 2, 3);
    ComparableVersion v3 = ComparableVersion.of(1, 2, 4);
    ComparableVersion v4 = ComparableVersion.of(1, 3, 3);
    ComparableVersion v5 = ComparableVersion.of(2, 2, 3);

    // Test equals
    assertEquals(v1, v2);
    assertNotEquals(v1, v3);
    assertNotEquals(v1, v4);
    assertNotEquals(v1, v5);
    assertNotEquals(v1, null);
    assertNotEquals(v1, "not a version");

    // Test hashCode
    assertEquals(v1.hashCode(), v2.hashCode());
  }

  @Test
  public void testIsEmpty() {
    ComparableVersion v1 = ComparableVersion.DEFAULT;
    ComparableVersion v2 = ComparableVersion.of(0, 0, 0);
    ComparableVersion v3 = ComparableVersion.of(1, 0, 0);

    assertTrue(v1.isEmpty());
    assertTrue(v2.isEmpty());
    assertFalse(v3.isEmpty());
  }

  @Test
  public void testCompareToComprehensive() {
    // Major version differences
    assertTrue(ComparableVersion.of(2, 0, 0).compareTo(ComparableVersion.of(1, 0, 0)) > 0);
    assertTrue(ComparableVersion.of(1, 0, 0).compareTo(ComparableVersion.of(2, 0, 0)) < 0);

    // Minor version differences
    assertTrue(ComparableVersion.of(1, 2, 0).compareTo(ComparableVersion.of(1, 1, 0)) > 0);
    assertTrue(ComparableVersion.of(1, 1, 0).compareTo(ComparableVersion.of(1, 2, 0)) < 0);

    // Patch version differences
    assertTrue(ComparableVersion.of(1, 1, 2).compareTo(ComparableVersion.of(1, 1, 1)) > 0);
    assertTrue(ComparableVersion.of(1, 1, 1).compareTo(ComparableVersion.of(1, 1, 2)) < 0);

    // Equal versions
    assertEquals(0, ComparableVersion.of(1, 2, 3).compareTo(ComparableVersion.of(1, 2, 3)));

    // Compare with DEFAULT
    assertTrue(ComparableVersion.of(1, 0, 0).compareTo(ComparableVersion.DEFAULT) > 0);
    assertTrue(ComparableVersion.DEFAULT.compareTo(ComparableVersion.of(1, 0, 0)) < 0);
    assertEquals(0, ComparableVersion.DEFAULT.compareTo(ComparableVersion.DEFAULT));
  }

  @Test
  public void testEdgeCases() {
    // Test handling of negative numbers in string parsing
    // The implementation should treat negative numbers as 0
    assertEquals(ComparableVersion.of(0, 0, 0), ComparableVersion.of("-1.-2.-3"));

    // Test handling of zero values
    ComparableVersion v1 = ComparableVersion.of("0.0.0");
    assertEquals(0, v1.getMajor());
    assertEquals(0, v1.getMinor());
    assertEquals(0, v1.getPatch());

    // Test handling of non-numeric characters that should be parsed as numbers
    assertEquals(ComparableVersion.DEFAULT, ComparableVersion.of("a.b.c"));

    // Test handling of leading zeros
    ComparableVersion v2 = ComparableVersion.of("01.02.03");
    assertEquals(1, v2.getMajor());
    assertEquals(2, v2.getMinor());
    assertEquals(3, v2.getPatch());
  }

  @Test
  public void testBoundaryValues() {
    // Test large version numbers
    ComparableVersion v1 =
        ComparableVersion.of("2147483647.2147483647.2147483647"); // Max int values
    assertEquals(2147483647, v1.getMajor());
    assertEquals(2147483647, v1.getMinor());
    assertEquals(2147483647, v1.getPatch());

    // Test version with mixed formats
    ComparableVersion v2 = ComparableVersion.of("100.0");
    assertEquals(100, v2.getMajor());
    assertEquals(0, v2.getMinor());
    assertEquals(0, v2.getPatch());
  }
}
