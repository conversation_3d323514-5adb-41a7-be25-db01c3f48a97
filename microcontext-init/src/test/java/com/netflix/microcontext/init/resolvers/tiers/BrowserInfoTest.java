package com.netflix.microcontext.init.resolvers.tiers;

import static org.junit.Assert.*;

import com.netflix.microcontext.init.utils.ComparableVersion;
import netflix.context.visit.DeviceDetails;
import netflix.context.visit.OSDetails;
import netflix.context.visit.SemVer;
import netflix.context.visit.UserAgentDetails;
import netflix.context.visit.WebClientDetails;
import org.junit.Test;

public class BrowserInfoTest {

  public static BrowserInfo getBrowserInfoAndroidChrome() {
    // {"user_agent": {"family": "Chrome", "major": "1", "minor": "2", "patch": "3"}, "os":
    // {"family": "Android", "major": "", "minor": "", "patch": "", "patch_minor": ""}, "device":
    // {"family": "Generic Smartphone"}}
    return BrowserInfo.parseBrowserInfo(
        "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/1.2.3 Mobile Safari/537.36",
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Chrome")
                    .setSemVer(SemVer.newBuilder().setMajor(1).setMinor(2).setPatch(3)))
            .setOsDetails(OSDetails.newBuilder().setFamily("Android"))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Generic Smartphone"))
            .build());
  }

  public static BrowserInfo getBrowserInfoAndroidBrowser() {
    // {"user_agent": {"family": "Samsung Internet", "major": "6", "minor": "4", "patch": ""}, "os":
    // {"family": "Android", "major": "13", "minor": "1", "patch": "", "patch_minor": ""}, "device":
    // {"family": "Samsung SM-N950F"}}
    return BrowserInfo.parseBrowserInfo(
        "Mozilla/5.0 (Linux; Android 13.1; SAMSUNG SM-N950F Build/NMF26X) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/6.4 Chrome/56.0.2924.87 Mobile Safari/537.36",
        WebClientDetails.newBuilder()
            .setUserAgentDetails(
                UserAgentDetails.newBuilder()
                    .setFamily("Samsung Internet")
                    .setSemVer(SemVer.newBuilder().setMajor(6).setMinor(4)))
            .setOsDetails(
                OSDetails.newBuilder()
                    .setFamily("Android")
                    .setSemVer(SemVer.newBuilder().setMajor(13).setMinor(1)))
            .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Samsung SM-N950F"))
            .build());
  }

  @Test
  public void parseBrowserInfoAndroidChrome() {
    final BrowserInfo browserInfo = getBrowserInfoAndroidChrome();
    assertTrue(browserInfo.isAndroid());
    assertTrue(browserInfo.isChrome());
    assertEquals(ComparableVersion.of(1, 2, 3), browserInfo.getBrowserVersion());
  }

  @Test
  public void parseBrowserInfoAndroidFirefox() {
    // {"user_agent": {"family": "Firefox Mobile", "major": "136", "minor": "0", "patch": ""}, "os":
    // {"family": "Android", "major": "", "minor": "", "patch": "", "patch_minor": ""}, "device":
    // {"family": "Generic Smartphone"}}
    final BrowserInfo browserInfo =
        BrowserInfo.parseBrowserInfo(
            "Mozilla/5.0 (Android 12; Mobile; rv:136.0) Gecko/136.0 Firefox/136.0",
            WebClientDetails.newBuilder()
                .setUserAgentDetails(
                    UserAgentDetails.newBuilder()
                        .setFamily("Firefox Mobile")
                        .setSemVer(SemVer.newBuilder().setMajor(136).setMinor(0)))
                .setOsDetails(OSDetails.newBuilder().setFamily("Android"))
                .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Generic Smartphone"))
                .build());
    assertTrue(browserInfo.isAndroid());
    assertFalse(browserInfo.isChrome());
    assertEquals(ComparableVersion.of(136, 0), browserInfo.getBrowserVersion());
  }

  @Test
  public void parseBrowserInfoAndroidBrowser() {
    final BrowserInfo browserInfo = getBrowserInfoAndroidBrowser();
    assertTrue(browserInfo.isAndroid());
    assertFalse(browserInfo.isChrome());
    assertEquals(ComparableVersion.of(6, 4), browserInfo.getBrowserVersion());
    assertEquals(ComparableVersion.of(13, 1), browserInfo.getOsVersion());
  }

  @Test
  public void parseBrowserInfoIOSBrowser() {
    // {"user_agent": {"family": "Mobile Safari", "major": "10", "minor": "0", "patch": ""}, "os":
    // {"family": "iOS", "major": "11", "minor": "1", "patch": "1", "patch_minor": ""}, "device":
    // {"family": "iPhone"}}
    final BrowserInfo browserInfo =
        BrowserInfo.parseBrowserInfo(
            "Mozilla/5.0 (iPhone; CPU iPhone OS 11.1.1 like Mac OS X) AppleWebKit/602.1.50 (KHTML, like Gecko) Version/10.0 Mobile/14A403 Safari/602.1",
            WebClientDetails.newBuilder()
                .setUserAgentDetails(
                    UserAgentDetails.newBuilder()
                        .setFamily("Mobile Safari")
                        .setSemVer(SemVer.newBuilder().setMajor(10).setMinor(0)))
                .setOsDetails(
                    OSDetails.newBuilder()
                        .setFamily("iOS")
                        .setSemVer(SemVer.newBuilder().setMajor(11).setMinor(1).setPatch(1)))
                .setDeviceDetails(DeviceDetails.newBuilder().setFamily("iPhone"))
                .build());
    assertFalse(browserInfo.isAndroid());
    assertFalse(browserInfo.isChrome());
    assertEquals(ComparableVersion.of(10, 0), browserInfo.getBrowserVersion());
    assertEquals(ComparableVersion.of(11, 1, 1), browserInfo.getOsVersion());
    assertTrue(browserInfo.isIOS());
  }

  @Test
  public void parseBrowserInfoDesktopSafari() {
    // {"user_agent": {"family": "Safari", "major": "100", "minor": "0", "patch": "4"}, "os":
    // {"family": "Mac OS X", "major": "10", "minor": "17", "patch": "4", "patch_minor": ""},
    // "device": {"family": "Other"}}
    final BrowserInfo browserInfo =
        BrowserInfo.parseBrowserInfo(
            "Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10.17.4; en-GB) AppleWebKit/605.1.5 (KHTML, like Gecko) Version/100.0.4 Safari/605.76",
            WebClientDetails.newBuilder()
                .setUserAgentDetails(
                    UserAgentDetails.newBuilder()
                        .setFamily("Safari")
                        .setSemVer(SemVer.newBuilder().setMajor(100).setMinor(0).setPatch(4)))
                .setOsDetails(
                    OSDetails.newBuilder()
                        .setFamily("Mac OS X")
                        .setSemVer(SemVer.newBuilder().setMajor(10).setMinor(17).setPatch(4)))
                .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Other"))
                .build());
    assertFalse(browserInfo.isAndroid());
    assertFalse(browserInfo.isChrome());
    assertEquals(ComparableVersion.of(100, 0, 4), browserInfo.getBrowserVersion());
    assertEquals(ComparableVersion.of(10, 17, 4), browserInfo.getOsVersion());
    assertFalse(browserInfo.isIOS());
    assertTrue(browserInfo.isSafari());
  }

  @Test
  public void parseBrowserInfoDesktopChrome() {
    // {"user_agent": {"family": "Chrome", "major": "10", "minor": "0", "patch": ""}, "os":
    // {"family": "Mac OS X", "major": "11", "minor": "2", "patch": "3", "patch_minor": ""},
    // "device": {"family": "Other"}}
    final BrowserInfo browserInfo =
        BrowserInfo.parseBrowserInfo(
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/10.0 Safari/537.36",
            WebClientDetails.newBuilder()
                .setUserAgentDetails(
                    UserAgentDetails.newBuilder()
                        .setFamily("Chrome")
                        .setSemVer(SemVer.newBuilder().setMajor(10).setMinor(0)))
                .setOsDetails(
                    OSDetails.newBuilder()
                        .setFamily("Mac OS X")
                        .setSemVer(SemVer.newBuilder().setMajor(11).setMinor(2).setPatch(3)))
                .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Other"))
                .build());
    assertFalse(browserInfo.isAndroid());
    assertTrue(browserInfo.isChrome());
    assertEquals(ComparableVersion.of(10, 0), browserInfo.getBrowserVersion());
    assertEquals(ComparableVersion.of(11, 2, 3), browserInfo.getOsVersion());
    assertFalse(browserInfo.isIOS());
    assertFalse(browserInfo.isSafari());
  }

  @Test
  public void parseBrowserInfoDesktopFirefox() {
    // {"user_agent": {"family": "Firefox", "major": "113", "minor": "0", "patch": ""}, "os":
    // {"family": "Windows 10", "major": "", "minor": "", "patch": "", "patch_minor": ""}, "device":
    // {"family": "Other"}}
    final BrowserInfo browserInfo =
        BrowserInfo.parseBrowserInfo(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:64.0) Gecko/20100101 Firefox/113.0",
            WebClientDetails.newBuilder()
                .setUserAgentDetails(
                    UserAgentDetails.newBuilder()
                        .setFamily("Firefox")
                        .setSemVer(SemVer.newBuilder().setMajor(113).setMinor(0)))
                .setOsDetails(OSDetails.newBuilder().setFamily("Windows 10"))
                .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Other"))
                .build());
    assertFalse(browserInfo.isAndroid());
    assertFalse(browserInfo.isChrome());
    assertEquals(ComparableVersion.of(113, 0), browserInfo.getBrowserVersion());
    assertFalse(browserInfo.isIOS());
    assertFalse(browserInfo.isSafari());
    assertTrue(browserInfo.isFirefox());
    assertTrue(browserInfo.isWindows());
  }

  @Test
  public void parseBrowserInfoDesktopOpera() {
    // {"user_agent": {"family": "Opera", "major": "63", "minor": "0", "patch": "0"}, "os":
    // {"family": "Windows 10", "major": "", "minor": "", "patch": "", "patch_minor": ""}, "device":
    // {"family": "Other"}}
    final BrowserInfo browserInfo =
        BrowserInfo.parseBrowserInfo(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.132 Safari/537.36 OPR/63.0.0.0",
            WebClientDetails.newBuilder()
                .setUserAgentDetails(
                    UserAgentDetails.newBuilder()
                        .setFamily("Opera")
                        .setSemVer(SemVer.newBuilder().setMajor(63).setMinor(0)))
                .setOsDetails(OSDetails.newBuilder().setFamily("Windows 10"))
                .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Other"))
                .build());
    assertFalse(browserInfo.isAndroid());
    assertFalse(browserInfo.isChrome());
    assertEquals(ComparableVersion.of(63), browserInfo.getBrowserVersion());
    assertFalse(browserInfo.isIOS());
    assertFalse(browserInfo.isSafari());
    assertFalse(browserInfo.isFirefox());
    assertTrue(browserInfo.isWindows());
    assertTrue(browserInfo.isOpera());
  }

  @Test
  public void parseBrowserInfoDesktopEdgeOSS() {
    // {"user_agent": {"family": "Edge OSS", "major": "94", "minor": "0", "patch": "992"}, "os":
    // {"family": "Windows 10", "major": "", "minor": "", "patch": "", "patch_minor": ""}, "device":
    // {"family": "Other"}}
    final BrowserInfo browserInfo =
        BrowserInfo.parseBrowserInfo(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36 Edg/94.0.992.38",
            WebClientDetails.newBuilder()
                .setUserAgentDetails(
                    UserAgentDetails.newBuilder()
                        .setFamily("Edge OSS")
                        .setSemVer(SemVer.newBuilder().setMajor(94).setMinor(0).setPatch(992)))
                .setOsDetails(OSDetails.newBuilder().setFamily("Windows 10"))
                .setDeviceDetails(DeviceDetails.newBuilder().setFamily("Other"))
                .build());
    assertFalse(browserInfo.isAndroid());
    assertFalse(browserInfo.isChrome());
    assertEquals(ComparableVersion.of(94, 0, 992), browserInfo.getBrowserVersion());
    assertFalse(browserInfo.isIOS());
    assertFalse(browserInfo.isSafari());
    assertFalse(browserInfo.isFirefox());
    assertTrue(browserInfo.isWindows());
    assertFalse(browserInfo.isOpera());
    assertTrue(browserInfo.isEdgeOSS());
  }
}
