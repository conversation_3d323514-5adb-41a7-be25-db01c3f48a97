package com.netflix.microcontext.init.resolvers;

import static org.junit.Assert.*;

import com.netflix.server.context.RequestContext;
import com.netflix.type.protogen.BasicTypes.Locale;
import java.util.List;
import java.util.Optional;
import org.junit.Test;

public class BaseResolverTest {

  @Test
  public void localeContext() {
    RequestContext requestContext = new RequestContext(null, true);
    requestContext.setLocale("[\"en-US\", \"es-US\", \"en-CA\"]");
    final Optional<List<Locale>> locales = BaseResolvers.localeContext(requestContext);
    assertTrue(locales.isPresent());
    final List<Locale> list = locales.get();
    assertEquals(3, list.size());
    assertEquals("en-US", list.get(0).getId());
    assertEquals("es-US", list.get(1).getId());
    assertEquals("en-CA", list.get(2).getId());
  }
}
