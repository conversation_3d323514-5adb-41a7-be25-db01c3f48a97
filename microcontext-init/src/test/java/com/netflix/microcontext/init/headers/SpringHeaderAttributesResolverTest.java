package com.netflix.microcontext.init.headers;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.util.List;
import java.util.Optional;
import org.junit.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.ServletWebRequest;

public class SpringHeaderAttributesResolverTest {

  @Test
  public void testEmpty() {
    final HeaderResolver springHeaderResolver =
        new SpringHeaderAttributesResolver(new ServletWebRequest(new MockHttpServletRequest()));
    final List<String> all = springHeaderResolver.getAll("foo");
    assertNotNull(all);
    assertEquals(0, all.size());

    final Optional<String> get = springHeaderResolver.get("foo");
    assertNotNull(get);
    assertFalse(get.isPresent());

    final boolean contains = springHeaderResolver.contains("foo");
    assertFalse(contains);
  }

  @Test
  public void testHeader() {
    final MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
    mockHttpServletRequest.setAttribute("foo", "baz");
    mockHttpServletRequest.addHeader("foo", "bar");
    final HeaderResolver springHeaderResolver =
        new SpringHeaderAttributesResolver(new ServletWebRequest(mockHttpServletRequest), false);
    final List<String> all = springHeaderResolver.getAll("foo");
    assertNotNull(all);
    assertEquals(2, all.size());

    final Optional<String> get = springHeaderResolver.get("foo");
    assertNotNull(get);
    assertTrue(get.isPresent());
    assertEquals("bar", get.get());

    final boolean contains = springHeaderResolver.contains("foo");
    assertTrue(contains);
  }

  @Test
  public void testAttribute() {
    final MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
    mockHttpServletRequest.setAttribute("foo", "baz");
    mockHttpServletRequest.addHeader("foo", "bar");
    final HeaderResolver springHeaderResolver =
        new SpringHeaderAttributesResolver(new ServletWebRequest(mockHttpServletRequest));
    final List<String> all = springHeaderResolver.getAll("foo");
    assertNotNull(all);
    assertEquals(2, all.size());

    final Optional<String> get = springHeaderResolver.get("foo");
    assertNotNull(get);
    assertTrue(get.isPresent());
    assertEquals("baz", get.get());

    final boolean contains = springHeaderResolver.contains("foo");
    assertTrue(contains);
  }

  @Test
  public void testAttributeHeaderEmpty() {
    final MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
    mockHttpServletRequest.setAttribute("foo", "baz");
    final HeaderResolver springHeaderResolver =
        new SpringHeaderAttributesResolver(new ServletWebRequest(mockHttpServletRequest));
    final List<String> all = springHeaderResolver.getAll("foo");
    assertNotNull(all);
    assertEquals(1, all.size());

    final Optional<String> get = springHeaderResolver.get("foo");
    assertNotNull(get);
    assertTrue(get.isPresent());
    assertEquals("baz", get.get());

    final boolean contains = springHeaderResolver.contains("foo");
    assertTrue(contains);
  }

  @Test
  public void testHeaderAttributeEmpty() {
    final MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
    mockHttpServletRequest.addHeader("foo", "bar");
    final HeaderResolver springHeaderResolver =
        new SpringHeaderAttributesResolver(new ServletWebRequest(mockHttpServletRequest));
    final List<String> all = springHeaderResolver.getAll("foo");
    assertNotNull(all);
    assertEquals(1, all.size());

    final Optional<String> get = springHeaderResolver.get("foo");
    assertNotNull(get);
    assertTrue(get.isPresent());
    assertEquals("bar", get.get());

    final boolean contains = springHeaderResolver.contains("foo");
    assertTrue(contains);
  }
}
