package com.netflix.microcontext.init.resolvers.geo;

import static org.junit.Assert.*;

import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.google.type.LatLng;
import com.netflix.grpc.shaded.com.google.common.base.Splitter;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import netflix.context.geo.GeoContext;
import org.junit.Test;

public class GeosTest {

  private static final String geoRaw =
      "zip=60313&city_key=102303&ipaddress=*************&real_ipaddress=*******&is_override=true&is_fallback=true&subdivision_key=50162&city=FRANKFURT+AM+MAIN&timezone=GMT%2B1&iana_timezone=Europe%2FBerlin&metro_key=1126&nf_ip_provider=blend&long=8.68&country_code=DE&asnum=262287&dma=276003&company=IPXO&real_country=DE&network_type=hosted&lat=50.12&region_code=HE&is_country_mapped=true";

  @Test
  public void getOverrideTimezoneHeaderMalformed() {
    final Optional<String> timezone = Geos.normalizeZoneId("foo");
    assertFalse(timezone.isPresent());
  }

  @Test
  public void getOverrideTimezoneHeader() {
    final Optional<String> timezone = Geos.normalizeZoneId("America/Denver");
    assertTrue(timezone.isPresent());
    assertEquals("America/Denver", timezone.get());
  }

  @Test
  public void testParseDouble() {
    Map<String, String> attributes = Collections.singletonMap("long", "definitelynotlong");
    final GeoContext context = Geos.context(null, attributes);
    assertNotNull(context);
    final LatLng coordinates = context.getCoordinates();
    assertNotNull(coordinates);
    assertEquals(LatLng.getDefaultInstance(), coordinates);
  }

  @Test
  public void testSerde() {
    final Map<String, String> attributes = getAttributes();
    final GeoContext context = Geos.context(attributes);
    assertTrue(context.getCountryMapped());
    final Optional<String> company = context.getOptionalCompany();
    assertTrue(company.isPresent());
    assertEquals("IPXO", company.get());
    final Optional<String> networkType = context.getOptionalNetworkType();
    assertTrue(networkType.isPresent());
    assertEquals("hosted", networkType.get());
    final Map<String, String> roundtrip = Geos.attributeMap(context);
    final MapDifference<String, String> difference = Maps.difference(attributes, roundtrip);
    assertTrue(difference.areEqual());
  }

  private static Map<String, String> getAttributes() {
    return Splitter.on("&").withKeyValueSeparator("=").split(geoRaw);
  }
}
