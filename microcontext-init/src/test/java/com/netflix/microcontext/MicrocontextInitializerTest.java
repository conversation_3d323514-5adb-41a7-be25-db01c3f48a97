package com.netflix.microcontext;

import static org.junit.Assert.*;

import com.google.protobuf.Message;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.request.CookieResolver;
import com.netflix.passport.test.TestPassport;
import com.netflix.server.context.RequestContext;
import javax.annotation.Nonnull;
import netflix.context.Context;
import netflix.context.user.User;
import netflix.context.user.UserContext;
import org.junit.Test;

public class MicrocontextInitializerTest {

  @Test
  public void init() {
    final Context context = MicrocontextInitializer.init();
    assertNotNull("Context is null", context);
    assertTrue("No country", context.hasCountry());
    assertEquals("Default country", "US", context.getCountry().getId());
    assertEquals("Countries match", context.getCountry(), context.getCountry());
    assertTrue("client", isDefault(context.getClient()));
    assertTrue("device", isDefault(context.getDevice()));
    System.out.println(context);
  }

  @Test
  public void initUserPassport() {
    RequestContext requestContext = RequestContext.fromKeySupplier(key -> null);
    final UserContext userContext =
        UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(123)).build();
    final TestPassport randomTestPassport = TestPassport.createRandomTestPassport(true, false);
    final Context context =
        MicrocontextInitializer.init(
            HeaderResolver.EMPTY,
            ParamResolver.EMPTY,
            CookieResolver.EMPTY,
            randomTestPassport.toPassportIdentity(),
            null,
            null,
            requestContext,
            null,
            null,
            userContext,
            null,
            false,
            false);
    System.out.println(context);
    assertNotNull("Context is null", context);
    assertTrue("No country", context.hasCountry());
    assertEquals("Default country", "US", context.getCountry().getId());
    assertEquals("Countries match", context.getCountry(), context.getCountry());
    assertTrue("client", isDefault(context.getClient()));
    assertTrue("device", isDefault(context.getDevice()));
    assertTrue("user", isDefault(context.getUser()));
  }

  private static boolean isDefault(@Nonnull Message m) {
    return m == m.getDefaultInstanceForType();
  }
}
