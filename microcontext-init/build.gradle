apply plugin: 'netflix.jvm-library'
apply plugin: 'netflix.spring-boot-netflix-library'

dependencies {
    api project(":microcontext-model")
    implementation "netflix:basicTypes-proto-bridge:latest.release"
    implementation "netflix:nfi18n-core:latest.release"
    implementation "netflix:subscriberservice-common:latest.release"
    implementation 'netflix:server-context'
    implementation 'netflix:geoip-common:latest.release'
    implementation "org.slf4j:slf4j-api"
    compileOnly "org.springframework:spring-web"
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.core:jackson-core'
    implementation 'com.google.guava:guava'

    testImplementation "com.netflix.spring:spring-boot-netflix-starter-test"
    testImplementation "com.netflix.passport.test:passport-test-core:latest.release"
}

tasks.named('test', Test).configure {
    useJUnitPlatform()
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(8)
    }
}


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations:latest.release'
}
