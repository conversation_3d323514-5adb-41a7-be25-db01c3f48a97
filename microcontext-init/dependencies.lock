{"annotationProcessor": {"com.netflix.spring:spring-boot-netflix-application-dependencies": {"locked": "2.7.226"}}, "compileClasspath": {"com.fasterxml.jackson.core:jackson-annotations": {"locked": "2.17.3", "transitive": ["com.fasterxml.jackson.core:jackson-databind"]}, "com.fasterxml.jackson.core:jackson-core": {"locked": "2.17.3", "transitive": ["com.fasterxml.jackson.core:jackson-databind"]}, "com.fasterxml.jackson.core:jackson-databind": {"locked": "2.17.3"}, "com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx"]}, "com.google.guava:guava": {"locked": "20.0"}, "com.ibm.icu:icu4j": {"locked": "73.2", "transitive": ["netflix:nfi18n-core"]}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:passport"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0", "transitive": ["com.netflix.microcontext:microcontext-model"]}, "com.netflix.microcontext:microcontext-model": {"project": true}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "com.netflix.spectator:spectator-api": {"locked": "0.68.0", "transitive": ["netflix:server-context"]}, "com.netflix.spring:spring-boot-netflix-application-dependencies": {"locked": "2.7.226"}, "javax.servlet:javax.servlet-api": {"locked": "3.1.0", "transitive": ["netflix:server-context"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix:basicTypes": {"locked": "1.86.0", "transitive": ["netflix:basicTypes-proto-bridge", "netflix:server-context"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:basicTypes-proto-bridge", "netflix:passport"]}, "netflix:basicTypes-proto-bridge": {"locked": "1.86.0"}, "netflix:geoip-common": {"locked": "2.324"}, "netflix:nfi18n-core": {"locked": "2.248"}, "netflix:passport": {"locked": "4.6.0", "transitive": ["netflix:subscriberservice-proto-definition"]}, "netflix:server-context": {"locked": "4.7.633"}, "netflix:subscriberservice-common": {"locked": "5.66.0"}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:subscriberservice-common"]}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}, "org.slf4j:slf4j-api": {"locked": "1.7.36", "transitive": ["com.netflix.spectator:spectator-api", "netflix:server-context", "netflix:subscriberservice-common"]}, "org.springframework:spring-beans": {"locked": "5.3.39", "transitive": ["org.springframework:spring-web"]}, "org.springframework:spring-core": {"locked": "5.3.39", "transitive": ["org.springframework:spring-beans", "org.springframework:spring-web"]}, "org.springframework:spring-jcl": {"locked": "5.3.39", "transitive": ["org.springframework:spring-core"]}, "org.springframework:spring-web": {"locked": "5.3.39"}}, "nebulaRecommenderBom": {"com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations": {"locked": "2.7.226"}}, "resolutionRules": {"com.netflix.microcontext:microcontext": {"project": true}, "com.netflix.nebula:gradle-resolution-rules": {"locked": "0.82.0", "transitive": ["com.netflix.microcontext:microcontext"]}, "netflix.nebula.resolutionrules:resolution-rules": {"locked": "1.357.0", "transitive": ["com.netflix.microcontext:microcontext"]}}, "runtimeClasspath": {"antlr:antlr": {"locked": "2.7.7", "transitive": ["org.antlr:antlr-runtime", "org.antlr:stringtemplate"]}, "aopalliance:aopalliance": {"locked": "1.0", "transitive": ["com.google.inject:guice"]}, "ch.qos.reload4j:reload4j": {"locked": "1.2.25", "transitive": ["com.netflix.blitz4j:blitz4j", "netflix:platform-jdk-compat"]}, "com.amazonaws:aws-java-sdk-core": {"locked": "1.12.783", "transitive": ["netflix:platform-core"]}, "com.fasterxml.jackson.core:jackson-annotations": {"locked": "2.17.3", "transitive": ["com.fasterxml.jackson.core:jackson-databind", "com.netflix.archaius:archaius-core"]}, "com.fasterxml.jackson.core:jackson-core": {"locked": "2.17.3", "transitive": ["com.fasterxml.jackson.core:jackson-databind", "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor", "com.netflix.archaius:archaius-core"]}, "com.fasterxml.jackson.core:jackson-databind": {"locked": "2.17.3", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor", "com.netflix.archaius:archaius-core", "com.netflix.ksclient:ksclient-api", "netflix:platform-core"]}, "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor": {"locked": "2.17.3", "transitive": ["com.amazonaws:aws-java-sdk-core"]}, "com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1", "transitive": ["netflix:basicTypes", "netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge", "netflix:geoip-common"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "com.netflix.archaius:archaius-core", "com.netflix.netflix-commons:netflix-infix", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:netflix-config"]}, "com.google.code.gson:gson": {"locked": "2.8.9", "transitive": ["com.netflix.netflix-commons:netflix-infix", "netflix.com.google.protobuf:protobuf-java-util-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "com.google.guava:guava": {"locked": "20.0", "transitive": ["com.google.inject:guice", "com.netflix.archaius:archaius-core", "com.netflix.netflix-commons:netflix-infix", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "netflix:platform-core", "netflix:platform-logimpl", "netflix:platform-utils"]}, "com.google.inject.extensions:guice-multibindings": {"locked": "4.1.0", "transitive": ["netflix:atlas-client"]}, "com.google.inject:guice": {"locked": "4.1.0", "transitive": ["com.google.inject.extensions:guice-multibindings", "com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client"]}, "com.ibm.icu:icu4j": {"locked": "73.2", "transitive": ["netflix:nfi18n-core"]}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:passport"]}, "com.netflix.archaius:archaius-core": {"locked": "0.7.12", "transitive": ["com.netflix.blitz4j:blitz4j", "com.netflix.netflix-commons:netflix-eventbus", "netflix:netflix-config", "netflix:platform-core"]}, "com.netflix.archaius:archaius2-api": {"locked": "2.5.0", "transitive": ["com.netflix.archaius:archaius2-core"]}, "com.netflix.archaius:archaius2-core": {"locked": "2.5.0", "transitive": ["com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client"]}, "com.netflix.blitz4j:blitz4j": {"locked": "1.42.0", "transitive": ["netflix:platform-core"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0", "transitive": ["com.netflix.microcontext:microcontext-model"]}, "com.netflix.frigga:frigga": {"locked": "0.26.0", "transitive": ["netflix:platform-core"]}, "com.netflix.governator:governator-api": {"locked": "1.17.13", "transitive": ["netflix:nfi18n-core"]}, "com.netflix.ksclient:ksclient-api": {"locked": "3.11.0", "transitive": ["netflix:platform-core", "netflix:platform-logimpl"]}, "com.netflix.microcontext:microcontext-model": {"project": true}, "com.netflix.netflix-commons:netflix-eventbus": {"locked": "0.3.0", "transitive": ["netflix:nf-eventbus-core", "netflix:platform-core"]}, "com.netflix.netflix-commons:netflix-infix": {"locked": "0.3.0", "transitive": ["com.netflix.netflix-commons:netflix-eventbus"]}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "com.netflix.servo:servo-apache": {"locked": "0.13.2", "transitive": ["netflix:atlas-client"]}, "com.netflix.servo:servo-core": {"locked": "0.13.2", "transitive": ["com.netflix.blitz4j:blitz4j", "com.netflix.netflix-commons:netflix-eventbus", "com.netflix.servo:servo-apache", "com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client", "netflix:geoip-common", "netflix:netflix-config"]}, "com.netflix.spectator:spectator-api": {"locked": "1.6.6", "transitive": ["com.netflix.ksclient:ksclient-api", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "com.netflix.spectator:spectator-ext-gc", "com.netflix.spectator:spectator-ext-ipc", "com.netflix.spectator:spectator-ext-jvm", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spectator:spectator-reg-atlas", "netflix:atlas-client", "netflix:platform-core", "netflix:server-context"]}, "com.netflix.spectator:spectator-ext-gc": {"locked": "1.6.6", "transitive": ["com.netflix.spectator:spectator-nflx-plugin"]}, "com.netflix.spectator:spectator-ext-ipc": {"locked": "1.6.6", "transitive": ["com.netflix.spectator:spectator-reg-atlas", "netflix:atlas-client"]}, "com.netflix.spectator:spectator-ext-jvm": {"locked": "1.6.6", "transitive": ["com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client"]}, "com.netflix.spectator:spectator-nflx-plugin": {"locked": "1.6.6", "transitive": ["netflix:atlas-client"]}, "com.netflix.spectator:spectator-nflx-tagging": {"locked": "1.6.6", "transitive": ["com.netflix.spectator:spectator-nflx-plugin"]}, "com.netflix.spectator:spectator-reg-atlas": {"locked": "1.6.6", "transitive": ["com.netflix.spectator:spectator-nflx-plugin"]}, "com.netflix.spring:spring-boot-netflix-application-dependencies": {"locked": "2.7.226"}, "com.netflix.tracing:netflix-tracing-tags": {"locked": "0.660.0", "transitive": ["netflix:platform-core"]}, "com.sun.activation:jakarta.activation": {"locked": "1.2.2", "transitive": ["com.sun.xml.bind:jaxb-impl"]}, "com.sun.jersey:jersey-client": {"locked": "1.20.1", "transitive": ["netflix:platform-core", "netflix:platform-logimpl"]}, "com.sun.jersey:jersey-core": {"locked": "1.20.1", "transitive": ["com.sun.jersey:jersey-client", "netflix:platform-jdk-compat"]}, "com.sun.xml.bind:jaxb-impl": {"locked": "2.3.6", "transitive": ["netflix:platform-jdk-compat"]}, "com.thoughtworks.xstream:xstream": {"locked": "1.4.21", "transitive": ["netflix:netflix-config"]}, "com.typesafe:config": {"locked": "1.4.2", "transitive": ["com.netflix.spectator:spectator-ext-jvm", "netflix:atlas-client"]}, "commons-beanutils:commons-beanutils": {"locked": "1.9.4", "transitive": ["netflix:netflix-config"]}, "commons-codec:commons-codec": {"locked": "1.15", "transitive": ["com.amazonaws:aws-java-sdk-core", "netflix:platform-core", "org.apache.httpcomponents:httpclient"]}, "commons-collections:commons-collections": {"locked": "3.2.2", "transitive": ["com.netflix.blitz4j:blitz4j", "commons-beanutils:commons-beanutils", "netflix:platform-core"]}, "commons-configuration:commons-configuration": {"locked": "1.10", "transitive": ["com.netflix.archaius:archaius-core", "com.netflix.blitz4j:blitz4j", "netflix:netflix-config", "netflix:nfi18n-core", "netflix:nflibrary-slim", "netflix:platform-core", "netflix:platform-logimpl"]}, "commons-io:commons-io": {"locked": "2.7", "transitive": ["netflix:nfi18n-core"]}, "commons-jxpath:commons-jxpath": {"locked": "1.3", "transitive": ["com.netflix.netflix-commons:netflix-infix"]}, "commons-lang:commons-lang": {"locked": "2.6", "transitive": ["commons-configuration:commons-configuration", "netflix:geoip-common", "netflix:platform-core", "netflix:platform-utils"]}, "commons-logging:commons-logging": {"locked": "1.2", "transitive": ["com.amazonaws:aws-java-sdk-core", "commons-beanutils:commons-beanutils", "commons-configuration:commons-configuration", "netflix:platform-core", "org.apache.httpcomponents:httpclient"]}, "io.github.x-stream:mxparser": {"locked": "1.2.2", "transitive": ["com.thoughtworks.xstream:xstream"]}, "io.zipkin.brave:brave": {"locked": "5.13.3", "transitive": ["netflix:platform-core"]}, "io.zipkin.reporter2:zipkin-reporter": {"locked": "2.16.3", "transitive": ["io.zipkin.reporter2:zipkin-reporter-brave"]}, "io.zipkin.reporter2:zipkin-reporter-brave": {"locked": "2.16.3", "transitive": ["io.zipkin.brave:brave"]}, "io.zipkin.zipkin2:zipkin": {"locked": "2.23.2", "transitive": ["io.zipkin.reporter2:zipkin-reporter"]}, "jakarta.xml.bind:jakarta.xml.bind-api": {"locked": "2.3.3", "transitive": ["com.sun.xml.bind:jaxb-impl"]}, "javax.activation:javax.activation-api": {"locked": "1.2.0", "transitive": ["javax.xml.bind:jaxb-api"]}, "javax.inject:javax.inject": {"locked": "1", "transitive": ["com.google.inject:guice", "com.netflix.archaius:archaius2-api", "com.netflix.governator:governator-api", "com.netflix.ksclient:ksclient-api", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spectator:spectator-reg-atlas", "netflix:nf-eventbus-core"]}, "javax.servlet:javax.servlet-api": {"locked": "3.1.0", "transitive": ["com.netflix.netflix-commons:netflix-infix", "netflix:server-context"]}, "javax.ws.rs:jsr311-api": {"locked": "1.1.1", "transitive": ["com.sun.jersey:jersey-core", "netflix:platform-core"]}, "javax.xml.bind:jaxb-api": {"locked": "2.5.0", "transitive": ["netflix:platform-jdk-compat"]}, "joda-time:joda-time": {"locked": "2.12.7", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.netflix.netflix-commons:netflix-infix", "netflix:platform-utils"]}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-lite-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix:atlas-client": {"locked": "2.29.10", "transitive": ["netflix:platform-utils"]}, "netflix:basicTypes": {"locked": "1.86.0", "transitive": ["netflix:basicTypes-proto-bridge", "netflix:geoip-common", "netflix:nfi18n-core", "netflix:server-context"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:basicTypes-proto-bridge", "netflix:passport"]}, "netflix:basicTypes-proto-bridge": {"locked": "1.86.0"}, "netflix:geoip-common": {"locked": "2.324"}, "netflix:i18n-dictionaries": {"locked": "0.205.0", "transitive": ["netflix:nfi18n-core"]}, "netflix:moduleRegistry": {"locked": "1.50.0", "transitive": ["netflix:nflibrary-slim"]}, "netflix:netflix-config": {"locked": "4.7.633", "transitive": ["netflix:geoip-common", "netflix:nflibrary-slim", "netflix:platform-core", "netflix:platform-logimpl", "netflix:platform-utils"]}, "netflix:nf-eventbus-core": {"locked": "1.32.0", "transitive": ["netflix:platform-core", "netflix:platform-logimpl"]}, "netflix:nfi18n-core": {"locked": "2.248"}, "netflix:nflibrary-slim": {"locked": "4.7.633", "transitive": ["netflix:nfi18n-core"]}, "netflix:ngl": {"locked": "0.49.0", "transitive": ["netflix:nfi18n-core"]}, "netflix:passport": {"locked": "4.6.0", "transitive": ["netflix:subscriberservice-proto-definition"]}, "netflix:platform-core": {"locked": "4.7.633", "transitive": ["netflix:platform-logimpl", "netflix:platform-utils"]}, "netflix:platform-jdk-compat": {"locked": "4.7.633", "transitive": ["netflix:platform-core"]}, "netflix:platform-logimpl": {"locked": "4.7.633", "transitive": ["netflix:platform-utils"]}, "netflix:platform-utils": {"locked": "4.7.633", "transitive": ["netflix:nfi18n-core"]}, "netflix:server-context": {"locked": "4.7.633", "transitive": ["netflix:geoip-common", "netflix:netflix-config", "netflix:platform-core", "netflix:platform-utils"]}, "netflix:statistics": {"locked": "1.7.0", "transitive": ["netflix:platform-utils"]}, "netflix:subscriberservice-common": {"locked": "5.66.0"}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:subscriberservice-common"]}, "org.antlr:antlr-runtime": {"locked": "3.4", "transitive": ["com.netflix.netflix-commons:netflix-infix"]}, "org.antlr:stringtemplate": {"locked": "3.2.1", "transitive": ["org.antlr:antlr-runtime"]}, "org.apache.commons:commons-lang3": {"locked": "3.3.2", "transitive": ["com.netflix.archaius:archaius2-core"]}, "org.apache.commons:commons-math": {"locked": "2.2", "transitive": ["com.netflix.netflix-commons:netflix-eventbus", "netflix:platform-utils"]}, "org.apache.httpcomponents:httpclient": {"locked": "4.5.13", "transitive": ["com.amazonaws:aws-java-sdk-core"]}, "org.apache.httpcomponents:httpcore": {"locked": "4.4.13", "transitive": ["org.apache.httpcomponents:httpclient"]}, "org.aspectj:aspectjweaver": {"locked": "1.9.5", "transitive": ["netflix:platform-core"]}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}, "org.codehaus.jackson:jackson-core-asl": {"locked": "1.10.3", "transitive": ["netflix:platform-jdk-compat", "org.codehaus.jackson:jackson-mapper-asl"]}, "org.codehaus.jackson:jackson-mapper-asl": {"locked": "1.10.3", "transitive": ["netflix:netflix-config"]}, "org.slf4j:slf4j-api": {"locked": "1.7.36", "transitive": ["com.netflix.archaius:archaius-core", "com.netflix.archaius:archaius2-api", "com.netflix.archaius:archaius2-core", "com.netflix.blitz4j:blitz4j", "com.netflix.netflix-commons:netflix-eventbus", "com.netflix.netflix-commons:netflix-infix", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "com.netflix.spectator:spectator-api", "com.netflix.spectator:spectator-ext-gc", "com.netflix.spectator:spectator-ext-ipc", "com.netflix.spectator:spectator-ext-jvm", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spectator:spectator-nflx-tagging", "com.netflix.spectator:spectator-reg-atlas", "netflix:atlas-client", "netflix:geoip-common", "netflix:nf-eventbus-core", "netflix:nflibrary-slim", "netflix:server-context", "netflix:subscriberservice-common"]}, "xmlpull:xmlpull": {"locked": "*******", "transitive": ["io.github.x-stream:mxparser"]}}, "testAnnotationProcessor": {"com.netflix.spring:spring-boot-netflix-application-dependencies": {"locked": "2.7.226"}}, "testCompileClasspath": {"com.fasterxml.jackson.core:jackson-annotations": {"locked": "2.17.3", "transitive": ["com.fasterxml.jackson.core:jackson-databind"]}, "com.fasterxml.jackson.core:jackson-core": {"locked": "2.17.3", "transitive": ["com.fasterxml.jackson.core:jackson-databind"]}, "com.fasterxml.jackson.core:jackson-databind": {"locked": "2.17.3"}, "com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx"]}, "com.google.guava:guava": {"locked": "20.0"}, "com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}, "com.ibm.icu:icu4j": {"locked": "73.2", "transitive": ["netflix:nfi18n-core"]}, "com.jayway.jsonpath:json-path": {"locked": "2.7.0", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["com.netflix.microcontext:microcontext-model", "com.netflix.passport.test:passport-test-core", "netflix:passport"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0", "transitive": ["com.netflix.microcontext:microcontext-model"]}, "com.netflix.microcontext:microcontext-model": {"project": true}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.passport.test:passport-test-core": {"locked": "0.41.0"}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "com.netflix.spectator:spectator-api": {"locked": "0.68.0", "transitive": ["netflix:server-context"]}, "com.netflix.spring.devagent:sbn-dev-agent-client": {"locked": "0.0.13", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "com.netflix.spring:spring-boot-netflix": {"locked": "2.7.226", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "com.netflix.spring:spring-boot-netflix-application-dependencies": {"locked": "2.7.226"}, "com.netflix.spring:spring-boot-netflix-starter-test": {"locked": "2.7.226"}, "com.netflix.spring:spring-boot-netflix-test": {"locked": "2.7.226", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "jakarta.activation:jakarta.activation-api": {"locked": "1.2.2", "transitive": ["jakarta.xml.bind:jakarta.xml.bind-api"]}, "jakarta.annotation:jakarta.annotation-api": {"locked": "1.3.5", "transitive": ["org.springframework.boot:spring-boot-starter"]}, "jakarta.xml.bind:jakarta.xml.bind-api": {"locked": "2.3.3", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "javax.servlet:javax.servlet-api": {"locked": "3.1.0", "transitive": ["netflix:server-context"]}, "junit:junit": {"locked": "4.13.2", "transitive": ["org.junit.vintage:junit-vintage-engine"]}, "net.bytebuddy:byte-buddy": {"locked": "1.12.9", "transitive": ["org.mockito:mockito-core"]}, "net.bytebuddy:byte-buddy-agent": {"locked": "1.12.9", "transitive": ["org.mockito:mockito-core"]}, "net.minidev:accessors-smart": {"locked": "2.4.7", "transitive": ["net.minidev:json-smart"]}, "net.minidev:json-smart": {"locked": "2.4.7", "transitive": ["com.jayway.jsonpath:json-path"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix:basicTypes": {"locked": "1.86.0", "transitive": ["netflix:basicTypes-proto-bridge", "netflix:server-context"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:basicTypes-proto-bridge", "netflix:passport"]}, "netflix:basicTypes-proto-bridge": {"locked": "1.86.0"}, "netflix:geoip-common": {"locked": "2.324"}, "netflix:nfi18n-core": {"locked": "2.248"}, "netflix:passport": {"locked": "4.6.0", "transitive": ["netflix:subscriberservice-proto-definition"]}, "netflix:server-context": {"locked": "4.7.633"}, "netflix:subscriberservice-common": {"locked": "5.66.0"}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:subscriberservice-common"]}, "org.apiguardian:apiguardian-api": {"locked": "1.1.2", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.jupiter:junit-jupiter-params", "org.junit.platform:junit-platform-commons", "org.junit.platform:junit-platform-engine", "org.junit.vintage:junit-vintage-engine"]}, "org.assertj:assertj-core": {"locked": "3.22.0", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}, "org.hamcrest:hamcrest": {"locked": "2.2", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.junit.jupiter:junit-jupiter": {"locked": "5.12.2", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.junit.jupiter:junit-jupiter-api": {"locked": "5.12.2", "transitive": ["org.junit.jupiter:junit-jupiter", "org.junit.jupiter:junit-jupiter-params"]}, "org.junit.jupiter:junit-jupiter-params": {"locked": "5.12.2", "transitive": ["org.junit.jupiter:junit-jupiter"]}, "org.junit.platform:junit-platform-commons": {"locked": "1.12.2", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.platform:junit-platform-engine"]}, "org.junit.platform:junit-platform-engine": {"locked": "1.12.2", "transitive": ["org.junit.vintage:junit-vintage-engine"]}, "org.junit.vintage:junit-vintage-engine": {"locked": "5.12.2", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "org.mockito:mockito-core": {"locked": "4.5.1", "transitive": ["org.mockito:mockito-junit-jupiter", "org.springframework.boot:spring-boot-starter-test"]}, "org.mockito:mockito-junit-jupiter": {"locked": "4.5.1", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.opentest4j:opentest4j": {"locked": "1.3.0", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.platform:junit-platform-engine"]}, "org.ow2.asm:asm": {"locked": "9.1", "transitive": ["net.minidev:accessors-smart"]}, "org.skyscreamer:jsonassert": {"locked": "1.5.1", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.slf4j:slf4j-api": {"locked": "1.7.36", "transitive": ["com.jayway.jsonpath:json-path", "com.netflix.spectator:spectator-api", "com.netflix.spring:spring-boot-netflix", "netflix:server-context", "netflix:subscriberservice-common"]}, "org.springframework.boot:spring-boot": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot-autoconfigure", "org.springframework.boot:spring-boot-starter", "org.springframework.boot:spring-boot-test", "org.springframework.boot:spring-boot-test-autoconfigure"]}, "org.springframework.boot:spring-boot-autoconfigure": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot-starter", "org.springframework.boot:spring-boot-test-autoconfigure"]}, "org.springframework.boot:spring-boot-starter": {"locked": "2.7.18", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.springframework.boot:spring-boot-starter-test": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "org.springframework.boot:spring-boot-test": {"locked": "2.7.18", "transitive": ["org.springframework.boot:spring-boot-starter-test", "org.springframework.boot:spring-boot-test-autoconfigure"]}, "org.springframework.boot:spring-boot-test-autoconfigure": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix-test", "org.springframework.boot:spring-boot-starter-test"]}, "org.springframework.security:spring-security-core": {"locked": "5.8.13", "transitive": ["org.springframework.security:spring-security-test", "org.springframework.security:spring-security-web"]}, "org.springframework.security:spring-security-crypto": {"locked": "5.8.13", "transitive": ["org.springframework.security:spring-security-core"]}, "org.springframework.security:spring-security-test": {"locked": "5.8.13", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test", "com.netflix.spring:spring-boot-netflix-test"]}, "org.springframework.security:spring-security-web": {"locked": "5.8.13", "transitive": ["org.springframework.security:spring-security-test"]}, "org.springframework:spring-aop": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-context"]}, "org.springframework:spring-beans": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-aop", "org.springframework:spring-context", "org.springframework:spring-web"]}, "org.springframework:spring-context": {"locked": "5.3.37", "transitive": ["org.springframework.boot:spring-boot", "org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web"]}, "org.springframework:spring-core": {"locked": "5.3.37", "transitive": ["com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot", "org.springframework.boot:spring-boot-starter", "org.springframework.boot:spring-boot-starter-test", "org.springframework.security:spring-security-core", "org.springframework.security:spring-security-test", "org.springframework.security:spring-security-web", "org.springframework:spring-aop", "org.springframework:spring-beans", "org.springframework:spring-context", "org.springframework:spring-expression", "org.springframework:spring-test", "org.springframework:spring-web"]}, "org.springframework:spring-expression": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-context"]}, "org.springframework:spring-jcl": {"locked": "5.3.37", "transitive": ["org.springframework:spring-core"]}, "org.springframework:spring-test": {"locked": "5.3.37", "transitive": ["com.netflix.spring:spring-boot-netflix-test", "org.springframework.boot:spring-boot-starter-test", "org.springframework.security:spring-security-test"]}, "org.springframework:spring-web": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-web"]}, "org.xmlunit:xmlunit-core": {"locked": "2.9.1", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.yaml:snakeyaml": {"locked": "1.29", "transitive": ["org.springframework.boot:spring-boot-starter"]}}, "testRuntimeClasspath": {"antlr:antlr": {"locked": "2.7.7", "transitive": ["org.antlr:antlr-runtime", "org.antlr:stringtemplate"]}, "aopalliance:aopalliance": {"locked": "1.0", "transitive": ["com.google.inject:guice"]}, "ch.qos.reload4j:reload4j": {"locked": "1.2.25", "transitive": ["com.netflix.blitz4j:blitz4j", "netflix:platform-jdk-compat"]}, "com.amazonaws:aws-java-sdk-core": {"locked": "1.12.783", "transitive": ["netflix:platform-core"]}, "com.atomicjar:testcontainers-cloud-provider-strategy": {"locked": "0.0.41", "transitive": ["com.netflix.nebula.testcontainers:testcontainers-cloud-configurer"]}, "com.fasterxml.jackson.core:jackson-annotations": {"locked": "2.17.3", "transitive": ["com.fasterxml.jackson.core:jackson-databind", "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", "com.netflix.archaius:archaius-core"]}, "com.fasterxml.jackson.core:jackson-core": {"locked": "2.17.3", "transitive": ["com.fasterxml.jackson.core:jackson-databind", "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor", "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", "com.netflix.archaius:archaius-core"]}, "com.fasterxml.jackson.core:jackson-databind": {"locked": "2.17.3", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor", "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", "com.netflix.archaius:archaius-core", "com.netflix.ksclient:ksclient-api", "com.netflix.spring:spring-boot-netflix", "netflix:platform-core"]}, "com.fasterxml.jackson.dataformat:jackson-dataformat-cbor": {"locked": "2.17.3", "transitive": ["com.amazonaws:aws-java-sdk-core"]}, "com.fasterxml.jackson.datatype:jackson-datatype-jsr310": {"locked": "2.17.3", "transitive": ["com.netflix.spring:spring-boot-netflix"]}, "com.google.api.grpc:proto-google-common-protos": {"locked": "2.29.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "com.google.code.findbugs:annotations": {"locked": "3.0.1", "transitive": ["netflix:basicTypes", "netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge", "netflix:geoip-common"]}, "com.google.code.findbugs:jsr305": {"locked": "3.0.2", "transitive": ["com.google.code.findbugs:annotations", "com.netflix.archaius:archaius-core", "com.netflix.netflix-commons:netflix-infix", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:netflix-config"]}, "com.google.code.gson:gson": {"locked": "2.8.9", "transitive": ["com.netflix.netflix-commons:netflix-infix", "netflix.com.google.protobuf:protobuf-java-util-nflx"]}, "com.google.errorprone:error_prone_annotations": {"locked": "2.23.0", "transitive": ["netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-guava-shaded-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "com.google.guava:guava": {"locked": "20.0", "transitive": ["com.google.inject:guice", "com.netflix.archaius:archaius-core", "com.netflix.netflix-commons:netflix-infix", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "netflix:platform-core", "netflix:platform-logimpl", "netflix:platform-utils"]}, "com.google.inject.extensions:guice-multibindings": {"locked": "4.1.0", "transitive": ["netflix:atlas-client"]}, "com.google.inject:guice": {"locked": "4.1.0", "transitive": ["com.google.inject.extensions:guice-multibindings", "com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client"]}, "com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}, "com.ibm.icu:icu4j": {"locked": "73.2", "transitive": ["netflix:nfi18n-core"]}, "com.jayway.jsonpath:json-path": {"locked": "2.7.0", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "com.netflix.appregistry.admin:appregistry-rt-proto": {"locked": "0.582.0", "transitive": ["com.netflix.microcontext:microcontext-model", "com.netflix.passport.test:passport-test-core", "netflix:passport"]}, "com.netflix.archaius:archaius-core": {"locked": "0.7.12", "transitive": ["com.netflix.blitz4j:blitz4j", "com.netflix.netflix-commons:netflix-eventbus", "netflix:netflix-config", "netflix:platform-core"]}, "com.netflix.archaius:archaius2-api": {"locked": "2.8.3", "transitive": ["com.netflix.archaius:archaius2-core"]}, "com.netflix.archaius:archaius2-core": {"locked": "2.8.3", "transitive": ["com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client"]}, "com.netflix.blitz4j:blitz4j": {"locked": "1.42.0", "transitive": ["netflix:platform-core"]}, "com.netflix.dcms:dcms-runtime-proto": {"locked": "1.880.0", "transitive": ["com.netflix.microcontext:microcontext-model"]}, "com.netflix.frigga:frigga": {"locked": "0.26.0", "transitive": ["netflix:platform-core"]}, "com.netflix.governator:governator-api": {"locked": "1.17.13", "transitive": ["netflix:nfi18n-core"]}, "com.netflix.ksclient:ksclient-api": {"locked": "3.11.0", "transitive": ["netflix:platform-core", "netflix:platform-logimpl"]}, "com.netflix.microcontext:microcontext-model": {"project": true}, "com.netflix.nebula.testcontainers:testcontainers-cloud-configurer": {"locked": "1.16.0"}, "com.netflix.netflix-commons:netflix-eventbus": {"locked": "0.3.0", "transitive": ["netflix:nf-eventbus-core", "netflix:platform-core"]}, "com.netflix.netflix-commons:netflix-infix": {"locked": "0.3.0", "transitive": ["com.netflix.netflix-commons:netflix-eventbus"]}, "com.netflix.ngp:ngplibs-auth-proto-definition": {"locked": "3.771.0", "transitive": ["netflix:passport"]}, "com.netflix.passport.test:passport-test-core": {"locked": "0.41.0"}, "com.netflix.peas:auth-token-scopes": {"locked": "1.42.0", "transitive": ["netflix:passport"]}, "com.netflix.servo:servo-apache": {"locked": "0.13.2", "transitive": ["netflix:atlas-client"]}, "com.netflix.servo:servo-core": {"locked": "0.13.2", "transitive": ["com.netflix.blitz4j:blitz4j", "com.netflix.netflix-commons:netflix-eventbus", "com.netflix.servo:servo-apache", "com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client", "netflix:geoip-common", "netflix:netflix-config"]}, "com.netflix.spectator:spectator-api": {"locked": "1.8.5", "transitive": ["com.netflix.ksclient:ksclient-api", "com.netflix.passport.test:passport-test-core", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "com.netflix.spectator:spectator-ext-gc", "com.netflix.spectator:spectator-ext-ipc", "com.netflix.spectator:spectator-ext-jvm", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spectator:spectator-reg-atlas", "netflix:atlas-client", "netflix:platform-core", "netflix:server-context"]}, "com.netflix.spectator:spectator-ext-gc": {"locked": "1.8.5", "transitive": ["com.netflix.spectator:spectator-nflx-plugin"]}, "com.netflix.spectator:spectator-ext-ipc": {"locked": "1.8.5", "transitive": ["com.netflix.spectator:spectator-reg-atlas", "netflix:atlas-client"]}, "com.netflix.spectator:spectator-ext-jvm": {"locked": "1.8.5", "transitive": ["com.netflix.spectator:spectator-nflx-plugin", "netflix:atlas-client"]}, "com.netflix.spectator:spectator-nflx-plugin": {"locked": "1.8.5", "transitive": ["netflix:atlas-client"]}, "com.netflix.spectator:spectator-nflx-tagging": {"locked": "1.8.5", "transitive": ["com.netflix.spectator:spectator-nflx-plugin"]}, "com.netflix.spectator:spectator-reg-atlas": {"locked": "1.8.5", "transitive": ["com.netflix.spectator:spectator-nflx-plugin"]}, "com.netflix.spring.devagent:sbn-dev-agent-client": {"locked": "0.0.13", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "com.netflix.spring:spring-boot-netflix": {"locked": "2.7.226", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "com.netflix.spring:spring-boot-netflix-application-dependencies": {"locked": "2.7.226"}, "com.netflix.spring:spring-boot-netflix-starter-test": {"locked": "2.7.226"}, "com.netflix.spring:spring-boot-netflix-test": {"locked": "2.7.226", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "com.netflix.tracing:netflix-tracing-tags": {"locked": "0.660.0", "transitive": ["netflix:platform-core"]}, "com.sun.activation:jakarta.activation": {"locked": "1.2.2", "transitive": ["com.sun.xml.bind:jaxb-impl"]}, "com.sun.jersey:jersey-client": {"locked": "1.20.1", "transitive": ["netflix:platform-core", "netflix:platform-logimpl"]}, "com.sun.jersey:jersey-core": {"locked": "1.20.1", "transitive": ["com.sun.jersey:jersey-client", "netflix:platform-jdk-compat"]}, "com.sun.xml.bind:jaxb-impl": {"locked": "2.3.6", "transitive": ["netflix:platform-jdk-compat"]}, "com.thoughtworks.xstream:xstream": {"locked": "1.4.21", "transitive": ["netflix:netflix-config"]}, "com.typesafe:config": {"locked": "1.4.3", "transitive": ["com.netflix.spectator:spectator-ext-jvm", "netflix:atlas-client"]}, "commons-beanutils:commons-beanutils": {"locked": "1.9.4", "transitive": ["netflix:netflix-config"]}, "commons-codec:commons-codec": {"locked": "1.16.0", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.netflix.archaius:archaius2-core", "netflix:platform-core", "org.apache.httpcomponents:httpclient"]}, "commons-collections:commons-collections": {"locked": "3.2.2", "transitive": ["com.netflix.blitz4j:blitz4j", "commons-beanutils:commons-beanutils", "netflix:platform-core"]}, "commons-configuration:commons-configuration": {"locked": "1.10", "transitive": ["com.netflix.archaius:archaius-core", "com.netflix.blitz4j:blitz4j", "netflix:netflix-config", "netflix:nfi18n-core", "netflix:nflibrary-slim", "netflix:platform-core", "netflix:platform-logimpl"]}, "commons-io:commons-io": {"locked": "2.7", "transitive": ["netflix:nfi18n-core"]}, "commons-jxpath:commons-jxpath": {"locked": "1.3", "transitive": ["com.netflix.netflix-commons:netflix-infix"]}, "commons-lang:commons-lang": {"locked": "2.6", "transitive": ["commons-configuration:commons-configuration", "netflix:geoip-common", "netflix:platform-core", "netflix:platform-utils"]}, "io.github.x-stream:mxparser": {"locked": "1.2.2", "transitive": ["com.thoughtworks.xstream:xstream"]}, "io.zipkin.brave:brave": {"locked": "5.13.3", "transitive": ["netflix:platform-core"]}, "io.zipkin.reporter2:zipkin-reporter": {"locked": "2.16.3", "transitive": ["io.zipkin.reporter2:zipkin-reporter-brave"]}, "io.zipkin.reporter2:zipkin-reporter-brave": {"locked": "2.16.3", "transitive": ["io.zipkin.brave:brave"]}, "io.zipkin.zipkin2:zipkin": {"locked": "2.23.2", "transitive": ["io.zipkin.reporter2:zipkin-reporter"]}, "jakarta.activation:jakarta.activation-api": {"locked": "1.2.2", "transitive": ["jakarta.xml.bind:jakarta.xml.bind-api"]}, "jakarta.annotation:jakarta.annotation-api": {"locked": "1.3.5", "transitive": ["org.springframework.boot:spring-boot-starter"]}, "jakarta.xml.bind:jakarta.xml.bind-api": {"locked": "2.3.3", "transitive": ["com.sun.xml.bind:jaxb-impl", "org.springframework.boot:spring-boot-starter-test"]}, "javax.activation:javax.activation-api": {"locked": "1.2.0", "transitive": ["javax.xml.bind:jaxb-api"]}, "javax.inject:javax.inject": {"locked": "1", "transitive": ["com.google.inject:guice", "com.netflix.archaius:archaius2-api", "com.netflix.governator:governator-api", "com.netflix.ksclient:ksclient-api", "com.netflix.passport.test:passport-test-core", "com.netflix.spectator:spectator-nflx-plugin", "netflix:nf-eventbus-core"]}, "javax.servlet:javax.servlet-api": {"locked": "3.1.0", "transitive": ["com.netflix.netflix-commons:netflix-infix", "netflix:server-context"]}, "javax.ws.rs:jsr311-api": {"locked": "1.1.1", "transitive": ["com.sun.jersey:jersey-core", "netflix:platform-core"]}, "javax.xml.bind:jaxb-api": {"locked": "2.5.0", "transitive": ["netflix:platform-jdk-compat"]}, "joda-time:joda-time": {"locked": "2.12.7", "transitive": ["com.amazonaws:aws-java-sdk-core", "com.netflix.netflix-commons:netflix-infix", "netflix:platform-utils"]}, "junit:junit": {"locked": "4.13.2", "transitive": ["org.junit.vintage:junit-vintage-engine"]}, "net.bytebuddy:byte-buddy": {"locked": "1.12.9", "transitive": ["org.mockito:mockito-core"]}, "net.bytebuddy:byte-buddy-agent": {"locked": "1.12.9", "transitive": ["org.mockito:mockito-core"]}, "net.jcip:jcip-annotations": {"locked": "1.0", "transitive": ["com.google.code.findbugs:annotations"]}, "net.minidev:accessors-smart": {"locked": "2.4.7", "transitive": ["net.minidev:json-smart"]}, "net.minidev:json-smart": {"locked": "2.4.7", "transitive": ["com.jayway.jsonpath:json-path"]}, "netflix.com.google.protobuf:protobuf-java-nflx": {"locked": "3.25.5", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.com.google.protobuf:protobuf-java-util-nflx", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix.io.grpc:grpc-protobuf-nflx", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.com.google.protobuf:protobuf-java-util-nflx": {"locked": "3.25.5", "transitive": ["netflix.grpc:netflix-grpc-options-proto-definition"]}, "netflix.grpc:netflix-grpc-options-proto-definition": {"locked": "1.63.20", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix:basicTypes-proto", "netflix:basicTypes-proto-bridge", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-api-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-guava-shaded-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-api-nflx", "netflix.io.grpc:grpc-protobuf-lite-nflx", "netflix.io.grpc:grpc-protobuf-nflx", "netflix.io.grpc:grpc-stub-nflx"]}, "netflix.io.grpc:grpc-protobuf-lite-nflx": {"locked": "1.63.0", "transitive": ["netflix.io.grpc:grpc-protobuf-nflx"]}, "netflix.io.grpc:grpc-protobuf-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix.io.grpc:grpc-stub-nflx": {"locked": "1.63.0", "transitive": ["com.netflix.appregistry.admin:appregistry-rt-proto", "com.netflix.dcms:dcms-runtime-proto", "com.netflix.microcontext:microcontext-model", "com.netflix.ngp:ngplibs-auth-proto-definition", "com.netflix.peas:auth-token-scopes", "netflix.grpc:netflix-grpc-options-proto-definition", "netflix:basicTypes-proto", "netflix:subscriberservice-common-types-proto-definition", "netflix:subscriberservice-proto-definition"]}, "netflix:atlas-client": {"locked": "2.29.10", "transitive": ["netflix:platform-utils"]}, "netflix:basicTypes": {"locked": "1.86.0", "transitive": ["netflix:basicTypes-proto-bridge", "netflix:geoip-common", "netflix:nfi18n-core", "netflix:server-context"]}, "netflix:basicTypes-proto": {"locked": "1.86.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:basicTypes-proto-bridge", "netflix:passport"]}, "netflix:basicTypes-proto-bridge": {"locked": "1.86.0"}, "netflix:geoip-common": {"locked": "2.324"}, "netflix:i18n-dictionaries": {"locked": "0.205.0", "transitive": ["netflix:nfi18n-core"]}, "netflix:moduleRegistry": {"locked": "1.50.0", "transitive": ["netflix:nflibrary-slim"]}, "netflix:netflix-config": {"locked": "4.7.633", "transitive": ["netflix:geoip-common", "netflix:nflibrary-slim", "netflix:platform-core", "netflix:platform-logimpl", "netflix:platform-utils"]}, "netflix:nf-eventbus-core": {"locked": "1.32.0", "transitive": ["netflix:platform-core", "netflix:platform-logimpl"]}, "netflix:nfi18n-core": {"locked": "2.248"}, "netflix:nflibrary-slim": {"locked": "4.7.633", "transitive": ["netflix:nfi18n-core"]}, "netflix:ngl": {"locked": "0.49.0", "transitive": ["netflix:nfi18n-core"]}, "netflix:passport": {"locked": "4.6.0", "transitive": ["com.netflix.passport.test:passport-test-core", "netflix:subscriberservice-proto-definition"]}, "netflix:platform-core": {"locked": "4.7.633", "transitive": ["netflix:platform-logimpl", "netflix:platform-utils"]}, "netflix:platform-jdk-compat": {"locked": "4.7.633", "transitive": ["netflix:platform-core"]}, "netflix:platform-logimpl": {"locked": "4.7.633", "transitive": ["netflix:platform-utils"]}, "netflix:platform-utils": {"locked": "4.7.633", "transitive": ["netflix:nfi18n-core"]}, "netflix:server-context": {"locked": "4.7.633", "transitive": ["com.netflix.passport.test:passport-test-core", "netflix:geoip-common", "netflix:netflix-config", "netflix:platform-core", "netflix:platform-utils"]}, "netflix:statistics": {"locked": "1.7.0", "transitive": ["netflix:platform-utils"]}, "netflix:subscriberservice-common": {"locked": "5.66.0"}, "netflix:subscriberservice-common-types-proto-definition": {"locked": "5.66.0", "transitive": ["netflix:passport", "netflix:subscriberservice-proto-definition"]}, "netflix:subscriberservice-proto-definition": {"locked": "5.66.0", "transitive": ["com.netflix.microcontext:microcontext-model", "netflix:subscriberservice-common"]}, "org.antlr:antlr-runtime": {"locked": "3.4", "transitive": ["com.netflix.netflix-commons:netflix-infix"]}, "org.antlr:stringtemplate": {"locked": "3.2.1", "transitive": ["org.antlr:antlr-runtime"]}, "org.apache.commons:commons-lang3": {"locked": "3.3.2", "transitive": ["com.netflix.archaius:archaius2-core"]}, "org.apache.commons:commons-math": {"locked": "2.2", "transitive": ["com.netflix.netflix-commons:netflix-eventbus", "netflix:platform-utils"]}, "org.apache.httpcomponents:httpclient": {"locked": "4.5.13", "transitive": ["com.amazonaws:aws-java-sdk-core"]}, "org.apache.httpcomponents:httpcore": {"locked": "4.4.13", "transitive": ["org.apache.httpcomponents:httpclient"]}, "org.apiguardian:apiguardian-api": {"locked": "1.1.2", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.jupiter:junit-jupiter-engine", "org.junit.jupiter:junit-jupiter-params", "org.junit.platform:junit-platform-commons", "org.junit.platform:junit-platform-engine", "org.junit.platform:junit-platform-launcher", "org.junit.vintage:junit-vintage-engine"]}, "org.aspectj:aspectjweaver": {"locked": "1.9.5", "transitive": ["netflix:platform-core"]}, "org.assertj:assertj-core": {"locked": "3.22.0", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.checkerframework:checker-qual": {"locked": "3.5.0", "transitive": ["netflix.io.grpc:grpc-guava-shaded-nflx"]}, "org.codehaus.jackson:jackson-core-asl": {"locked": "1.10.3", "transitive": ["netflix:platform-jdk-compat", "org.codehaus.jackson:jackson-mapper-asl"]}, "org.codehaus.jackson:jackson-mapper-asl": {"locked": "1.10.3", "transitive": ["netflix:netflix-config"]}, "org.hamcrest:hamcrest": {"locked": "2.2", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.junit.jupiter:junit-jupiter": {"locked": "5.12.2", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.junit.jupiter:junit-jupiter-api": {"locked": "5.12.2", "transitive": ["org.junit.jupiter:junit-jupiter", "org.junit.jupiter:junit-jupiter-engine", "org.junit.jupiter:junit-jupiter-params", "org.mockito:mockito-junit-jupiter"]}, "org.junit.jupiter:junit-jupiter-engine": {"locked": "5.12.2", "transitive": ["org.junit.jupiter:junit-jupiter"]}, "org.junit.jupiter:junit-jupiter-params": {"locked": "5.12.2", "transitive": ["org.junit.jupiter:junit-jupiter"]}, "org.junit.platform:junit-platform-commons": {"locked": "1.12.2", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.platform:junit-platform-engine"]}, "org.junit.platform:junit-platform-engine": {"locked": "1.12.2", "transitive": ["org.junit.jupiter:junit-jupiter-engine", "org.junit.platform:junit-platform-launcher", "org.junit.vintage:junit-vintage-engine"]}, "org.junit.platform:junit-platform-launcher": {"locked": "1.12.2"}, "org.junit.vintage:junit-vintage-engine": {"locked": "5.12.2", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "org.mockito:mockito-core": {"locked": "4.5.1", "transitive": ["org.mockito:mockito-junit-jupiter", "org.springframework.boot:spring-boot-starter-test"]}, "org.mockito:mockito-junit-jupiter": {"locked": "4.5.1", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.objenesis:objenesis": {"locked": "3.2", "transitive": ["org.mockito:mockito-core"]}, "org.opentest4j:opentest4j": {"locked": "1.3.0", "transitive": ["org.junit.jupiter:junit-jupiter-api", "org.junit.platform:junit-platform-engine"]}, "org.ow2.asm:asm": {"locked": "9.1", "transitive": ["net.minidev:accessors-smart"]}, "org.skyscreamer:jsonassert": {"locked": "1.5.1", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.slf4j:slf4j-api": {"locked": "1.7.36", "transitive": ["com.jayway.jsonpath:json-path", "com.netflix.archaius:archaius-core", "com.netflix.archaius:archaius2-api", "com.netflix.archaius:archaius2-core", "com.netflix.blitz4j:blitz4j", "com.netflix.netflix-commons:netflix-eventbus", "com.netflix.netflix-commons:netflix-infix", "com.netflix.servo:servo-apache", "com.netflix.servo:servo-core", "com.netflix.spectator:spectator-api", "com.netflix.spectator:spectator-ext-gc", "com.netflix.spectator:spectator-ext-ipc", "com.netflix.spectator:spectator-ext-jvm", "com.netflix.spectator:spectator-nflx-plugin", "com.netflix.spectator:spectator-nflx-tagging", "com.netflix.spectator:spectator-reg-atlas", "com.netflix.spring:spring-boot-netflix", "netflix:atlas-client", "netflix:geoip-common", "netflix:nf-eventbus-core", "netflix:nflibrary-slim", "netflix:server-context", "netflix:subscriberservice-common"]}, "org.springframework.boot:spring-boot": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot-autoconfigure", "org.springframework.boot:spring-boot-starter", "org.springframework.boot:spring-boot-test", "org.springframework.boot:spring-boot-test-autoconfigure"]}, "org.springframework.boot:spring-boot-autoconfigure": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot-starter", "org.springframework.boot:spring-boot-test-autoconfigure"]}, "org.springframework.boot:spring-boot-starter": {"locked": "2.7.18", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.springframework.boot:spring-boot-starter-test": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test"]}, "org.springframework.boot:spring-boot-test": {"locked": "2.7.18", "transitive": ["org.springframework.boot:spring-boot-starter-test", "org.springframework.boot:spring-boot-test-autoconfigure"]}, "org.springframework.boot:spring-boot-test-autoconfigure": {"locked": "2.7.18", "transitive": ["com.netflix.spring:spring-boot-netflix-test", "org.springframework.boot:spring-boot-starter-test"]}, "org.springframework.security:spring-security-core": {"locked": "5.8.13", "transitive": ["org.springframework.security:spring-security-test", "org.springframework.security:spring-security-web"]}, "org.springframework.security:spring-security-crypto": {"locked": "5.8.13", "transitive": ["org.springframework.security:spring-security-core"]}, "org.springframework.security:spring-security-test": {"locked": "5.8.13", "transitive": ["com.netflix.spring:spring-boot-netflix-starter-test", "com.netflix.spring:spring-boot-netflix-test"]}, "org.springframework.security:spring-security-web": {"locked": "5.8.13", "transitive": ["org.springframework.security:spring-security-test"]}, "org.springframework:spring-aop": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-context"]}, "org.springframework:spring-beans": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-aop", "org.springframework:spring-context", "org.springframework:spring-web"]}, "org.springframework:spring-context": {"locked": "5.3.37", "transitive": ["org.springframework.boot:spring-boot", "org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web"]}, "org.springframework:spring-core": {"locked": "5.3.37", "transitive": ["com.netflix.spring:spring-boot-netflix", "org.springframework.boot:spring-boot", "org.springframework.boot:spring-boot-starter", "org.springframework.boot:spring-boot-starter-test", "org.springframework.security:spring-security-core", "org.springframework.security:spring-security-test", "org.springframework.security:spring-security-web", "org.springframework:spring-aop", "org.springframework:spring-beans", "org.springframework:spring-context", "org.springframework:spring-expression", "org.springframework:spring-test", "org.springframework:spring-web"]}, "org.springframework:spring-expression": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-core", "org.springframework.security:spring-security-web", "org.springframework:spring-context"]}, "org.springframework:spring-jcl": {"locked": "5.3.37", "transitive": ["com.amazonaws:aws-java-sdk-core", "commons-beanutils:commons-beanutils", "commons-configuration:commons-configuration", "netflix:platform-core", "org.apache.httpcomponents:httpclient", "org.springframework:spring-core"]}, "org.springframework:spring-test": {"locked": "5.3.37", "transitive": ["com.netflix.spring:spring-boot-netflix-test", "org.springframework.boot:spring-boot-starter-test", "org.springframework.security:spring-security-test"]}, "org.springframework:spring-web": {"locked": "5.3.37", "transitive": ["org.springframework.security:spring-security-web"]}, "org.xmlunit:xmlunit-core": {"locked": "2.9.1", "transitive": ["org.springframework.boot:spring-boot-starter-test"]}, "org.yaml:snakeyaml": {"locked": "1.29", "transitive": ["org.springframework.boot:spring-boot-starter"]}, "tools.profiler:async-profiler": {"locked": "2.9", "transitive": ["com.netflix.spring:spring-boot-netflix"]}, "xmlpull:xmlpull": {"locked": "*******", "transitive": ["io.github.x-stream:mxparser"]}}}