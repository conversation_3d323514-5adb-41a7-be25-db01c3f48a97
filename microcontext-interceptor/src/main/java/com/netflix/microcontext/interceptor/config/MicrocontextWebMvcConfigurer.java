package com.netflix.microcontext.interceptor.config;

import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.microcontext.interceptor.caller.MicrocontextCallerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

public class MicrocontextWebMvcConfigurer implements WebMvcConfigurer {

  private final MicrocontextCallerInterceptor microcontextCallerInterceptor;
  private final Property<String> interceptorPath;

  public MicrocontextWebMvcConfigurer(
      MicrocontextCallerInterceptor microcontextCallerInterceptor,
      PropertyRepository propertyRepository) {
    this.microcontextCallerInterceptor = microcontextCallerInterceptor;
    interceptorPath =
        propertyRepository
            .get("com.netflix.microcontext.caller.interceptor.path", String.class)
            .orElse("/**");
  }

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry
        .addInterceptor(this.microcontextCallerInterceptor)
        .addPathPatterns(interceptorPath.get());
  }
}
