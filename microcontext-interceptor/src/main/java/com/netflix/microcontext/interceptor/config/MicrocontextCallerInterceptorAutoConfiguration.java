package com.netflix.microcontext.interceptor.config;

import com.netflix.archaius.api.PropertyRepository;
import com.netflix.microcontext.interceptor.caller.MicrocontextCallerInterceptor;
import com.netflix.microcontext.protogen.MicrocontextServiceGrpc.MicrocontextServiceBlockingStub;
import com.netflix.spectator.api.Registry;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
@ConditionalOnProperty(
    name = "microcontext.caller.interceptor.enabled",
    havingValue = "true",
    matchIfMissing = true)
public class MicrocontextCallerInterceptorAutoConfiguration {

  @Bean
  MicrocontextCallerInterceptor microcontext_interceptorMicrocontextCallerInterceptor(
      @GrpcSpringClient("microcontext") MicrocontextServiceBlockingStub microcontextService,
      PropertyRepository propertyRepository,
      Registry registry) {
    return new MicrocontextCallerInterceptor(microcontextService, propertyRepository, registry);
  }

  @Bean
  MicrocontextWebMvcConfigurer microcontext_interceptorMicrocontextWebMvcConfigurer(
      MicrocontextCallerInterceptor microcontextCallerInterceptor,
      PropertyRepository propertyRepository) {
    return new MicrocontextWebMvcConfigurer(microcontextCallerInterceptor, propertyRepository);
  }
}
