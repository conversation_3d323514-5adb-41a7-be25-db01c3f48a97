package com.netflix.microcontext.interceptor.caller;

import static com.netflix.microcontext.init.utils.Params.stringList;
import static com.netflix.passport.introspect.PassportIdentityFactory.REQUEST_CONTEXT_PASSPORT_CONTEXT_NAME;

import com.netflix.archaius.api.Property;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.grpc.shaded.com.google.common.annotations.VisibleForTesting;
import com.netflix.microcontext.MicrocontextInitializer;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.MicrocontextInternalUtils;
import com.netflix.microcontext.access.server.base.ServerUtils;
import com.netflix.microcontext.access.server.metrics.MicrocontextInitMetrics;
import com.netflix.microcontext.access.server.resolvers.RequestHeaderResolver;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.resolvers.GeoResolvers;
import com.netflix.microcontext.init.utils.Passports;
import com.netflix.microcontext.interceptor.util.StringUtil;
import com.netflix.microcontext.protogen.GetContextRequest;
import com.netflix.microcontext.protogen.GetContextResponse;
import com.netflix.microcontext.protogen.MicrocontextServiceGrpc.MicrocontextServiceBlockingStub;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.Registry;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import netflix.context.Context;
import netflix.context.common.StringList;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

/**
 * When configured, this intercepts server calls and checks for a Microcontext in the request
 * context. If Microcontext is absent, it makes a blocking call to the microcontext-server to fetch
 * the microcontext and sets it in the current request context
 *
 * <p>If you wish to use this interceptor, register this in your web config, e.g.: <a
 * href="https://stash.corp.netflix.com/projects/dna/repos/gusto/browse/src/main/java/com/netflix/gusto/config/GustoWebConfig.java#11">config</a>
 *
 * <p>You can also augment the request with default values for client capabilities by adding an
 * interceptor prior to invoking this. See
 * com.netflix.microcontext.interceptor.caller.MicrocontextHeaderAugmentingInterceptor for an
 * example
 */
public class MicrocontextCallerInterceptor implements AsyncHandlerInterceptor {

  private final MicrocontextServiceBlockingStub microcontextService;
  private final Property<Boolean> enableMetrics;
  private final Property<Boolean> favorHeaderOverAttr;
  private final Property<Boolean> callRemote;
  private final Property<Boolean> fixPassport;
  private final Counter headerCounter;
  private final Counter requestContextCounter;
  private final Counter serverCounter;
  private final Counter defaultCounter;
  private final Counter passportHeaderMismatch;
  private final MicrocontextInitMetrics microcontextMetrics;

  private static final Logger log = LoggerFactory.getLogger(MicrocontextCallerInterceptor.class);
  private static final String GATEWAY_PROVIDED_XFF_HEADER_KEY = "x-forwarded-for-gql";
  public static final String HEALTH_CHECK_QUERY_HEADER_KEY = "X-Netflix.GQLQueryHealthCheck";

  /** The following config values should be set by the consumer of this module */
  public static class Properties {
    public static final String ENABLE_MICROCONTEXT_METRICS_PROPERTY =
        "com.netflix.microcontext.caller.interceptor.metrics.enabled";
    public static final String FAVOR_HEADERS_OVER_REQ_ATTR =
        "com.netflix.microcontext.caller.interceptor.headers.favored";
    public static final String CALL_MICROCONTEXT_REMOTE =
        "com.netflix.microcontext.caller.interceptor.call.remote";
    public static final String FIX_PASSPORT =
        "com.netflix.microcontext.caller.interceptor.fix.passport";
  }

  public MicrocontextCallerInterceptor(
      @GrpcSpringClient("microcontext") MicrocontextServiceBlockingStub microcontextService,
      PropertyRepository propertyRepository,
      Registry registry) {
    this.microcontextService = microcontextService;
    this.enableMetrics =
        propertyRepository
            .get(Properties.ENABLE_MICROCONTEXT_METRICS_PROPERTY, Boolean.class)
            .orElse(true);
    this.callRemote =
        propertyRepository.get(Properties.CALL_MICROCONTEXT_REMOTE, Boolean.class).orElse(false);
    this.fixPassport = propertyRepository.get(Properties.FIX_PASSPORT, Boolean.class).orElse(true);
    this.favorHeaderOverAttr =
        propertyRepository.get(Properties.FAVOR_HEADERS_OVER_REQ_ATTR, Boolean.class).orElse(true);
    headerCounter = registry.counter("microcontext.caller.interceptor.call", "source", "header");
    requestContextCounter =
        registry.counter("microcontext.caller.interceptor.call", "source", "requestContext");
    serverCounter = registry.counter("microcontext.caller.interceptor.call", "source", "server");
    defaultCounter = registry.counter("microcontext.caller.interceptor.call", "source", "default");
    passportHeaderMismatch =
        registry.counter("microcontext.caller.interceptor.passport", "result", "mismatch");
    microcontextMetrics = new MicrocontextInitMetrics(registry);
  }

  @Override
  public boolean preHandle(
      HttpServletRequest request, @Nonnull HttpServletResponse response, @Nonnull Object handler) {
    if (request.getHeader(HEALTH_CHECK_QUERY_HEADER_KEY) != null) {
      // Do not resolve microcontext for health check queries
      return true;
    }

    // Will fix passport if header and request context mismatch
    String passport = handlePassport(request);

    // if it's already set, don't call microcontext
    final Optional<Context> optionalContext = getContext(request);
    if (optionalContext.isPresent()) {
      final Context context = optionalContext.get();
      // if the header contains geo and rc does not, set geodata in rc
      if (!GeoResolvers.hasGeo() && context.hasGeo()) {
        GeoResolvers.setGeo(context.getGeo());
      }
      MicrocontextInternalUtils.internalSet(context);
    } else if (callRemote.get()) {
      GetContextRequest contextRequest =
          createContextRequest(request, favorHeaderOverAttr, passport);
      try {
        GetContextResponse contextResponse = microcontextService.getContext(contextRequest);
        log.debug("contextResponse found and set: {}", contextResponse.getContext());
        serverCounter.increment();
        MicrocontextInternalUtils.internalSet(contextResponse.getContext());
      } catch (Exception ex) {
        log.warn("microcontext error", ex);
      }
    } else {
      defaultCounter.increment();
      // FIXME add query param resolver
      MicrocontextInternalUtils.internalSet(
          MicrocontextInitializer.init(
              RequestHeaderResolver.of(request), ParamResolver.EMPTY, CurrentRequestContext.get()));
    }
    if (enableMetrics.get()) {
      microcontextMetrics.emit(optionalContext.orElse(null));
    }
    return true;
  }

  private Optional<Context> getContext(HttpServletRequest request) {
    Optional<Context> header = ServerUtils.fromHeader(request);
    // get the value from header, request context or default
    if (header.isPresent()) {
      headerCounter.increment();
      return header;
    } else {
      Optional<Context> requestContext = CurrentMicrocontext.requestContext();
      if (requestContext.isPresent()) {
        requestContextCounter.increment();
        return requestContext;
      } else {
        // emit the metric from the calling method if server not resolved
        return Optional.empty();
      }
    }
  }

  @VisibleForTesting
  public static GetContextRequest createContextRequest(
      HttpServletRequest request, Property<Boolean> favorHeaderOverAttr, String passport) {
    String xffGqLHeader = request.getHeader(GATEWAY_PROVIDED_XFF_HEADER_KEY);

    GetContextRequest.Builder contextReqBldr = GetContextRequest.newBuilder().setPassport(passport);
    if (StringUtils.isNotEmpty(xffGqLHeader)) {
      contextReqBldr.setIpAddress(xffGqLHeader);
    }

    Map<String, StringList> headerMap = headerMap(request);
    Map<String, StringList> requestAttrParams = paramsFromRequestAttributes(request);
    Map<String, StringList> allParams = new HashMap<>();
    if (favorHeaderOverAttr.get()) {
      allParams.putAll(requestAttrParams);
      allParams.putAll(headerMap);
    } else {
      allParams.putAll(headerMap);
      allParams.putAll(requestAttrParams);
    }
    return contextReqBldr.putAllHeaders(allParams).build();
  }

  private static Map<String, StringList> paramsFromRequestAttributes(HttpServletRequest request) {
    Map<String, StringList> requestAttributes = new HashMap<>();
    Enumeration<String> attributeNames = request.getAttributeNames();
    while (attributeNames.hasMoreElements()) {
      String attributeName = attributeNames.nextElement();
      // allowlist of attributes
      if (attributeName.startsWith("x-netflix") && request.getAttribute(attributeName) != null) {
        try {
          List<String> attrVal = StringUtil.getCsv((String) request.getAttribute(attributeName));
          requestAttributes.put(
              attributeName, StringList.newBuilder().addAllValues(attrVal).build());
        } catch (Exception e) {
          log.warn(
              "could not parse request attribute value: {} for request attribute key: {}",
              attributeName,
              request.getAttribute(attributeName));
        }
      }
    }
    return requestAttributes;
  }

  public static Map<String, StringList> headerMap(HttpServletRequest request) {
    Map<String, StringList> map = new HashMap<>();
    Enumeration<String> headerNames = request.getHeaderNames();
    while (headerNames.hasMoreElements()) {
      String headerName = headerNames.nextElement().toLowerCase();
      // exclude request context
      if (!headerName.startsWith("x-netflix.request.sub.context")
          && !headerName.startsWith("x-netflix.request.context"))
        map.put(headerName, stringList(request.getHeaders(headerName)));
    }
    return map;
  }

  String handlePassport(HttpServletRequest req) {
    return handlePassport(req, passportHeaderMismatch, fixPassport);
  }

  /**
   * Gets the passport from header or request context and handles cases in which they do not match
   */
  static String handlePassport(
      HttpServletRequest req, Counter passportHeaderMismatch, Property<Boolean> fixPassport) {
    HeaderResolver resolver = RequestHeaderResolver.of(req);
    final Optional<String> headerPassport = Passports.passport(resolver);
    final RequestContext currentRequestContext = CurrentRequestContext.get();
    final Optional<String> requestContextPassport = Passports.passport(currentRequestContext);
    if (!headerPassport.isPresent()) {
      return requestContextPassport.orElse(null);
    }
    final String headerPassportString = headerPassport.get();
    if (!Objects.equals(headerPassport, requestContextPassport)) {
      passportHeaderMismatch.increment();
      if (log.isDebugEnabled()) {
        log.debug(
            "Passport mismatch headerPassport {} context {}",
            headerPassport,
            requestContextPassport);
      }
      if (fixPassport.get()) {
        currentRequestContext.addContext(
            REQUEST_CONTEXT_PASSPORT_CONTEXT_NAME, headerPassportString);
      }
    }
    return headerPassportString;
  }
}
