package com.netflix.microcontext.interceptor.caller;

import com.netflix.microcontext.init.headers.Headers;
import javax.annotation.Nonnull;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

/**
 * This is an EXAMPLE of how to set attributes on the request to provide default capabilities and
 * IXMS (interactive) experiences
 *
 * <p>C/P this interceptor and register it in your repo similar to: <a
 * href="https://stash.corp.netflix.com/projects/dna/repos/gusto/browse/src/main/java/com/netflix/gusto/config/GustoWebConfig.java#11">GustoWebConfig</a>
 */
public class MicrocontextHeaderAugmentingInterceptor implements AsyncHandlerInterceptor {

  private final Environment env;
  private static final Logger log =
      LoggerFactory.getLogger(MicrocontextHeaderAugmentingInterceptor.class);

  public static class Properties {
    public static final String ADD_CAPABILITY_HEADERS_PROPERTY =
        "com.netflix.microcontext.capability.headers.enabled";
    public static final String LOCALIZATION_FEATURES_PROPERTY =
        "com.netflix.microcontext.localization.features";
    public static final String TITLE_CAPABILITIES_PROPERTY =
        "com.netflix.microcontext.title.capabilities";
    public static final String FEATURE_CAPABILITIES_PROPERTY =
        "com.netflix.microcontext.feature.capabilities";
  }

  public static class Defaults {

    /** If the above config is not set, these defaults below will be provided */
    public static final String DEFAULT_LOCALIZATION_FEATURES = "defaultKidsProfile";

    public static final String DEFAULT_TITLE_CAPABILITIES =
        "episodeOrdering,seasonOrdering,"
            + "hiddenEpisodeNumbers,episodicNewBadge,episodicAvailabilityMessage,episodeSkipping";
    // From Rohan/Ed:
    // we only needed two separate flags when the kids top10 badge had a different color.
    // now we can send both capabilities across for all profiles, just like ios does.
    public static final String DEFAULT_FEATURE_CAPABILITIES = "supportsTop10,supportsTop10Kids";
  }

  public MicrocontextHeaderAugmentingInterceptor(Environment env) {
    this.env = env;
  }

  @Override
  public boolean preHandle(
      @Nonnull HttpServletRequest request,
      @Nonnull HttpServletResponse response,
      @Nonnull Object handler) {
    try {
      if (env.getProperty(Properties.ADD_CAPABILITY_HEADERS_PROPERTY, Boolean.class, true)) {
        addCapabilitiesAttributes(request);
      }
    } catch (Exception e) {
      log.error("error in MicrocontextHeaderAugmentingInterceptor setting request attributes", e);
    }
    return true;
  }

  private void addCapabilitiesAttributes(HttpServletRequest request) {
    String localizationFeatures =
        env.getProperty(
            Properties.LOCALIZATION_FEATURES_PROPERTY,
            String.class,
            Defaults.DEFAULT_LOCALIZATION_FEATURES);
    String titleCapabilities =
        env.getProperty(
            Properties.TITLE_CAPABILITIES_PROPERTY,
            String.class,
            Defaults.DEFAULT_TITLE_CAPABILITIES);
    String featureCapabilities =
        env.getProperty(
            Properties.FEATURE_CAPABILITIES_PROPERTY,
            String.class,
            Defaults.DEFAULT_FEATURE_CAPABILITIES);
    request.setAttribute(Headers.LOCALIZATION_FEATURES, localizationFeatures);
    request.setAttribute(Headers.TITLE_CAPABILITIES, titleCapabilities);
    request.setAttribute(Headers.FEATURE_CAPABILITIES, featureCapabilities);
  }
}
