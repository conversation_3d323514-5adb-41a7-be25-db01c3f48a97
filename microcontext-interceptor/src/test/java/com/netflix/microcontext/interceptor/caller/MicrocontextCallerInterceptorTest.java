package com.netflix.microcontext.interceptor.caller;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.netflix.archaius.api.Property;
import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.init.headers.Headers;
import com.netflix.microcontext.init.resolvers.AuthResolvers;
import com.netflix.microcontext.init.utils.Passports;
import com.netflix.microcontext.interceptor.caller.MicrocontextHeaderAugmentingInterceptor.Defaults;
import com.netflix.microcontext.protogen.GetContextRequest;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Counter;
import com.netflix.spectator.api.DefaultRegistry;
import com.netflix.spectator.api.Registry;
import java.util.Collections;
import java.util.Optional;
import java.util.StringJoiner;
import javax.servlet.http.HttpServletRequest;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class MicrocontextCallerInterceptorTest {

  private final Property<Boolean> property = mock(Property.class);
  private final Registry registry = new DefaultRegistry();
  private final Counter counter = registry.counter("foocounter");

  private static final String MOCK_PASSPORT = "mock-passport";

  @Before
  public void setup() {
    BindingContexts.push();
  }

  @After
  public void after() {
    BindingContexts.pop();
  }

  @Test
  public void shouldFavorHeadersWhenFavorHeadersConfigIsTrue() {

    when(property.get()).thenReturn(true);

    HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
    setupRequestHeaders(request);
    when(request.getAttribute(Headers.LOCALIZATION_FEATURES)).thenReturn("foo1");
    when(request.getAttribute(Headers.FEATURE_CAPABILITIES)).thenReturn("foo2");
    when(request.getAttribute(Headers.TITLE_CAPABILITIES)).thenReturn("foo3");

    GetContextRequest contextRequest =
        MicrocontextCallerInterceptor.createContextRequest(
            request,
            property,
            MicrocontextCallerInterceptor.handlePassport(request, counter, property));
    String l10nStr =
        String.join(
            ",", contextRequest.getHeadersMap().get(Headers.LOCALIZATION_FEATURES).getValuesList());
    StringJoiner joiner = new StringJoiner(",");
    for (String s :
        contextRequest.getHeadersMap().get(Headers.FEATURE_CAPABILITIES).getValuesList()) {
      joiner.add(s);
    }
    String featureCapabilities = joiner.toString();
    String titleCapabilities =
        String.join(
            ",", contextRequest.getHeadersMap().get(Headers.TITLE_CAPABILITIES).getValuesList());

    Assert.assertEquals(Defaults.DEFAULT_LOCALIZATION_FEATURES, l10nStr);
    Assert.assertEquals(Defaults.DEFAULT_FEATURE_CAPABILITIES, featureCapabilities);
    Assert.assertEquals(Defaults.DEFAULT_TITLE_CAPABILITIES, titleCapabilities);
  }

  @Test
  public void shouldFavorReqAttrWhenFavorHeadersConfigIsFalse() {

    when(property.get()).thenReturn(false);

    HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
    setupRequestHeaders(request);
    when(request.getAttribute(Headers.LOCALIZATION_FEATURES)).thenReturn("foo1");
    when(request.getAttribute(Headers.FEATURE_CAPABILITIES)).thenReturn("foo2");
    when(request.getAttribute(Headers.TITLE_CAPABILITIES)).thenReturn("foo3");

    GetContextRequest contextRequest =
        MicrocontextCallerInterceptor.createContextRequest(
            request,
            property,
            MicrocontextCallerInterceptor.handlePassport(request, counter, property));
    String l10nStr =
        String.join(
            ",", contextRequest.getHeadersMap().get(Headers.LOCALIZATION_FEATURES).getValuesList());
    String featureCapabilities =
        String.join(
            ",", contextRequest.getHeadersMap().get(Headers.FEATURE_CAPABILITIES).getValuesList());
    String titleCapabilities =
        String.join(
            ",", contextRequest.getHeadersMap().get(Headers.TITLE_CAPABILITIES).getValuesList());

    Assert.assertEquals("foo1", l10nStr);
    Assert.assertEquals("foo2", featureCapabilities);
    Assert.assertEquals("foo3", titleCapabilities);
  }

  private void setupRequestHeaders(HttpServletRequest request) {
    when(request.getHeaderNames())
        .thenReturn(
            Collections.enumeration(
                Lists.newArrayList(
                    Headers.LOCALIZATION_FEATURES,
                    Headers.FEATURE_CAPABILITIES,
                    Headers.TITLE_CAPABILITIES,
                    Headers.PASSPORT)));
    when(request.getAttributeNames())
        .thenReturn(
            Collections.enumeration(
                Lists.newArrayList(
                    Headers.LOCALIZATION_FEATURES,
                    Headers.FEATURE_CAPABILITIES,
                    Headers.TITLE_CAPABILITIES)));

    when(request.getHeader(Headers.PASSPORT)).thenReturn(MOCK_PASSPORT);
    when(request.getHeaders(Headers.LOCALIZATION_FEATURES))
        .thenReturn(
            Collections.enumeration(Lists.newArrayList(Defaults.DEFAULT_LOCALIZATION_FEATURES)));
    when(request.getHeaders(Headers.FEATURE_CAPABILITIES))
        .thenReturn(
            Collections.enumeration(Lists.newArrayList(Defaults.DEFAULT_FEATURE_CAPABILITIES)));
    when(request.getHeaders(Headers.TITLE_CAPABILITIES))
        .thenReturn(
            Collections.enumeration(Lists.newArrayList(Defaults.DEFAULT_TITLE_CAPABILITIES)));
  }

  @Test
  public void handlePassportFixRCMissing() {
    when(property.get()).thenReturn(true);
    HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
    when(request.getHeader(Headers.PASSPORT)).thenReturn(MOCK_PASSPORT);
    final RequestContext requestContext = CurrentRequestContext.get();
    assertFalse(Passports.passport(requestContext).isPresent());
    final String pass = MicrocontextCallerInterceptor.handlePassport(request, counter, property);
    final Optional<String> passport = Passports.passport(requestContext);
    assertTrue(passport.isPresent());
    assertEquals(MOCK_PASSPORT, pass);
    assertEquals(pass, passport.get());
    assertEquals(1L, counter.actualCount(), 0L);
  }

  @Test
  public void handlePassportFixHeaderWins() {
    when(property.get()).thenReturn(true);
    HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
    when(request.getHeader(Headers.PASSPORT)).thenReturn(MOCK_PASSPORT);
    final RequestContext requestContext = CurrentRequestContext.get();
    AuthResolvers.setPassport("foo-passport");
    final Optional<String> initialPassport = Passports.passport(requestContext);
    assertTrue(initialPassport.isPresent());
    assertEquals("foo-passport", initialPassport.get());
    final String pass = MicrocontextCallerInterceptor.handlePassport(request, counter, property);
    final Optional<String> passport = Passports.passport(requestContext);
    assertTrue(passport.isPresent());
    assertEquals(MOCK_PASSPORT, pass);
    assertEquals(pass, passport.get());
    assertEquals(1L, counter.actualCount(), 0L);
  }

  @Test
  public void handlePassportDisabled() {
    when(property.get()).thenReturn(false);
    HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
    when(request.getHeader(Headers.PASSPORT)).thenReturn(MOCK_PASSPORT);
    final RequestContext requestContext = CurrentRequestContext.get();
    assertFalse(Passports.passport(requestContext).isPresent());
    final String pass = MicrocontextCallerInterceptor.handlePassport(request, counter, property);
    final Optional<String> passport = Passports.passport(requestContext);
    assertFalse(passport.isPresent());
    assertEquals(MOCK_PASSPORT, pass);
    assertEquals(1L, counter.actualCount(), 0L);
  }

  @Test
  public void handlePassportNoHeader() {
    when(property.get()).thenReturn(false);
    HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
    final RequestContext requestContext = CurrentRequestContext.get();
    AuthResolvers.setPassport(MOCK_PASSPORT);
    assertTrue(Passports.passport(requestContext).isPresent());
    final String pass = MicrocontextCallerInterceptor.handlePassport(request, counter, property);
    final Optional<String> passport = Passports.passport(requestContext);
    assertTrue(passport.isPresent());
    assertEquals(MOCK_PASSPORT, pass);
    assertEquals(MOCK_PASSPORT, passport.get());
    assertEquals(0L, counter.actualCount(), 0L);
  }
}
