package com.netflix.microcontext.interceptor.caller;

import static com.netflix.microcontext.interceptor.caller.MicrocontextHeaderAugmentingInterceptor.Defaults;
import static com.netflix.microcontext.interceptor.caller.MicrocontextHeaderAugmentingInterceptor.Properties;

import com.netflix.microcontext.init.headers.Headers;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;
import org.springframework.core.env.Environment;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

public class MicrocontextHeaderAugmentingInterceptorTest {

  private MicrocontextHeaderAugmentingInterceptor interceptor;

  private Environment env;

  @Before
  public void setup() {
    env = Mockito.mock(Environment.class);
    interceptor = new MicrocontextHeaderAugmentingInterceptor(env);
  }

  @Test
  public void shouldSetDefaultAttributes() throws Exception {
    setupEnv();

    HttpServletRequest httpServletRequest = new MockHttpServletRequest();
    HttpServletResponse httpServletResponse = new MockHttpServletResponse();

    interceptor.preHandle(httpServletRequest, httpServletResponse, null);
    Object localizationAttribute = httpServletRequest.getAttribute(Headers.LOCALIZATION_FEATURES);
    Object titleCapabilities = httpServletRequest.getAttribute(Headers.TITLE_CAPABILITIES);
    Object featureCapabilities = httpServletRequest.getAttribute(Headers.FEATURE_CAPABILITIES);
    Assertions.assertEquals(Defaults.DEFAULT_LOCALIZATION_FEATURES, localizationAttribute);
    Assertions.assertEquals(Defaults.DEFAULT_TITLE_CAPABILITIES, titleCapabilities);
    Assertions.assertEquals(Defaults.DEFAULT_FEATURE_CAPABILITIES, featureCapabilities);
  }

  private void setupEnv() {
    Mockito.when(env.getProperty(Properties.ADD_CAPABILITY_HEADERS_PROPERTY, Boolean.class, true))
        .thenReturn(true);
    Mockito.when(
            env.getProperty(
                Properties.LOCALIZATION_FEATURES_PROPERTY,
                String.class,
                Defaults.DEFAULT_LOCALIZATION_FEATURES))
        .thenReturn(Defaults.DEFAULT_LOCALIZATION_FEATURES);
    Mockito.when(
            env.getProperty(
                Properties.TITLE_CAPABILITIES_PROPERTY,
                String.class,
                Defaults.DEFAULT_TITLE_CAPABILITIES))
        .thenReturn(Defaults.DEFAULT_TITLE_CAPABILITIES);
    Mockito.when(
            env.getProperty(
                Properties.FEATURE_CAPABILITIES_PROPERTY,
                String.class,
                Defaults.DEFAULT_FEATURE_CAPABILITIES))
        .thenReturn(Defaults.DEFAULT_FEATURE_CAPABILITIES);
  }
}
