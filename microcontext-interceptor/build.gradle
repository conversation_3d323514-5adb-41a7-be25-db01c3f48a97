apply plugin: 'netflix.jvm-library'
apply plugin: 'netflix.spring-boot-netflix-library'

dependencies {
    api project(':microcontext-proto-definition')
    api project(':microcontext-access')
    api 'com.netflix.ixms:ixms-proto-definition:latest.release'

    // this has to be an api level dependency so that clients have access to the @GrpcSpringClient of microcontext
    api "com.netflix.spring:spring-boot-netflix-starter-grpc-client"

    compileOnly "com.netflix.spring:spring-boot-netflix-starter-library"

    testImplementation 'com.netflix.spring:spring-boot-netflix-starter-test'
    testRuntimeOnly 'org.junit.vintage:junit-vintage-engine'
}
tasks.withType(Test).configureEach {
    useJUnitPlatform()
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(8)
    }
}


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations:latest.release'
}
