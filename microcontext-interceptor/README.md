# Microcontext Interceptor

This interceptor performs basic handling of Microcontext inputs. This interceptor is meant for use
by servers which rely on a valid Microcontext in the request context.

It employs:

* a Spring Interceptor that reads Microcontext from header or context inputs and if none are found
  can conditionally
  make a blocking call to the microcontext microcontext service
* the SBN gRPC library, specifically: `com.netflix.springboot.grpc.client.GrpcSpringClient` in `
  com.netflix.spring:spring-boot-netflix-starter-application-web-runtime`

Do not configure/consume this module if any of these pose runtime challenges. Most SBN servers
already take advantage of the SBN gRPC library.

### Registering with your Spring server

If you wish to use this interceptor, register this in your web config

```java
import com.netflix.microcontext.interceptor.caller.MicrocontextCallerInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class MyWebConfig implements WebMvcConfigurer {

  private final MicrocontextCallerInterceptor microcontextCallerInterceptor;

  @Autowired
  public GustoWebConfig(MicrocontextCallerInterceptor microcontextCallerInterceptor) {
    this.microcontextCallerInterceptor = microcontextCallerInterceptor;
  }

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(this.microcontextCallerInterceptor).addPathPatterns("/<path>");
  }
}
```

### Configuration

All properties are prefixed by the prefix `com.netflix.microcontext.caller.interceptor`

| Property                   | Default |                                                                                                   Description |
|----------------------------|:-------:|--------------------------------------------------------------------------------------------------------------:|
| \<prefix\>.metrics.enabled |  true   |                              Indicates if detailed metrics about fields contained in microcontext are emitted |
| \<prefix\>.call.remote     |  false  | If set to false, the interceptor will not call the microcontext to resolve if nothing is present in the input |
| \<prefix\>.headers.favored |  true   |              Indicates if request headers should override request attributes for the microcontext server call |