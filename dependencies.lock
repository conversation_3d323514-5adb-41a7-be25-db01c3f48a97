{"resolutionRules": {"com.netflix.nebula:gradle-resolution-rules": {"locked": "0.82.0"}, "netflix.nebula.resolutionrules:resolution-rules": {"locked": "1.357.0"}}, "springBootNetflixRolloutRule": {"com.netflix.sbnfeaturerolloutrule:sbn-feature-rollout-rule": {"locked": "0.1.5220"}}, "testCompileClasspath": {"com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}}, "testRuntimeClasspath": {"com.atomicjar:testcontainers-cloud-provider-strategy": {"locked": "0.0.41", "transitive": ["com.netflix.nebula.testcontainers:testcontainers-cloud-configurer"]}, "com.gradle:develocity-testing-annotations": {"locked": "2.0.1"}, "com.gradle:gradle-enterprise-testing-annotations": {"locked": "1.0"}, "com.netflix.nebula.testcontainers:testcontainers-cloud-configurer": {"locked": "1.16.0"}, "org.apiguardian:apiguardian-api": {"locked": "1.1.2", "transitive": ["org.junit.platform:junit-platform-commons", "org.junit.platform:junit-platform-engine", "org.junit.platform:junit-platform-launcher"]}, "org.junit.platform:junit-platform-commons": {"locked": "1.12.2", "transitive": ["org.junit.platform:junit-platform-engine"]}, "org.junit.platform:junit-platform-engine": {"locked": "1.12.2", "transitive": ["org.junit.platform:junit-platform-launcher"]}, "org.junit.platform:junit-platform-launcher": {"locked": "1.12.2"}, "org.opentest4j:opentest4j": {"locked": "1.3.0", "transitive": ["org.junit.platform:junit-platform-engine"]}}}