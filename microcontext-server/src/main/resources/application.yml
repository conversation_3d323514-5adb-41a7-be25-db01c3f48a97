spring:
  application:
    name: microcontext

# couldn't figure out how to yaml this one under a leaf value
grpc.service.MicrocontextService.interceptor.concurrency_limit.type.gradient.initialLimit: 75

grpc:
  service:
    MicrocontextService:
      interceptor:
        concurrency_limit:
          type: gradient2
        accessLogging:
          enabled: false
        metatron:
          clientCertHeader:
            enabled: true
            priority: 1
  server:
    default:
      maxHeaderListSize: 5242880
    microcontext:
      sslContext: metatron
  client:
    ixms:
      channel:
        primeConnectionsEnabled: true
        primeConnectionsTimeoutSeconds: 5
      IxmsService:
        interceptor:
          retry:
            default:
              statuses: UNAVAILABLE
    ember:
      channel:
        primeConnectionsEnabled: true
        primeConnectionsTimeoutSeconds: 5
    mantispublishgrpc:
      MantisPublishGrpcService:
        channel:
          primeConnectionsEnabled: true
          primeConnectionsTimeoutSeconds: 5
    default:
      channel:
        mesh:
          egress:
            enabled: true
  swagger:
    enableMicrocontextHeader: true

# default for all grpc clients to 1s
grpc.client.default.interceptor.timeout.default: 1000
grpc.client.dcmsrt.channel.DcmsRtService.interceptor.timeout.default: 100

logging:
  level:
    root: WARN
    AccessLog: OFF

management:
  httpexchanges.recording.enabled: true

IXMS_TARGET_STACK: prod
