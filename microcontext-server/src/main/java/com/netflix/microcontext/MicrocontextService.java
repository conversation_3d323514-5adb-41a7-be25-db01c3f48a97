package com.netflix.microcontext;

import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.MicrocontextImpl;
import com.netflix.microcontext.init.serializers.Serializers;
import com.netflix.microcontext.protogen.DeserializeContextRequest;
import com.netflix.microcontext.protogen.EchoContextRequest;
import com.netflix.microcontext.protogen.EchoContextResponse;
import com.netflix.microcontext.protogen.GetContextRequest;
import com.netflix.microcontext.protogen.GetContextResponse;
import com.netflix.microcontext.protogen.GetExperimentationRequest;
import com.netflix.microcontext.protogen.GetExperimentationResponse;
import com.netflix.microcontext.protogen.MicrocontextServiceGrpc.MicrocontextServiceImplBase;
import com.netflix.microcontext.protogen.SerializeContextRequest;
import com.netflix.microcontext.protogen.SerializeContextResponse;
import com.netflix.microcontext.resolver.experimentation.ExperimentationResolver;
import com.netflix.microcontext.resolvers.MicroResolver;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import java.util.Optional;
import netflix.context.Context;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MicrocontextService extends MicrocontextServiceImplBase {

  private static final Logger logger = LoggerFactory.getLogger(MicrocontextService.class);
  private final MicroResolver resolver;
  private final ExperimentationResolver experimentationResolver;

  @Autowired
  public MicrocontextService(
      MicroResolver resolver, ExperimentationResolver experimentationResolver) {
    this.resolver = resolver;
    this.experimentationResolver = experimentationResolver;
  }

  @Override
  public void deserializeContext(
      DeserializeContextRequest request, StreamObserver<GetContextResponse> responseObserver) {
    try {
      String serializedContext = request.getSerializedContext();
      Optional<Context> context = Serializers.fromRawString(serializedContext);
      if (context.isPresent()) {
        responseObserver.onNext(GetContextResponse.newBuilder().setContext(context.get()).build());
        responseObserver.onCompleted();
      } else {
        throw Status.INVALID_ARGUMENT
            .withDescription("Could not deserialize context")
            .asRuntimeException();
      }
    } catch (Throwable t) {
      responseObserver.onError(t);
    }
  }

  @Override
  public void getContext(
      GetContextRequest request, StreamObserver<GetContextResponse> responseObserver) {
    resolver
        .resolve(request)
        .whenComplete(
            (context, throwable) -> {
              if (throwable == null) {
                responseObserver.onNext(
                    GetContextResponse.newBuilder().setContext(context).build());
                responseObserver.onCompleted();
              } else {
                logger.error("Could not resolve context", throwable);
                responseObserver.onError(throwable);
              }
            });
  }

  @Override
  public void echoContext(
      EchoContextRequest request, StreamObserver<EchoContextResponse> responseObserver) {
    try {
      responseObserver.onNext(
          EchoContextResponse.newBuilder().setContext(CurrentMicrocontext.get().toProto()).build());
      responseObserver.onCompleted();
    } catch (Throwable t) {
      responseObserver.onError(t);
    }
  }

  @Override
  public void serializeContext(
      SerializeContextRequest request, StreamObserver<SerializeContextResponse> responseObserver) {
    try {
      responseObserver.onNext(
          SerializeContextResponse.newBuilder()
              .setSerializedContext(MicrocontextImpl.toSerialized(request.getContext()))
              .build());
      responseObserver.onCompleted();
    } catch (Throwable t) {
      responseObserver.onError(t);
    }
  }

  @Override
  public void getExperimentation(
      GetExperimentationRequest request,
      StreamObserver<GetExperimentationResponse> responseObserver) {
    final Context context;
    if (request.hasSerializedContext()) {
      context = Serializers.fromRawString(request.getSerializedContext()).orElse(null);
    } else if (request.hasContext()) {
      context = request.getContext();
    } else {
      context = null;
    }

    if (context == null) {
      throw Status.INVALID_ARGUMENT
          .withDescription("No valid context present")
          .asRuntimeException();
    }

    experimentationResolver
        .resolve(context)
        .whenComplete(
            (experimentationContext, throwable) -> {
              if (experimentationContext != null) {
                responseObserver.onNext(
                    GetExperimentationResponse.newBuilder()
                        .setExperimentation(experimentationContext)
                        .build());
                responseObserver.onCompleted();
              }
              if (throwable != null) {
                responseObserver.onError(throwable);
              }
            });
  }
}
