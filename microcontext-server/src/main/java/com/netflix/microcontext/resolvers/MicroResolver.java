package com.netflix.microcontext.resolvers;

import com.netflix.archaius.api.PropertyRepository;
import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.resolvers.RequestQueryResolver;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.headers.MapHeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.microcontext.init.resolvers.BaseResolvers;
import com.netflix.microcontext.init.resolvers.ClientResolvers;
import com.netflix.microcontext.init.resolvers.VideoResolvers;
import com.netflix.microcontext.init.resolvers.VisitResolvers;
import com.netflix.microcontext.protogen.GetContextRequest;
import com.netflix.microcontext.resolver.user.UserResolver;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.passport.introspect.PassportIdentityFactory;
import com.netflix.passport.introspect.PassportIntrospectionException;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.servo.monitor.DynamicCounter;
import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import netflix.context.Context;
import netflix.context.Context.Builder;
import netflix.context.client.ClientContext;
import netflix.context.visit.RequestPriority;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MicroResolver {

  private static final Logger logger = LoggerFactory.getLogger(MicroResolver.class);

  private final UserResolver userResolver;
  private final DeviceResolver deviceResolver;
  private final PropertyRepository propertyRepository;
  private final PassportIdentityFactory passportIdentityFactory;
  private static final String MICROCONTEXT_ERROR_METRIC_KEY = "microcontext.error";
  private static final String MICROCONTEXT_ERROR_METRIC_TAG_KEY = "missing_contexts";

  @Autowired
  public MicroResolver(
      DeviceResolver deviceResolver,
      UserResolver userResolver,
      PropertyRepository propertyRepository,
      PassportIdentityFactory passportIdentityFactory) {
    this.deviceResolver = deviceResolver;
    this.userResolver = userResolver;
    this.propertyRepository = propertyRepository;
    this.passportIdentityFactory = passportIdentityFactory;
  }

  public CompletionStage<Context> resolve(GetContextRequest request) {
    try {
      PassportIdentity passport = getPassportIdentity(request);
      final Context.Builder contextBuilder = Context.newBuilder();
      final RequestPriority requestPriority =
          CurrentMicrocontext.getRaw()
              .map(microcontext -> microcontext.getVisit().getPriority())
              .orElse(null);
      HeaderResolver headers = MapHeaderResolver.of(request.getHeadersMap());

      // Params are not passed via the server call. We'll eventually get rid of this endpoint once
      // Zuul becomes the source of truth.
      ParamResolver requestQueryResolver = RequestQueryResolver.of(Map.of());
      final RequestContext requestContext = CurrentRequestContext.get();
      return getUser(contextBuilder)
          .thenApply(builder -> base(builder, request, requestContext))
          .thenApply(builder -> visit(builder, request, requestContext, requestPriority))
          .thenApply(
              builder -> deviceResolver.resolve(builder, passport, headers, requestQueryResolver))
          .thenApply(builder -> client(builder, request))
          .thenApply(builder -> video(builder, requestContext))
          .thenApply(Builder::build)
          .thenApply(context -> logContext(context, passport != null));
    } catch (Exception e) {
      logger.error("fatal exception creating context", e);
      return CompletableFuture.failedFuture(e);
    }
  }

  private CompletionStage<Builder> getUser(Builder contextBuilder) {
    if (propertyRepository.get("microcontext.user.resolve", Boolean.class).orElse(false).get()) {
      return userResolver.resolve(contextBuilder);
    }
    return CompletableFuture.completedFuture(contextBuilder);
  }

  private Context logContext(Context context, boolean passport) {
    if (!context.isInitialized() || context == context.getDefaultInstanceForType()) {
      DynamicCounter.increment(
          MICROCONTEXT_ERROR_METRIC_KEY, MICROCONTEXT_ERROR_METRIC_TAG_KEY, "all_missing");
    } else {
      List<String> missingContexts = new ArrayList<>();
      if (context.getLocalesList().stream().findFirst().isEmpty()) {
        missingContexts.add("locale");
      }
      if (!context.getCountry().isInitialized()
          || context.getCountry() == context.getCountry().getDefaultInstanceForType()) {
        missingContexts.add("country");
      }
      if (!context.getDevice().isInitialized()
          || context.getDevice() == context.getDevice().getDefaultInstanceForType()) {
        MantisPublishContext.getCurrent().add("missing_context_device", "true");
        missingContexts.add("device");
      }
      if (!context.getClient().isInitialized()
          || context.getClient() == context.getClient().getDefaultInstanceForType()) {
        missingContexts.add("client");
      }
      String missingContextsStr =
          missingContexts.isEmpty() ? "none" : String.join("_", missingContexts);
      MantisPublishContext.getCurrent().add("missing_contexts", missingContextsStr);
      if (context.getClient() != context.getClient().getDefaultInstanceForType()) {
        // add the platform if it's there
        DynamicCounter.increment(
            MICROCONTEXT_ERROR_METRIC_KEY,
            MICROCONTEXT_ERROR_METRIC_TAG_KEY,
            missingContextsStr,
            "ui_category",
            context.getClient().getClientCategory().name(),
            "passport",
            Boolean.toString(passport));
      } else {
        DynamicCounter.increment(
            MICROCONTEXT_ERROR_METRIC_KEY,
            MICROCONTEXT_ERROR_METRIC_TAG_KEY,
            missingContextsStr,
            "passport",
            Boolean.toString(passport));
      }
    }
    return context;
  }

  @Nullable
  private PassportIdentity getPassportIdentity(GetContextRequest request) {
    if (request.hasPassport()) {
      try {
        return passportIdentityFactory.createPassportIdentity(request.getBoxedPassport());
      } catch (PassportIntrospectionException e) {
        logger.error("Could not create passport", e);
      }
    }
    return null;
  }

  public static Context.Builder base(
      Context.Builder builder, GetContextRequest request, RequestContext requestContext) {
    final Microcontext microcontext = CurrentMicrocontext.get();
    return BaseResolvers.base(
        builder,
        microcontext.getUser(),
        microcontext.getGeo(),
        requestContext,
        MapHeaderResolver.of(request.getHeadersMap()));
  }

  private Context.Builder video(Context.Builder builder, RequestContext requestContext) {
    VideoResolvers.resolve(requestContext).ifPresent(builder::setVideo);
    return builder;
  }

  public static Builder visit(
      Context.Builder builder,
      GetContextRequest request,
      RequestContext requestContext,
      @Nullable RequestPriority requestPriority) {
    return builder.setVisit(
        VisitResolvers.resolve(
            MapHeaderResolver.of(request.getHeadersMap()), requestContext, requestPriority, null));
  }

  public Context.Builder client(Context.Builder builder, GetContextRequest request) {
    final Boolean resolveCapabilities =
        propertyRepository
            .get("microcontext.client.capabilities.resolve", Boolean.class)
            .orElse(true)
            .get();
    final Optional<ClientContext> clientContext =
        ClientResolvers.resolve(
            MapHeaderResolver.of(request.getHeadersMap()),
            ParamResolver.EMPTY,
            resolveCapabilities,
            builder.getDevice(),
            builder.getVisit().getUserAgent().getClientDetails());
    clientContext.ifPresent(builder::setClient);
    return builder;
  }
}
