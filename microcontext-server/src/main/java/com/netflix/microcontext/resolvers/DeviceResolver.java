package com.netflix.microcontext.resolvers;

import static com.netflix.microcontext.init.device.DeviceResolvers.ALL_PROPERTIES;

import com.netflix.mantis.publish.api.MantisPublishContext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.init.device.DeviceResolvers;
import com.netflix.microcontext.init.headers.HeaderResolver;
import com.netflix.microcontext.init.params.ParamResolver;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.servo.monitor.DynamicCounter;
import com.netflix.streaming.dts.client.DtsClient;
import com.netflix.streaming.dts.common.model.DeviceType;
import com.netflix.streaming.dts.common.model.DeviceTypeProperties;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import netflix.context.Context;
import netflix.context.Context.Builder;
import netflix.context.auth.AuthContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DeviceResolver {

  private static final Logger logger = LoggerFactory.getLogger(DeviceResolver.class);
  private final DtsClient dtsClient;

  @Autowired
  public DeviceResolver(DtsClient dtsClient) {
    this.dtsClient = dtsClient;
  }

  public Builder resolve(
      final Context.Builder builder,
      PassportIdentity passport,
      final HeaderResolver headerResolver,
      final ParamResolver paramResolver) {
    try {
      RequestContext requestContext = CurrentRequestContext.get();
      Integer deviceTypeId = null;
      Optional<String> esn = DeviceResolvers.resolveEsn(passport, headerResolver, paramResolver);
      MantisPublishContext.getCurrent().add("emptyesn", esn.isEmpty());
      AuthContext auth = CurrentMicrocontext.get().getAuth();
      if (auth != AuthContext.getDefaultInstance()) {
        if (auth.hasDeviceType()) {
          deviceTypeId = auth.getDeviceType().getId();
        }
      }
      if (deviceTypeId == null && requestContext.getDeviceType() != null) {
        deviceTypeId = requestContext.getDeviceType().getId();
      }

      DeviceType deviceType = null;
      if (deviceTypeId != null) {
        deviceType = dtsClient.getDeviceTypeFromId(deviceTypeId);
      } else if (esn.isPresent()) {
        deviceType = dtsClient.getDeviceTypeFromESN(esn.get());
        if (deviceType != null) {
          deviceTypeId = deviceType.getDeviceTypeId();
        }
      }
      builder.setDevice(
          DeviceResolvers.resolve(esn.orElse(null), deviceTypeId, properties(deviceType)));
    } catch (Throwable t) {
      DynamicCounter.increment("microcontext.resolver.device.error");
      logger.error("Could not create resolve context", t);
    }
    return builder;
  }

  @Nonnull
  private static Map<String, String> properties(@Nullable DeviceType deviceType) {
    if (deviceType == null) {
      return Collections.emptyMap();
    }
    final DeviceTypeProperties<?> properties = deviceType.getProperties();
    Map<String, String> map = new HashMap<>(7);
    for (String property : ALL_PROPERTIES) {
      final String value = properties.getString(property);
      if (value != null) {
        map.put(property, value);
      }
    }

    return map;
  }
}
