/**
 * Entry point for this application. Includes a main method for local debugging as well as an
 * entrypoint to be invoked by Servlet 3.0+ containers provided by the {@link
 * org.springframework.web.WebApplicationInitializer} interface.
 *
 * @see <a
 *     href="https://docs.spring.io/spring-boot/docs/current/reference/htmlsingle/#using-boot-using-springbootapplication-annotation">@SpringBootApplication
 *     Reference</a>
 * @see <a
 *     href="https://docs.spring.io/spring-boot/docs/current/reference/htmlsingle/#boot-features-embedded-container-context-initializer">Spring
 *     Servlet 3.0+ Support via WebApplicationInitializer</a>
 */
package com.netflix.microcontext;
