package com.netflix.microcontext;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;

import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.protogen.GetContextRequest;
import com.netflix.microcontext.protogen.MicrocontextServiceGrpc;
import com.netflix.microcontext.util.TestPassportUtil;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.springboot.grpc.test.EmbeddedGrpc;
import com.netflix.type.NFCountry;
import netflix.context.Context;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SuppressWarnings("SpringBootApplicationProperties")
@RunWith(SpringRunner.class)
@SpringBootTest(
    properties = {
      // make it fallback
      "grpc.client.microcontext.channel.target=localhost:8981"
    })
@EnableAutoConfiguration
@EmbeddedGrpc
public class MicrocontextFallbackSmokeTest {

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @GrpcSpringClient("microcontext")
  private MicrocontextServiceGrpc.MicrocontextServiceBlockingStub blockingStub;

  @Test
  public void testBasicFallback() {
    PassportIdentity passport = TestPassportUtil.generateTestPassport();
    BindingContexts.runWithNewContext(
        () -> {
          RequestContext requestContext = CurrentRequestContext.get();
          requestContext.setCountry(NFCountry.US);
          requestContext.setLocale("en");
          requestContext.setDeviceId("fooesn");
          Context context =
              blockingStub
                  .getContext(
                      GetContextRequest.newBuilder()
                          .setIpAddress("*******")
                          .setPassport(passport.getPassportAsString())
                          .build())
                  .getContext();
          assertNotNull(context);
          assertEquals("US", context.getCountry().getId());
          assertEquals("en", context.getLocales(0).getId());
          assertEquals(passport.getEsn().orElse(null), context.getDevice().getBoxedEsn());
        });
  }

  @Test
  public void testNoData() {
    BindingContexts.runWithNewContext(
        () -> {
          Context context =
              blockingStub
                  .getContext(GetContextRequest.newBuilder().setIpAddress("*******").build())
                  .getContext();
          assertNotNull(context);
          assertFalse(context.getDevice().hasEsn());
        });
  }
}
