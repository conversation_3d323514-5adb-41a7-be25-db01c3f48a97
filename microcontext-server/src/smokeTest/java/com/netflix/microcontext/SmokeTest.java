package com.netflix.microcontext;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import com.google.protobuf.FieldMask;
import com.netflix.archaius.api.config.SettableConfig;
import com.netflix.archaius.api.inject.RuntimeLayer;
import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.access.server.grpc.MicrocontextClientInterceptor;
import com.netflix.microcontext.access.server.grpc.MicrocontextForwardingInterceptor;
import com.netflix.microcontext.protogen.EchoContextRequest;
import com.netflix.microcontext.protogen.EchoContextResponse;
import com.netflix.microcontext.protogen.GetContextRequest;
import com.netflix.microcontext.protogen.MicrocontextServiceGrpc;
import com.netflix.microcontext.test.interceptor.TestMicrocontextInterceptor;
import com.netflix.microcontext.test.utils.TestMicrocontext;
import com.netflix.microcontext.util.TestPassportUtil;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.springboot.grpc.client.GrpcSpringClient;
import com.netflix.springboot.grpc.test.EmbeddedGrpc;
import com.netflix.type.proto.Countries;
import com.netflix.type.protogen.BasicTypes.Country;
import com.netflix.type.protogen.BasicTypes.DeviceType;
import java.util.HashMap;
import java.util.Map;
import netflix.context.Context;
import netflix.context.Models;
import netflix.context.client.ClientContext;
import netflix.context.client.category.ClientCategory;
import netflix.context.client.flavor.ClientFlavor;
import netflix.context.client.formfactor.ClientFormFactor;
import netflix.context.common.StringList;
import netflix.context.common.Version;
import netflix.context.device.DeviceContext;
import netflix.context.user.User;
import netflix.context.user.UserContext;
import netflix.context.user.UserContext.Builder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@EnableAutoConfiguration
@EmbeddedGrpc
public class SmokeTest {

  private static final Country US = Countries.toProtobuf("US");

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @GrpcSpringClient("microcontext")
  private MicrocontextServiceGrpc.MicrocontextServiceBlockingStub blockingStub;

  @Autowired @RuntimeLayer SettableConfig settableConfig;

  @Test
  public void clientConnects() {
    System.out.println("I connected");
  }

  @Test
  public void interceptHeaderBinary() {
    EchoContextResponse r =
        blockingStub
            .withInterceptors(
                MicrocontextForwardingInterceptor.of(Context.newBuilder().setCountry(US).build()))
            .echoContext(EchoContextRequest.newBuilder().build());
    assertEquals(US, r.getContext().getCountry());
  }

  @Test
  public void interceptHeaderASCII() {
    EchoContextResponse r =
        blockingStub
            .withInterceptors(
                TestMicrocontextInterceptor.of(Context.newBuilder().setCountry(US).build()))
            .echoContext(EchoContextRequest.newBuilder().build());
    assertEquals(US, r.getContext().getCountry());
  }

  @Test
  public void interceptHeaderTest() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder(Context.newBuilder().setCountry(US).build()).build();
    EchoContextResponse r =
        blockingStub
            .withInterceptors(TestMicrocontextInterceptor.of(testMicrocontext))
            .echoContext(EchoContextRequest.newBuilder().build());
    assertEquals(US, r.getContext().getCountry());
  }

  @Test
  public void requestContext() {
    BindingContexts.push();
    try {
      assertTrue(
          CurrentMicrocontext.set(
              Context.newBuilder()
                  .setCountry(US)
                  .setDevice(DeviceContext.newBuilder().setEsn("foobar"))
                  .build()));
      EchoContextResponse r =
          blockingStub
              .withInterceptors(new MicrocontextClientInterceptor())
              .echoContext(EchoContextRequest.newBuilder().build());
      assertEquals(US, r.getContext().getCountry());
      assertEquals("foobar", r.getContext().getDevice().getBoxedEsn());
    } finally {
      BindingContexts.pop();
    }
  }

  @Test
  public void requestContextMakeCallProperty() throws Exception {
    try {
      settableConfig.setProperty("microcontext.server.interceptor.clear.user", false);
      final TestMicrocontext testMicrocontext =
          TestMicrocontext.builder(
                  Context.newBuilder()
                      .setCountry(US)
                      .setDevice(DeviceContext.newBuilder().setEsn("foobar"))
                      .setUser(
                          UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(1234)))
                      .build())
              .setClearUser(false)
              .build();
      EchoContextResponse r =
          testMicrocontext.call(
              () -> blockingStub.echoContext(EchoContextRequest.newBuilder().build()));
      assertEquals(US, r.getContext().getCountry());
      assertEquals("foobar", r.getContext().getDevice().getBoxedEsn());
      assertEquals(1234, r.getContext().getUser().getCurrentUser().getId());
    } finally {
      settableConfig.clearProperty("microcontext.server.interceptor.clear.user");
    }
  }

  @Test
  public void requestContextMakeCallPropertyNoClear() {
    try {
      settableConfig.setProperty("microcontext.server.interceptor.clear.user", false);
      final TestMicrocontext testMicrocontext =
          TestMicrocontext.builder(
                  Context.newBuilder()
                      .setCountry(US)
                      .setDevice(DeviceContext.newBuilder().setEsn("foobar"))
                      .setUser(
                          UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(1234)))
                      .build())
              .build();
      EchoContextResponse r =
          testMicrocontext.makeCall(
              () -> blockingStub.echoContext(EchoContextRequest.newBuilder().build()));
      assertEquals(US, r.getContext().getCountry());
      assertEquals("foobar", r.getContext().getDevice().getBoxedEsn());
      assertFalse(r.getContext().hasUser());
    } finally {
      settableConfig.clearProperty("microcontext.server.interceptor.clear.user");
    }
  }

  @Test
  public void requestContextMakeCallDefault() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder(
                Context.newBuilder()
                    .setCountry(US)
                    .setDevice(DeviceContext.newBuilder().setEsn("foobar"))
                    .setUser(UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(1234)))
                    .build())
            .build();
    EchoContextResponse r =
        testMicrocontext.makeCall(
            () -> blockingStub.echoContext(EchoContextRequest.newBuilder().build()));
    assertEquals(US, r.getContext().getCountry());
    assertEquals("foobar", r.getContext().getDevice().getBoxedEsn());
    assertFalse(r.getContext().hasUser());
  }

  @Test
  public void requestContextMakeRunDefault() {
    TestMicrocontext.builder(
            Context.newBuilder()
                .setCountry(US)
                .setDevice(DeviceContext.newBuilder().setEsn("foobar"))
                .build())
        .build()
        .run(
            () -> {
              EchoContextResponse r =
                  blockingStub.echoContext(EchoContextRequest.newBuilder().build());
              assertEquals(US, r.getContext().getCountry());
              assertEquals("foobar", r.getContext().getDevice().getBoxedEsn());
            });
  }

  @Test
  public void requestContextMakeCallBinding() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder(
                Context.newBuilder()
                    .setCountry(US)
                    .setDevice(DeviceContext.newBuilder().setEsn("foobar"))
                    .setUser(UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(1234)))
                    .build())
            .build();
    BindingContexts.push();
    try {
      testMicrocontext.makeCurrent();
      EchoContextResponse r = blockingStub.echoContext(EchoContextRequest.newBuilder().build());
      assertEquals(US, r.getContext().getCountry());
      assertEquals("foobar", r.getContext().getDevice().getBoxedEsn());
      assertFalse(r.getContext().hasUser());
    } finally {
      BindingContexts.pop();
    }
  }

  @Test
  public void requestContextMakeCallBindingSet() {
    BindingContexts.push();
    try {
      CurrentMicrocontext.set(
          Context.newBuilder()
              .setCountry(Country.newBuilder().setId("US").build())
              .setUser(
                  UserContext.newBuilder()
                      .setCurrentUser(User.newBuilder().setId(1234).build())
                      .build())
              .build());
      EchoContextResponse r = blockingStub.echoContext(EchoContextRequest.newBuilder().build());
      assertEquals(US, r.getContext().getCountry());
      assertFalse(r.getContext().hasUser());
    } finally {
      BindingContexts.pop();
    }
  }

  @Test
  public void requestContextMakeCallBindingSetNoClear() {
    settableConfig.setProperty("microcontext.server.interceptor.clear.user", false);
    BindingContexts.push();
    try {
      CurrentMicrocontext.set(
          Context.newBuilder()
              .setCountry(Country.newBuilder().setId("US").build())
              .setUser(
                  UserContext.newBuilder()
                      .setCurrentUser(User.newBuilder().setId(1234).build())
                      .build())
              .build());
      EchoContextResponse r = blockingStub.echoContext(EchoContextRequest.newBuilder().build());
      assertEquals(US, r.getContext().getCountry());
      assertEquals(1234, r.getContext().getUser().getCurrentUser().getId());
    } finally {
      settableConfig.clearProperty("microcontext.server.interceptor.clear.user");
      BindingContexts.pop();
    }
  }

  @Test
  @Ignore(
      "geodata cannot be read from a header in the request message, must be a header on the request")
  public void resolveContextFromRequest() {
    Map<String, StringList> headers = new HashMap<>();
    headers.put(
        "x-netflix.request.sub.context.geodata",
        StringList.newBuilder()
            .addValues(
                "zip=94087&ipaddress=***********&city=SUNNYVALE&timezone=PST&nf_ip_provider=blend&long=-122.03&country_code=US&dradisSite=sjc002.ix&dradisRegions=81&domain=nflxvideo.net&asnum=2906&dma=807&company=Netflix_Inc&lat=37.35&region_code=CA")
            .build());

    GetContextRequest request =
        GetContextRequest.newBuilder()
            .putAllHeaders(headers)
            .setIpAddress("*************")
            .setPassport(TestPassportUtil.generateTestPassport().getPassportAsString())
            .build();
    Context context = blockingStub.getContext(request).getContext();
    System.out.println("context: " + context);

    assertEquals("US", context.getCountry().getId());
    assertTrue("en-US".equalsIgnoreCase(context.getLocales(0).getId()));
    assertEquals(
        (int) TestPassportUtil.DEFAULT_DEVICE_TYPE_ID, context.getDevice().getType().getId());
    assertEquals(TestPassportUtil.DEFAULT_ESN, context.getDevice().getEsn().getValue());
    assertEquals(1414010433L, context.getUser().getCurrentUser().getId());
    assertTrue(context.getUser().getCurrentUser().getCurrentMember());
  }

  @Ignore
  @Test
  public void returnMicroconfigFlagWhenMaskIsSupplied() {
    Map<String, StringList> headers = new HashMap<>();
    headers.put("x-netflix.client.type", StringList.newBuilder().addValues("argo").build());
    headers.put("x-netflix.client.appversion", StringList.newBuilder().addValues("14.14").build());

    GetContextRequest request =
        GetContextRequest.newBuilder()
            .putAllHeaders(headers)
            .setIpAddress("*************")
            .setPassport(TestPassportUtil.generateTestPassport().getPassportAsString())
            .setMask(
                FieldMask.newBuilder()
                    .addPaths("microconfig.discovery_flags.native_games_enabled")
                    .build())
            .build();
    Context context = blockingStub.getContext(request).getContext();
    System.out.println("context: " + context);
  }

  private netflix.context.Context.Builder buildAndroidContext() {
    CurrentRequestContext.get().setAppName("foo");
    DeviceContext dc =
        DeviceContext.newBuilder().setType(DeviceType.newBuilder().setId(1725).build()).build();
    Builder uc = UserContext.newBuilder();

    uc.setCurrentUser(
        User.newBuilder()
            .setIsTester(false)
            .setExperienceType(com.netflix.subscriber.types.protogen.Experience.Type.regular));
    ClientContext cc =
        ClientContext.newBuilder()
            .setAppVersion(Version.newBuilder().setVersion("8.88.0"))
            .setOsVersion(Version.newBuilder().setVersion("29"))
            .setClientCategory(ClientCategory.ANDROID)
            .setClientFlavor(ClientFlavor.ANDROID)
            .setClientFormFactor(ClientFormFactor.PHONE)
            .build();
    return Context.newBuilder()
        .setDevice(dc)
        .setClient(cc)
        .setUser(uc.build())
        .setCountry(Models.country("US"));
  }
}
