package com.netflix.microcontext.util;

import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.passport.test.TestDeviceInfo;
import com.netflix.passport.test.TestPassport;
import com.netflix.passport.test.TestUserInfo;

public class TestPassportUtil {

  public static final String DEFAULT_ESN = "NFCDCH-MC-CLPUUGD8CL8FJYC4KTGQ11U4L0EWA5";
  public static final Integer DEFAULT_DEVICE_TYPE_ID = 2145;
  public static final Long DEFAULT_CID = 1414010433L;
  public static final Long DEFAULT_ACCOUNT_OWNER_ID = 1414010433L;
  public static final String DEFAULT_VDID = "1234";
  public static final String DEFAULT_GUID = "foo-bar-guid";

  public static PassportIdentity generateTestPassport() {
    return TestPassport.builder()
        .deviceInfo(
            TestDeviceInfo.builder()
                .esn(DEFAULT_ESN)
                .source(com.netflix.passport.protobuf.Source.MSL_WEAK_AUTHENTICATION)
                .deviceTypeId(DEFAULT_DEVICE_TYPE_ID)
                .build())
        .userInfo(
            TestUserInfo.builder()
                .customerId(DEFAULT_CID)
                .visitorDeviceId(DEFAULT_VDID)
                .customerGuid(DEFAULT_GUID)
                .accountOwnerId(DEFAULT_ACCOUNT_OWNER_ID)
                .build())
        .build()
        .toPassportIdentity();
  }
}
