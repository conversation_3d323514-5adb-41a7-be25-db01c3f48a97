package com.netflix.microcontext.resolvers;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNotSame;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.netflix.archaius.DefaultPropertyFactory;
import com.netflix.archaius.api.PropertyRepository;
import com.netflix.archaius.config.EmptyConfig;
import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.init.resolvers.AuthResolvers;
import com.netflix.microcontext.protogen.GetContextRequest;
import com.netflix.microcontext.resolver.user.UserResolver;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.passport.introspect.PassportIdentityFactory;
import com.netflix.passport.test.TestDeviceInfo;
import com.netflix.passport.test.TestPassport;
import com.netflix.passport.test.TestUserInfo;
import com.netflix.spectator.api.NoopRegistry;
import java.util.concurrent.ExecutionException;
import netflix.context.Context;
import netflix.context.Context.Builder;
import netflix.context.auth.AuthContext;
import netflix.context.device.DeviceContext;
import netflix.context.visit.AppState;
import netflix.context.visit.Connection;
import netflix.context.visit.RequestPriority;
import netflix.context.visit.VisitContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MicroResolverTest {

  @Mock DeviceResolver deviceResolver;
  @Mock UserResolver userResolver;
  PropertyRepository propertyRepository = DefaultPropertyFactory.from(EmptyConfig.INSTANCE);
  PassportIdentityFactory passportIdentityFactory = new PassportIdentityFactory(new NoopRegistry());

  @Before
  public void before() {
    BindingContexts.push();
  }

  @After
  public void after() {
    BindingContexts.pop();
  }

  @Test
  public void resolveEmptyWithPriority() throws ExecutionException, InterruptedException {
    CurrentMicrocontext.set(
        Context.newBuilder()
            .setVisit(
                VisitContext.newBuilder()
                    .setConnection(Connection.newBuilder().setSecurePort(true).build())
                    .setPriority(
                        RequestPriority.newBuilder()
                            .setPriority(50)
                            .setAppState(AppState.APP_STATE_BACKGROUND)))
            .build());
    when(deviceResolver.resolve(any(), any(), any(), any()))
        .thenAnswer(invocation -> invocation.getArgument(0));
    MicroResolver microResolver =
        new MicroResolver(
            deviceResolver, userResolver, propertyRepository, passportIdentityFactory);
    Context context =
        microResolver.resolve(GetContextRequest.newBuilder().build()).toCompletableFuture().get();
    assertNotNull(context);
    final RequestPriority priority = context.getVisit().getPriority();
    assertNotSame(priority, RequestPriority.getDefaultInstance());
    assertEquals(50, priority.getPriority());
    assertEquals(AppState.APP_STATE_BACKGROUND, priority.getAppState());
    // connection will get overwritten until we support using the visit from the inbound request
    assertFalse(context.getVisit().hasConnection());
  }

  @Test
  public void resolveEmpty() throws ExecutionException, InterruptedException {
    when(deviceResolver.resolve(any(), any(), any(), any()))
        .thenAnswer(invocation -> invocation.getArgument(0));
    MicroResolver microResolver =
        new MicroResolver(
            deviceResolver, userResolver, propertyRepository, passportIdentityFactory);
    Context context =
        microResolver.resolve(GetContextRequest.newBuilder().build()).toCompletableFuture().get();
    assertNotNull(context);
    assertEquals("US", context.getCountry().getId());
    assertEquals(1, context.getLocalesCount());
    assertEquals("en-US", context.getLocales(0).getId());
  }

  @Test
  public void resolveUser() throws ExecutionException, InterruptedException {
    final TestPassport pass =
        TestPassport.builder()
            .userInfo(
                TestUserInfo.builder()
                    .customerId(1234L)
                    .customerGuid("foo")
                    .accountOwnerId(456L)
                    .accountOwnerGuid("bar")
                    .visitorDeviceId("vdid-foo")
                    .build())
            .deviceInfo(TestDeviceInfo.builder().esn("mesn").deviceTypeId(888).build())
            .build();
    final PassportIdentity passport = pass.toPassportIdentity();
    final AuthContext auth = AuthResolvers.convert(passport);
    when(deviceResolver.resolve(any(), any(), any(), any()))
        .thenAnswer(
            invocation -> {
              Builder builder = invocation.getArgument(0, Builder.class);
              builder.setDevice(DeviceContext.newBuilder().setEsn(auth.getEsn()).build());
              return builder;
            });
    MicroResolver microResolver =
        new MicroResolver(
            deviceResolver, userResolver, propertyRepository, passportIdentityFactory);
    Context context =
        microResolver
            .resolve(
                GetContextRequest.newBuilder().setPassport(pass.toPassportSerialized()).build())
            .toCompletableFuture()
            .get();
    DeviceContext device = context.getDevice();
    assertNotSame(DeviceContext.getDefaultInstance(), device);
    assertEquals(pass.deviceInfo.getEsn(), device.getBoxedEsn());
  }

  @Test
  public void resolveVisitor() throws ExecutionException, InterruptedException {
    final TestPassport pass =
        TestPassport.builder()
            .userInfo(TestUserInfo.builder().visitorDeviceId("vdid-foo").build())
            .deviceInfo(TestDeviceInfo.builder().esn("mesn").deviceTypeId(888).build())
            .build();
    AuthContext auth = AuthResolvers.convert(pass.toPassportIdentity());
    when(deviceResolver.resolve(any(), any(), any(), any()))
        .thenAnswer(
            invocation -> {
              Builder builder = invocation.getArgument(0, Builder.class);
              builder.setDevice(DeviceContext.newBuilder().setEsn(auth.getEsn()).build());
              return builder;
            });
    MicroResolver microResolver =
        new MicroResolver(
            deviceResolver, userResolver, propertyRepository, passportIdentityFactory);
    Context context =
        microResolver
            .resolve(
                GetContextRequest.newBuilder().setPassport(pass.toPassportSerialized()).build())
            .toCompletableFuture()
            .get();
    DeviceContext device = context.getDevice();
    assertNotSame(DeviceContext.getDefaultInstance(), device);
    assertEquals(pass.deviceInfo.getEsn(), device.getBoxedEsn());
  }
}
