apply plugin: 'netflix.spring-boot-netflix-application'
apply plugin: 'nebula.facet'

configurations.all {
    exclude group: 'ch.qos.logback', module: 'logback-classic'
    exclude group: 'ch.qos.logback', module: 'logback-core'
    exclude group: 'org.apache.logging.log4j', module: 'log4j-to-slf4j'
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
}

// Set up for the smoke test so we can execute ./gradlew smokeTest
facets {
    smokeTest {
        parentSourceSet = 'test'
    }
}
dependencies {
    implementation 'com.netflix.spring:spring-boot-netflix-starter-rest-server'
    implementation 'com.netflix.spring:spring-boot-netflix-starter-niws'
    smokeTestImplementation 'netflix:basicTypes'
    implementation 'com.netflix.spring:spring-boot-netflix-starter-grpc-server'
    implementation 'com.netflix.spring:spring-boot-netflix-starter-security-authn-embedded'
    implementation 'com.netflix.spring:spring-boot-netflix-starter-grpc-client'
    implementation project(':microcontext-proto-definition')
    implementation project(':microcontext-resolver')
    implementation project(':microcontext-init')
    implementation 'com.netflix.spring:spring-boot-netflix-starter-realtime-events'

    implementation "netflix.async.util:async-util:latest.release"
    implementation "netflix:passport:latest.release"
    implementation "netflix:basicTypes-proto:latest.release"
    implementation "netflix:basicTypes-proto-bridge:latest.release"
    implementation "com.netflix.dcms:dcms-rt-client:latest.release"
    implementation 'netflix:dts-standalone-common:latest.release'
    implementation 'netflix:server-context'

    testImplementation project(':microcontext-client')
    testImplementation project(':microcontext-test')
    testImplementation "com.netflix.spring:spring-boot-netflix-starter-test"
    testImplementation "com.netflix.passport.test:passport-test-core:latest.release"
}

// Define main class to be used in executable jar
mainClassName = 'com.netflix.microcontext.Microcontext'

// Spinnaker application name. Effects the name/path of startup scripts.
applicationName = 'microcontext'

// Print out full stack traces when our tests fail to assist debugging (e.g., when scanning Jenkins console output)
tasks.withType(Test).configureEach {
    useJUnitPlatform()
    testLogging {
        // set options for log level LIFECYCLE
        events "passed", "skipped", "failed", "standardOut"
        showExceptions true
        exceptionFormat "short"
        showCauses true
        showStackTraces true
        showStandardStreams true

        // set options for log level DEBUG and INFO
        debug {
            events "started", "passed", "skipped", "failed", "standardOut", "standardError"
            exceptionFormat "short"
        }
        info.events = debug.events
        info.exceptionFormat = debug.exceptionFormat
    }
}


// Configure Gradle Toolchains to use proper JDK
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

ospackage {
    requires('grpcuid')
}


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring:spring-boot-netflix-bom:latest.release'
}
