apply plugin: 'netflix.jvm-library'

dependencies {
    api project(":microcontext-access")
    api "com.netflix.passport.test:passport-test-core:latest.release"
    implementation "netflix:server-context"
    implementation 'junit:junit'
    testImplementation("junit:junit")
    testRuntimeOnly("org.junit.vintage:junit-vintage-engine")
}

tasks.named('test', Test).configure {
    useJUnitPlatform()
}


dependencyRecommendations {
    mavenBom module: 'com.netflix.spring.bom:spring-boot-netflix-internalplatform-recommendations:latest.release'
}
