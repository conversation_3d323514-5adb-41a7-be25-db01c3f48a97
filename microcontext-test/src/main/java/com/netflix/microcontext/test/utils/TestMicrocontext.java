package com.netflix.microcontext.test.utils;

import static com.netflix.lang.BindingContexts.pop;
import static com.netflix.lang.BindingContexts.push;
import static com.netflix.microcontext.test.MicrocontextTestResolvers.CLIENT;
import static com.netflix.microcontext.test.MicrocontextTestResolvers.DEVICE;
import static com.netflix.microcontext.test.MicrocontextTestResolvers.GEO_US;
import static com.netflix.microcontext.test.MicrocontextTestResolvers.VISIT;

import com.netflix.microcontext.access.server.ContextUtils;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.init.requestcontext.ContextContextSerializer;
import com.netflix.microcontext.init.resolvers.AuthResolvers;
import com.netflix.microcontext.init.resolvers.GeoResolvers;
import com.netflix.microcontext.init.serializers.Serializers;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.passport.test.TestPassport;
import com.netflix.server.context.ContextSerializationException;
import com.netflix.server.context.RequestContext;
import com.netflix.type.protogen.BasicTypes.Locale;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.function.BiConsumer;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.Context;
import netflix.context.auth.AuthContext;
import netflix.context.geo.GeoContext;
import netflix.context.user.UserContext;

public class TestMicrocontext {

  private final String passport;
  private final GeoContext geoContext;
  private final UserContext userContext;
  private final boolean propagateUser;
  private final Context context;

  public static TestMicrocontext of(Context context) {
    return new TestMicrocontext(
        null, null, null, context == null ? null : context.toBuilder(), null, true, true);
  }

  public static TestMicrocontext of(Context.Builder builder) {
    return new TestMicrocontext(null, null, null, builder, null, true, true);
  }

  @SuppressWarnings("deprecation")
  private TestMicrocontext(
      final @Nullable AuthContext authContext,
      final @Nullable GeoContext geoContext,
      final @Nullable UserContext userContext,
      final @Nullable Context.Builder context,
      final @Nullable String passport,
      final boolean propagateUser,
      final boolean clearUser) {
    this.propagateUser = propagateUser;
    boolean useBuilder = false;
    final Context.Builder builder;
    if (context == null) {
      builder = Context.newBuilder();
    } else {
      useBuilder = true;
      builder = context;
    }
    this.passport =
        passport != null ? passport : TestAuthUtil.toPassportString(authContext).orElse(null);
    GeoContext geo = null;
    if (geoContext != null) {
      geo = geoContext;
    } else if (builder.hasGeo()) {
      useBuilder = true;
      geo = builder.getGeo();
      builder.clearGeo();
    }
    this.geoContext = geo;

    UserContext user = null;
    if (userContext != null) {
      user = userContext;
    } else if (builder.hasUser()) {
      useBuilder = true;
      user = builder.getUser();
      if (clearUser) {
        builder.clearUser();
      }
    }
    this.userContext = user;

    if (authContext != null) {
      if (authContext.hasEsn()) {
        useBuilder = true;
        builder.getDeviceBuilder().setEsn(authContext.getEsn());
      }
      if (authContext.hasDeviceType()) {
        useBuilder = true;
        builder.getDeviceBuilder().setType(authContext.getDeviceType());
      }
    }
    this.context = useBuilder ? builder.build() : Context.getDefaultInstance();
  }

  public String getPassport() {
    return passport;
  }

  public AuthContext getAuthContext() {
    return AuthResolvers.decodePassport(passport).orElse(null);
  }

  @Nullable
  public GeoContext getGeoContext() {
    return geoContext;
  }

  @Nullable
  public UserContext getUserContext() {
    return userContext;
  }

  public boolean isPropagateUser() {
    return propagateUser;
  }

  @Nonnull
  public Context getContext() {
    return context;
  }

  public static Builder builder() {
    return new Builder();
  }

  public static Builder builder(@Nonnull Context context) {
    return new Builder(context);
  }

  public static Builder builder(@Nonnull Context.Builder context) {
    return new Builder(context.build());
  }

  public static Builder basic() {
    final UUID requestId = UUID.randomUUID();
    return new Builder(
            Context.newBuilder()
                .setClient(CLIENT)
                .setCountry(GEO_US.getCountry())
                .setDevice(DEVICE)
                .setRequestId(requestId.toString())
                .setVisit(VISIT)
                .addLocales(Locale.newBuilder().setId("en-US").build())
                .build())
        .setGeo(GEO_US);
  }

  public void makeCurrent() {
    TestCurrentMicrocontext.set(this);
  }

  /**
   * @return A string value of the microcontext if present
   */
  public Optional<String> getSerializedMicrocontext() {
    if (context != null && ContextUtils.isNotDefault(context)) {
      return Optional.of(context).map(Serializers::toString);
    }
    return Optional.empty();
  }

  public void forEachHeader(BiConsumer<String, String> consumer) {
    final RequestContext requestContext = new RequestContext((String) null);
    if (geoContext != null) {
      GeoResolvers.setGeo(geoContext, requestContext);
    }
    if (passport != null) {
      AuthResolvers.setPassport(passport, requestContext);
    }
    if (context != null && ContextUtils.isNotDefault(context)) {
      requestContext.addContext(
          CurrentMicrocontext.CONTEXT_KEY, context, ContextContextSerializer.INSTANCE);
    }
    try {
      requestContext.forEachSerializedContext(consumer);
    } catch (ContextSerializationException ignore) {
    }
  }

  /**
   * Make a call to the callable with the scope of the Microcontext and return the result. If an
   * exception is thrown, it will be sneakily thrown.
   *
   * @param c the callable to call
   * @return the result of the callable
   * @param <V> the type of the result
   */
  public <V> V makeCall(Callable<V> c) {
    try {
      return call(c);
    } catch (Exception e) {
      return sneakyThrow(e);
    }
  }

  /**
   * Make a call to the callable with the scope of the Microcontext and return the result.
   *
   * @param c the callable to call
   * @return the result of the callable
   * @param <V> the type of the result
   * @throws Exception if an exception is thrown by the callable
   */
  public <V> V call(Callable<V> c) throws Exception {
    push();
    try {
      makeCurrent();
      return c.call();
    } finally {
      pop();
    }
  }

  /**
   * Run the runnable in the context of this Microcontext
   *
   * @param r the runnable to run
   */
  public void run(Runnable r) {
    push();
    try {
      makeCurrent();
      r.run();
    } finally {
      pop();
    }
  }

  @SuppressWarnings("unchecked")
  private static <T, E extends Throwable> T sneakyThrow(@Nonnull Throwable throwable) throws E {
    throw (E) throwable;
  }

  public Map<String, String> asHeaders() {
    Map<String, String> headers = new HashMap<>();
    forEachHeader(headers::put);
    return headers;
  }

  public static class Builder {

    private AuthContext authContext;
    private GeoContext geoContext;
    private UserContext userContext;
    private boolean propagateUser;
    private boolean clearUser = true;
    private Context context;
    private String passport;

    Builder() {}

    Builder(Context context) {
      this.context = context;
    }

    public Builder setAuth(AuthContext authContext) {
      this.authContext = authContext;
      return this;
    }

    /**
     * @deprecated use {@link #setAuth(AuthContext)}
     */
    @Deprecated
    public Builder setAuthContext(AuthContext authContext) {
      return setAuth(authContext);
    }

    public Builder setContext(Context context) {
      this.context = context;
      return this;
    }

    public Builder setGeo(GeoContext geoContext) {
      this.geoContext = geoContext;
      return this;
    }

    public Builder setUser(UserContext userContext) {
      this.userContext = userContext;
      return this;
    }

    public Builder setPropagateUser() {
      this.propagateUser = true;
      return this;
    }

    public Builder setClearUser(boolean clearUser) {
      this.clearUser = clearUser;
      return this;
    }

    public Builder setPassport(TestPassport testPassport) {
      this.authContext = AuthResolvers.convert(testPassport.toPassportIdentity());
      return this;
    }

    public Builder setPassport(String passport) {
      this.passport = passport;
      return this;
    }

    public Builder setPassport(PassportIdentity passportIdentity) {
      this.authContext = AuthResolvers.convert(passportIdentity);
      return this;
    }

    public Builder setPassportIdentity(PassportIdentity passportIdentity) {
      return setPassport(passportIdentity);
    }

    public TestMicrocontext build() {
      return new TestMicrocontext(
          this.authContext,
          this.geoContext,
          this.userContext,
          this.context == null ? null : this.context.toBuilder(),
          passport,
          propagateUser,
          clearUser);
    }

    public void makeCurrent() {
      TestCurrentMicrocontext.set(this);
    }

    public String toString() {
      return "TestMicrocontext.Builder(authContext="
          + this.authContext
          + ", geo="
          + this.geoContext
          + ", user="
          + this.userContext
          + ", context="
          + this.context
          + ")";
    }
  }
}
