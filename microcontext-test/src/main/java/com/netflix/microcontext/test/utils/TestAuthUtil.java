package com.netflix.microcontext.test.utils;

import com.netflix.microcontext.init.resolvers.AuthResolvers;
import com.netflix.passport.protobuf.Passport;
import com.netflix.passport.protobuf.Source;
import com.netflix.passport.test.TestDeviceInfo;
import com.netflix.passport.test.TestDeviceInfo.TestDeviceInfoBuilder;
import com.netflix.passport.test.TestPassport;
import com.netflix.passport.test.TestPassport.TestPassportBuilder;
import com.netflix.passport.test.TestUserInfo;
import com.netflix.passport.test.TestUserInfo.TestUserInfoBuilder;
import java.util.Optional;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.auth.Auth;
import netflix.context.auth.AuthContext;

public class TestAuthUtil {

  public static Optional<String> toPassportString(@Nullable final AuthContext authContext) {
    if (authContext == null) {
      return Optional.empty();
    }
    return toPassport(authContext).map(AuthResolvers::encodePassport);
  }

  public static Optional<Passport> toPassport(@Nonnull final AuthContext authContext) {
    if (authContext != AuthContext.getDefaultInstance()) {
      boolean initialized = false;
      final TestPassportBuilder passportBuilder = TestPassport.builder();
      if (authContext.hasEsn() || authContext.hasDeviceType()) {
        initialized = true;
        final TestDeviceInfoBuilder testDeviceInfoBuilder =
            TestDeviceInfo.builder().source(Source.MSL_WEAK_AUTHENTICATION);
        authContext.getOptionalEsn().ifPresent(testDeviceInfoBuilder::esn);
        if (authContext.hasDeviceType()) {
          testDeviceInfoBuilder.deviceTypeId(authContext.getDeviceType().getId());
        }
        if (authContext.hasAppId()) {
          testDeviceInfoBuilder.appId(authContext.getAppId().getValue());
        }
        passportBuilder.deviceInfo(testDeviceInfoBuilder.build());
      }

      if (authContext.hasCurrentAuth() || authContext.hasVisitorDeviceId()) {
        initialized = true;
        final Auth currentAuth = authContext.getCurrentAuth();
        final TestUserInfoBuilder testUserInfoBuilder =
            TestUserInfo.builder()
                .customerId(currentAuth.getCustomerId())
                .customerGuid(currentAuth.getCustomerGuid())
                .visitorDeviceId(authContext.getBoxedVisitorDeviceId());
        currentAuth.getOptionalAccountOwnerId().ifPresent(testUserInfoBuilder::accountOwnerId);
        currentAuth.getOptionalAccountOwnerGuid().ifPresent(testUserInfoBuilder::accountOwnerGuid);
        passportBuilder.userInfo(testUserInfoBuilder.build());
        if (authContext.hasVisitorDeviceId()) {
          testUserInfoBuilder.visitorDeviceId(authContext.getBoxedVisitorDeviceId());
        }
        if (authContext.hasAppId()) {
          passportBuilder.deviceInfo(
              TestDeviceInfo.builder().appId(authContext.getAppId().getValue()).build());
        }
      }
      return initialized ? Optional.of(passportBuilder.build().toPassport()) : Optional.empty();
    }
    return Optional.empty();
  }
}
