package com.netflix.microcontext.test;

import static com.netflix.microcontext.test.MicrocontextTestResolvers.CLIENT;
import static com.netflix.microcontext.test.MicrocontextTestResolvers.DEVICE;
import static com.netflix.microcontext.test.MicrocontextTestResolvers.GEO_US;
import static com.netflix.microcontext.test.MicrocontextTestResolvers.VISIT;

import com.netflix.microcontext.test.utils.TestMicrocontext;
import com.netflix.type.protogen.BasicTypes.Locale;
import java.util.UUID;
import netflix.context.Context;

/**
 * @deprecated use {@link TestMicrocontext#basic()}
 */
@Deprecated
public class MicrocontextTest {

  public static Context.Builder basic() {
    return _basic();
  }

  private static Context.Builder _basic() {
    final UUID requestId = UUID.randomUUID();
    return Context.newBuilder()
        .setClient(CLIENT)
        .setCountry(GEO_US.getCountry())
        .setDevice(DEVICE)
        .setGeo(GEO_US)
        .setRequestId(requestId.toString())
        .setVisit(VISIT)
        .addLocales(Locale.newBuilder().setId("en-US").build());
  }
}
