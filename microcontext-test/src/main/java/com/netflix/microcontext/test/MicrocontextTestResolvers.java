package com.netflix.microcontext.test;

import com.google.protobuf.util.Timestamps;
import com.netflix.dcms.models.protogen.DseClientPlatformName;
import com.netflix.dcms.models.protogen.DseHardwareMajorCategory;
import com.netflix.type.protogen.BasicTypes.Country;
import com.netflix.type.protogen.BasicTypes.DeviceType;
import netflix.context.auth.AuthContext;
import netflix.context.client.ClientContext;
import netflix.context.client.category.ClientCategory;
import netflix.context.client.flavor.ClientFlavor;
import netflix.context.common.Version;
import netflix.context.device.DeviceContext;
import netflix.context.geo.GeoContext;
import netflix.context.visit.VisitContext;

public class MicrocontextTestResolvers {

  public static final GeoContext GEO_US =
      GeoContext.newBuilder()
          .setCountry(Country.newBuilder().setId("US").build())
          .setRegionCode("CA")
          .setCity("LOSGATOS")
          .setIanaTimezone("US/Pacific")
          .setZip("95030")
          .setAsn("2906")
          .build();

  public static AuthContext AUTH =
      AuthContext.newBuilder()
          .setVisitorDeviceId("1234")
          .setEsn("NFCDCH-MC-CLPUUGD8CL8FJYC4KTGQ11U4L0EWA5")
          .setDeviceType(DeviceType.newBuilder().setId(2145).build())
          .setCurrentAuth(
              netflix.context.auth.Auth.newBuilder()
                  .setCustomerId(1414010433L)
                  .setCustomerGuid("foo-bar-guid")
                  .setAccountOwnerId(1414010433L)
                  .setAccountOwnerGuid("fiz-baz-guid"))
          .setAppId("foo-bar-app")
          .build();

  public static VisitContext VISIT =
      VisitContext.newBuilder()
          .setEdgeTime(Timestamps.fromMillis(System.currentTimeMillis()))
          .build();

  public static DeviceContext DEVICE =
      DeviceContext.newBuilder()
          .setHardwareMajorCategory(DseHardwareMajorCategory.LAPTOP_DESKTOP)
          .setClientPlatform(DseClientPlatformName.HTML5)
          .build();

  public static ClientContext CLIENT =
      ClientContext.newBuilder()
          .setClientCategory(ClientCategory.WEB)
          .setClientFlavor(ClientFlavor.AKIRA)
          .setAppVersion(Version.newBuilder().setVersion("12.31.0"))
          .setSdkVersion(Version.newBuilder().setVersion("2019.1.6.0-3918278e45"))
          .build();
}
