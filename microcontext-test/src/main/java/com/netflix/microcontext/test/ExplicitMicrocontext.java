package com.netflix.microcontext.test;

import com.netflix.passport.protobuf.Passport;
import netflix.context.Context;
import netflix.context.auth.AuthContext;

public class ExplicitMicrocontext extends MicrocontextTestRule {

  public ExplicitMicrocontext(Context context, AuthContext authContext) {
    super(context, authContext);
  }

  public ExplicitMicrocontext(Context context, Passport passport) {
    super(context, passport);
  }
}
