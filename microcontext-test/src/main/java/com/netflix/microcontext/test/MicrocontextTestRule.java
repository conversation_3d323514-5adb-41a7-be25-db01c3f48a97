package com.netflix.microcontext.test;

import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.init.resolvers.AuthResolvers;
import com.netflix.microcontext.test.utils.TestCurrentMicrocontext;
import com.netflix.microcontext.test.utils.TestMicrocontext;
import com.netflix.passport.protobuf.Passport;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.Context;
import netflix.context.auth.AuthContext;
import org.junit.rules.TestRule;
import org.junit.runner.Description;
import org.junit.runners.model.Statement;

public class MicrocontextTestRule implements TestRule {

  private final Context context;
  private final AuthContext authContext;

  protected MicrocontextTestRule(Context context, AuthContext authContext) {
    this.context = context;
    this.authContext = authContext;
  }

  protected MicrocontextTestRule(Context context, @Nullable Passport passport) {
    this.context = context;
    this.authContext = AuthResolvers.resolve(passport).orElse(null);
  }

  @Nonnull
  @Override
  public Statement apply(@Nonnull Statement base, @Nonnull Description description) {
    return new Statement() {
      @Override
      public void evaluate() {
        BindingContexts.runWithNewContext(
            () -> {
              try {
                TestCurrentMicrocontext.set(
                    TestMicrocontext.builder().setAuth(authContext).setContext(context).build());
                base.evaluate();
              } catch (Throwable e) {
                throw new RuntimeException(e);
              }
            });
      }
    };
  }
}
