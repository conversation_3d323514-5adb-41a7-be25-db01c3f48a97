package com.netflix.microcontext.test.interceptor;

import static io.grpc.Metadata.ASCII_STRING_MARSHALLER;

import com.netflix.microcontext.test.utils.TestMicrocontext;
import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientInterceptor;
import io.grpc.ForwardingClientCall;
import io.grpc.Metadata;
import io.grpc.Metadata.Key;
import io.grpc.MethodDescriptor;
import javax.annotation.Nonnull;
import netflix.context.Context;

public class TestMicrocontextInterceptor implements ClientInterceptor {

  private final TestMicrocontext testMicrocontext;

  public static TestMicrocontextInterceptor of(@Nonnull TestMicrocontext context) {
    return new TestMicrocontextInterceptor(context);
  }

  public static ClientInterceptor of(Context context) {
    return new TestMicrocontextInterceptor(TestMicrocontext.of(context));
  }

  private TestMicrocontextInterceptor(@Nonnull TestMicrocontext testMicrocontext) {
    this.testMicrocontext = testMicrocontext;
  }

  @Override
  public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(
      MethodDescriptor<ReqT, RespT> methodDescriptor, CallOptions callOptions, Channel channel) {
    return new ForwardingClientCall.SimpleForwardingClientCall<ReqT, RespT>(
        channel.newCall(methodDescriptor, callOptions)) {
      @Override
      public void start(Listener<RespT> r, Metadata m) {
        testMicrocontext.forEachHeader((k, v) -> m.put(Key.of(k, ASCII_STRING_MARSHALLER), v));
        super.start(r, m);
      }
    };
  }
}
