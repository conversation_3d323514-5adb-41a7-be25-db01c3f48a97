package com.netflix.microcontext.test.utils;

import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.access.server.CurrentContextCache;
import com.netflix.microcontext.access.server.MicrocontextInternalUtils;
import com.netflix.microcontext.init.resolvers.AuthResolvers;
import com.netflix.microcontext.init.resolvers.GeoResolvers;
import com.netflix.microcontext.init.resolvers.UserResolvers;
import com.netflix.microcontext.resolver.user.UserConverter;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.spectator.api.Spectator;
import com.netflix.type.protogen.BasicTypes.Country;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import netflix.context.Context;
import netflix.context.Context.Builder;
import netflix.context.geo.GeoContext;
import netflix.context.user.UserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TestCurrentMicrocontext {

  private static final Logger logger = LoggerFactory.getLogger(TestCurrentMicrocontext.class);

  /**
   * Converts {@link TestMicrocontext} into the equivalent local request state. Must be called
   * within a {@link com.netflix.lang.ThreadContext} scope or will throw an error
   *
   * @param testMicrocontext The input for what to store, create using {@link
   *     TestMicrocontext#builder()}
   */
  public static void set(@Nonnull TestMicrocontext testMicrocontext) {
    setSafe(testMicrocontext);
  }

  /**
   * Similar to {@link #set(TestMicrocontext)} but returns a boolean indicating if the operation
   * succeeded or not
   */
  public static boolean setSafe(@Nonnull TestMicrocontext testMicrocontext) {
    return set(
        testMicrocontext.getContext(),
        testMicrocontext.getPassport(),
        testMicrocontext.getGeoContext(),
        testMicrocontext.getUserContext(),
        testMicrocontext.isPropagateUser());
  }

  /**
   * Converts {@link TestMicrocontext} into the equivalent local request state. Must be called
   * within a {@link com.netflix.lang.ThreadContext} scope or will throw an error
   *
   * @param builder The input for what to store, create using {@link TestMicrocontext#builder()}
   */
  public static void set(@Nonnull TestMicrocontext.Builder builder) {
    setSafe(builder.build());
  }

  /**
   * Simple setter for only adding Context to the local request state. Must be called within a
   * {@link com.netflix.lang.ThreadContext} scope or will throw an error
   *
   * @param context The input for what to store
   */
  public static void set(@Nonnull Context context) {
    set(TestMicrocontext.builder(context).build());
  }

  @SuppressWarnings("deprecation")
  private static boolean set(
      @Nonnull Context context,
      @Nullable String passport,
      @Nullable GeoContext geoContext,
      @Nullable UserContext userContext,
      boolean propagateUser) {
    if (BindingContexts.isInContext()) {
      final RequestContext requestContext = CurrentRequestContext.get();
      final Builder builder = context.toBuilder();
      if (passport != null) {
        AuthResolvers.setPassport(passport, requestContext);
      } else {
        AuthResolvers.clearPassport();
      }
      Country country = null;
      if (geoContext == null && context.hasGeo()) {
        Spectator.globalRegistry().counter("microcontext.test.geo.context").increment();
        geoContext = context.getGeo();
      }
      if (geoContext != null) {
        if (geoContext.hasCountry()) {
          country = geoContext.getCountry();
        }
        // set blocked proxy if not already set in the input
        if (!builder.getVideoBuilder().hasBlockedProxy() && geoContext.hasBlockedProxy()) {
          builder.getVideoBuilder().setBlockedProxy(geoContext.getBlockedProxy());
        }
        GeoResolvers.setGeo(geoContext, requestContext);
      }
      if (userContext != null && userContext.hasCurrentUser()) {
        if (propagateUser) {
          UserResolvers.setUser(userContext, requestContext);
        }
        CurrentContextCache.get().setUserContext(UserConverter.updateUser(userContext));
      }
      if (country != null) {
        // update context country
        builder.setCountry(country);
      }
      return MicrocontextInternalUtils.internalSet(builder.build(), requestContext);
    } else {
      logger.error("Trying to set Microcontext outside of BindingContext", new Exception());
      return false;
    }
  }
}
