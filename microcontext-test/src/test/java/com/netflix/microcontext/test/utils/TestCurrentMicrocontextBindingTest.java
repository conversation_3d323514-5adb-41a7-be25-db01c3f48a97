package com.netflix.microcontext.test.utils;

import netflix.context.Context;
import org.junit.Assert;
import org.junit.Test;

public class TestCurrentMicrocontextBindingTest {
  @Test
  public void builderEmpty() {
    Assert.assertFalse(
        "Should fail due to not being in a BindingContext",
        TestCurrentMicrocontext.setSafe(
            TestMicrocontext.builder(Context.newBuilder().setRequestId("foo").build()).build()));
  }
}
