package com.netflix.microcontext.test;

import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import java.util.Optional;
import netflix.context.auth.AuthContext;
import org.junit.Assert;
import org.junit.Rule;
import org.junit.Test;

public class MicrocontextOverrideRuleTest {

  @Rule
  public MicrocontextTestRule microcontextRule =
      new ExplicitMicrocontext(
          MicrocontextTest.basic().setRequestId("fooo").build(), AuthContext.getDefaultInstance());

  @Test
  public void basic() {
    final Optional<Microcontext> raw = CurrentMicrocontext.getRaw();
    Assert.assertTrue(raw.isPresent());
    final Microcontext microcontext = raw.get();
    Assert.assertEquals("fooo", microcontext.getRequestId());
    Assert.assertFalse(microcontext.getAuth().hasCurrentAuth());
  }
}
