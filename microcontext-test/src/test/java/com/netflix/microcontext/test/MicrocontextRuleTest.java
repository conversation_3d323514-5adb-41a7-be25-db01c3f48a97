package com.netflix.microcontext.test;

import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import java.util.Optional;
import netflix.context.Context;
import org.junit.Assert;
import org.junit.Rule;
import org.junit.Test;

public class MicrocontextRuleTest {

  @Rule public MicrocontextTestRule microcontextRule = new BasicMicrocontext();

  @Test
  public void basic() {
    final Optional<Microcontext> raw = CurrentMicrocontext.getRaw();
    Assert.assertTrue(raw.isPresent());
    final Microcontext microcontext = raw.get();
    Assert.assertEquals("US", microcontext.getCountry().getId());

    final Context.Builder basic = MicrocontextTest.basic();
    System.out.println("Default microcontext " + basic.build());
  }
}
