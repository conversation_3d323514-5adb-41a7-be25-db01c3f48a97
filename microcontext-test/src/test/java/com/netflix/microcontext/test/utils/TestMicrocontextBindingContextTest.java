package com.netflix.microcontext.test.utils;

import static org.junit.Assert.assertEquals;

import com.netflix.microcontext.access.server.CurrentMicrocontext;
import netflix.context.Context;
import org.junit.Assert;
import org.junit.Test;

public class TestMicrocontextBindingContextTest {
  @Test
  public void builder() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.of(Context.newBuilder().setRequestId("123"));
    Assert.assertNull(
        testMicrocontext.makeCall(
            () -> {
              assertEquals("123", CurrentMicrocontext.get().getRequestId());
              return null;
            }));
  }
}
