package com.netflix.microcontext.test.utils;

import static org.junit.Assert.*;

import com.netflix.lang.BindingContexts;
import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.init.resolvers.UserResolvers;
import com.netflix.passport.test.TestPassport;
import com.netflix.passport.test.TestUserInfo;
import com.netflix.type.protogen.BasicTypes.Country;
import java.util.Optional;
import netflix.context.Context;
import netflix.context.auth.Auth;
import netflix.context.auth.AuthContext;
import netflix.context.geo.GeoContext;
import netflix.context.user.User;
import netflix.context.user.UserContext;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class TestCurrentMicrocontextTest {

  @Before
  public void before() {
    BindingContexts.push();
  }

  @After
  public void after() {
    BindingContexts.pop();
  }

  @Test
  public void builderEmpty() {
    final TestMicrocontext testMicrocontext = TestMicrocontext.builder().build();
    TestCurrentMicrocontext.set(testMicrocontext);
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertNull(microcontext.getRequestId());
    final AuthContext auth = microcontext.getAuth();
    assertNotNull(auth);
    assertFalse(auth.hasCurrentAuth());
  }

  @Test
  public void builderNoAuth() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.of(Context.newBuilder().setRequestId("123").build());
    TestCurrentMicrocontext.set(testMicrocontext);
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
    final AuthContext auth = microcontext.getAuth();
    assertNotNull(auth);
    assertFalse(auth.hasCurrentAuth());
  }

  @Test
  public void builder() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.of(Context.newBuilder().setRequestId("123"));
    TestCurrentMicrocontext.set(testMicrocontext);
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
  }

  @Test
  public void simpleSet() {
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder(Context.newBuilder().setRequestId("123").build()));
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
  }

  @Test
  public void simpleSetViaBuilder() {
    TestMicrocontext.builder(Context.newBuilder().setRequestId("123").build()).makeCurrent();
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
  }

  @Test
  public void simpleSetViaTest() {
    TestMicrocontext.builder(Context.newBuilder().setRequestId("123").build())
        .build()
        .makeCurrent();
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
  }

  @Test
  public void builderAuth() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder()
            .setContext(Context.newBuilder().setRequestId("123").build())
            .setAuth(
                AuthContext.newBuilder()
                    .setCurrentAuth(Auth.newBuilder().setCustomerId(456L))
                    .setEsn("foo-esn")
                    .build())
            .build();
    TestCurrentMicrocontext.set(testMicrocontext);
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
    final AuthContext auth = microcontext.getAuth();
    assertNotNull(auth);
    assertTrue(auth.hasCurrentAuth());
    assertEquals(456L, auth.getCurrentAuth().getCustomerId());
  }

  @Test
  public void builderAuthDevice() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder()
            .setContext(Context.newBuilder().setRequestId("123").build())
            .setAuth(AuthContext.newBuilder().setEsn("foo-esn").build())
            .build();
    TestCurrentMicrocontext.set(testMicrocontext);
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
    final AuthContext auth = microcontext.getAuth();
    assertNotNull(auth);
    assertEquals("foo-esn", auth.getBoxedEsn());
    assertEquals("foo-esn", microcontext.getEsn().orElse(null));
  }

  @Test
  public void builderAuthDeviceDeprecated() {
    TestCurrentMicrocontext.set(
        TestMicrocontext.builder(Context.newBuilder().setRequestId("123").build())
            .setAuth(AuthContext.newBuilder().setEsn("foo-esn").build()));
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
    final AuthContext auth = microcontext.getAuth();
    assertNotNull(auth);
    assertEquals("foo-esn", auth.getBoxedEsn());
    assertEquals("foo-esn", microcontext.getEsn().orElse(null));
  }

  @Test
  public void builderPassport() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder()
            .setContext(Context.newBuilder().setRequestId("123").build())
            .setPassport(
                TestPassport.builder()
                    .userInfo(TestUserInfo.builder().customerId(456L).build())
                    .build())
            .build();
    TestCurrentMicrocontext.set(testMicrocontext);
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
    final AuthContext auth = microcontext.getAuth();
    assertNotNull(auth);
    assertTrue(auth.hasCurrentAuth());
    assertEquals(456L, auth.getCurrentAuth().getCustomerId());
  }

  @Test
  public void builderPassportIdentity() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder()
            .setContext(Context.newBuilder().setRequestId("123").build())
            .setPassportIdentity(
                TestPassport.builder()
                    .userInfo(TestUserInfo.builder().customerId(456L).build())
                    .build()
                    .toPassportIdentity())
            .build();
    TestCurrentMicrocontext.set(testMicrocontext);
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
    final AuthContext auth = microcontext.getAuth();
    assertNotNull(auth);
    assertTrue(auth.hasCurrentAuth());
    assertEquals(456L, auth.getCurrentAuth().getCustomerId());
  }

  @Test
  public void getAuthContext() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder()
            .setPassportIdentity(
                TestPassport.builder()
                    .userInfo(TestUserInfo.builder().customerId(456L).build())
                    .build()
                    .toPassportIdentity())
            .build();
    final AuthContext auth = testMicrocontext.getAuthContext();
    assertNotNull(auth);
    assertTrue(auth.hasCurrentAuth());
    assertEquals(456L, auth.getCurrentAuth().getCustomerId());
  }

  @Test
  public void getAuthContextEmpty() {
    final TestMicrocontext testMicrocontext = TestMicrocontext.builder().build();
    final AuthContext auth = testMicrocontext.getAuthContext();
    assertNull(auth);
  }

  @Test
  public void builderGeo() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder()
            .setContext(Context.newBuilder().setRequestId("123").build())
            .setAuth(
                AuthContext.newBuilder()
                    .setCurrentAuth(
                        Auth.newBuilder()
                            .setCustomerId(123L)
                            .setCustomerGuid("some globally unique id"))
                    .build())
            .setGeo(
                GeoContext.newBuilder()
                    .setBlockedProxy(true)
                    .setCountry(Country.newBuilder().setId("CA"))
                    .build())
            .build();
    TestCurrentMicrocontext.set(testMicrocontext);
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
    final GeoContext geo = microcontext.getGeo();
    assertNotNull(geo);
    assertEquals("CA", geo.getCountry().getId());
    assertEquals("CA", microcontext.getCountry().getId());
    assertEquals("CA", microcontext.toProto().getCountry().getId());
    assertTrue(geo.getBlockedProxy());
    assertTrue(microcontext.getVideo().getBlockedProxy());
    assertFalse(microcontext.toProto().hasGeo());
  }

  @Test
  public void builderGeoInContext() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder()
            .setContext(
                Context.newBuilder()
                    .setRequestId("123")
                    .setGeo(
                        GeoContext.newBuilder()
                            .setCountry(Country.newBuilder().setId("CA"))
                            .build())
                    .build())
            .build();
    assertFalse(testMicrocontext.getContext().hasGeo());
    assertNotNull(testMicrocontext.getGeoContext());
    TestCurrentMicrocontext.set(testMicrocontext);
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
    final GeoContext geo = microcontext.getGeo();
    assertNotNull(geo);
    assertEquals("CA", geo.getCountry().getId());
    assertEquals("CA", microcontext.getCountry().getId());
    assertEquals("CA", microcontext.toProto().getCountry().getId());
    assertFalse(microcontext.toProto().hasGeo());
  }

  @Test
  public void builderUser() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder()
            .setContext(Context.newBuilder().setRequestId("123").build())
            .setUser(UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(456L)).build())
            .setPropagateUser()
            .build();
    TestCurrentMicrocontext.set(testMicrocontext);
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
    final UserContext user = microcontext.getUser();
    assertNotNull(user);
    assertTrue(user.hasCurrentUser());
    assertEquals(456L, user.getCurrentUser().getId());
    final Optional<UserContext> userContext = UserResolvers.requestContext();
    assertTrue(userContext.isPresent());
    assertTrue(user.hasCurrentUser());
    assertEquals(456L, userContext.get().getCurrentUser().getId());
  }

  @Test
  public void builderUserLocal() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder()
            .setContext(Context.newBuilder().setRequestId("123").build())
            .setUser(UserContext.newBuilder().setCurrentUser(User.newBuilder().setId(456L)).build())
            .build();
    TestCurrentMicrocontext.set(testMicrocontext);
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertEquals("123", microcontext.getRequestId());
    final UserContext user = microcontext.getUser();
    assertNotNull(user);
    assertTrue(user.hasCurrentUser());
    assertEquals(456L, user.getCurrentUser().getId());
    final Optional<UserContext> userContext = UserResolvers.requestContext();
    assertFalse(userContext.isPresent());
  }
}
