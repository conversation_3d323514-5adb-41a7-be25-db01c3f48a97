package com.netflix.microcontext.test.utils;

import static org.junit.Assert.*;

import com.netflix.microcontext.init.resolvers.AuthResolvers;
import com.netflix.microcontext.init.utils.Passports;
import com.netflix.passport.introspect.PassportIdentity;
import com.netflix.passport.introspect.PassportIdentityFactory;
import com.netflix.passport.introspect.PassportIntrospectionException;
import com.netflix.passport.protobuf.Passport;
import com.netflix.spectator.api.NoopRegistry;
import java.util.Optional;
import netflix.context.auth.Auth;
import netflix.context.auth.AuthContext;
import org.junit.Test;

public class TestAuthUtilTest {

  PassportIdentityFactory passportIdentityFactory = new PassportIdentityFactory(new NoopRegistry());

  @Test
  public void toPassportEmpty() {
    final Optional<Passport> passport = TestAuthUtil.toPassport(AuthContext.newBuilder().build());
    assertFalse(passport.isPresent());
  }

  @Test
  public void toPassportDefault() {
    final Optional<Passport> passport = TestAuthUtil.toPassport(AuthContext.getDefaultInstance());
    assertFalse(passport.isPresent());
  }

  @Test
  public void toPassportEsn() throws PassportIntrospectionException {
    final Optional<Passport> passport =
        TestAuthUtil.toPassport(AuthContext.newBuilder().setEsn("fooesn").build());
    assertTrue(passport.isPresent());
    final PassportIdentity passportIdentity =
        passportIdentityFactory.createPassportIdentity(passport.get());
    assertEquals("fooesn", passportIdentity.getEsn().orElse(null));
  }

  @Test
  public void toPassportDeviceTypeDefault() throws PassportIntrospectionException {
    final Optional<Passport> passport =
        TestAuthUtil.toPassport(
            AuthContext.newBuilder()
                .setCurrentAuth(Auth.newBuilder().setCustomerId(123L).setAccountOwnerId(456L))
                .setEsn("fooesn")
                .build());
    assertTrue(passport.isPresent());
    final Optional<PassportIdentity> passportIdentity = Passports.passportIdentity(passport.get());
    assertTrue(passportIdentity.isPresent());
    final Optional<AuthContext> resolved = AuthResolvers.resolve(passportIdentity.get());
    assertTrue(resolved.isPresent());
    final AuthContext authContext = resolved.get();
    assertFalse(authContext.hasDeviceType());
  }

  @Test
  public void toPassportAccountOwnerDefault() throws PassportIntrospectionException {
    final Optional<Passport> passport =
        TestAuthUtil.toPassport(
            AuthContext.newBuilder()
                .setCurrentAuth(Auth.newBuilder().setCustomerId(123L))
                .setEsn("fooesn")
                .build());
    assertTrue(passport.isPresent());
    final Optional<PassportIdentity> passportIdentity = Passports.passportIdentity(passport.get());
    assertTrue(passportIdentity.isPresent());
    final Optional<AuthContext> resolved = AuthResolvers.resolve(passportIdentity.get());
    assertTrue(resolved.isPresent());
    final AuthContext authContext = resolved.get();
    assertTrue(authContext.hasCurrentAuth());
    final Auth currentAuth = authContext.getCurrentAuth();
    assertEquals(123L, currentAuth.getCustomerId());
    assertEquals("", currentAuth.getCustomerGuid());
    assertFalse(currentAuth.hasAccountOwnerId());
    assertFalse(currentAuth.hasAccountOwnerGuid());
  }

  @Test
  public void toPassportCidEsn() throws PassportIntrospectionException {
    final Optional<Passport> passport =
        TestAuthUtil.toPassport(
            AuthContext.newBuilder()
                .setCurrentAuth(Auth.newBuilder().setCustomerId(123L))
                .setEsn("fooesn")
                .build());
    assertTrue(passport.isPresent());
    final PassportIdentity passportIdentity =
        passportIdentityFactory.createPassportIdentity(passport.get());
    assertEquals("fooesn", passportIdentity.getEsn().orElse(null));
    assertTrue(passportIdentity.getProfileId().isPresent());
    assertEquals(123L, passportIdentity.getProfileId().get().longValue());
  }

  @Test
  public void testPassportString() {
    assertFalse(TestAuthUtil.toPassportString(null).isPresent());
    assertFalse(TestAuthUtil.toPassportString(AuthContext.getDefaultInstance()).isPresent());
    assertFalse(TestAuthUtil.toPassportString(AuthContext.newBuilder().build()).isPresent());
    final Optional<String> passportString =
        TestAuthUtil.toPassportString(AuthContext.newBuilder().setEsn("foo").build());
    assertTrue(passportString.isPresent());
    assertFalse(passportString.get().isEmpty());
  }

  @Test
  public void testVdid() throws PassportIntrospectionException {
    final Optional<Passport> passport =
        TestAuthUtil.toPassport(AuthContext.newBuilder().setVisitorDeviceId("foovdid").build());
    assertTrue(passport.isPresent());
    final PassportIdentity passportIdentity =
        passportIdentityFactory.createPassportIdentity(passport.get());
    assertEquals("foovdid", passportIdentity.getVisitorDeviceId().orElse(null));
  }

  @Test
  public void testVdidEsn() throws PassportIntrospectionException {
    final Optional<Passport> passport =
        TestAuthUtil.toPassport(
            AuthContext.newBuilder().setVisitorDeviceId("foovdid").setEsn("fooesn").build());
    assertTrue(passport.isPresent());
    final PassportIdentity passportIdentity =
        passportIdentityFactory.createPassportIdentity(passport.get());
    assertEquals("foovdid", passportIdentity.getVisitorDeviceId().orElse(null));
    assertEquals("fooesn", passportIdentity.getEsn().orElse(null));
  }
}
