package com.netflix.microcontext.test.utils;

import static org.junit.Assert.*;

import com.netflix.microcontext.access.Microcontext;
import com.netflix.microcontext.access.server.CurrentMicrocontext;
import com.netflix.microcontext.init.serializers.Serializers;
import com.netflix.microcontext.test.MicrocontextTestResolvers;
import com.netflix.passport.test.TestPassport;
import com.netflix.passport.test.TestUserInfo;
import com.netflix.server.context.CurrentRequestContext;
import com.netflix.server.context.RequestContext;
import com.netflix.server.context.junit.BindingContextRule;
import com.netflix.type.protogen.BasicTypes.Country;
import java.util.Map;
import java.util.Optional;
import netflix.context.Context;
import netflix.context.auth.Auth;
import netflix.context.auth.AuthContext;
import netflix.context.geo.GeoContext;
import org.junit.Rule;
import org.junit.Test;

public class TestMicrocontextTest {

  @Rule public BindingContextRule bindingContextRule = new BindingContextRule();

  @Test
  public void headersEmpty() {
    final TestMicrocontext testMicrocontext = TestMicrocontext.builder().build();
    final Map<String, String> headers = testMicrocontext.asHeaders();
    assertNotNull(headers);
    assertTrue(headers.isEmpty());
  }

  @Test
  public void headersContextGeoAuth() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder()
            .setContext(Context.newBuilder().setRequestId("123").build())
            .setAuth(
                AuthContext.newBuilder()
                    .setCurrentAuth(
                        Auth.newBuilder()
                            .setCustomerId(123L)
                            .setCustomerGuid("some globally unique id"))
                    .setAppId("foo-app-id")
                    .build())
            .setGeo(GeoContext.newBuilder().setCountry(Country.newBuilder().setId("CA")).build())
            .build();
    final Map<String, String> headers = testMicrocontext.asHeaders();
    assertNotNull(headers);
    assertEquals(4, headers.size());
    CurrentRequestContext.set(RequestContext.fromKeySupplier(headers::get));
    final Microcontext microcontext = CurrentMicrocontext.get();
    microcontext.getGeo();
    final GeoContext geoContext = microcontext.getGeo();
    assertEquals("CA", geoContext.getCountry().getId());
    assertEquals("123", microcontext.getRequestId());
    assertEquals(123L, microcontext.getAuth().getCurrentAuth().getCustomerId());
    assertEquals("foo-app-id", microcontext.getAuth().getAppId().getValue());
  }

  @Test
  public void headersContextGeoPassport() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder()
            .setContext(Context.newBuilder().setRequestId("123").build())
            .setPassport(
                TestPassport.builder()
                    .userInfo(
                        TestUserInfo.builder()
                            .customerId(123L)
                            .customerGuid("some globally unique id")
                            .build())
                    .build())
            .setGeo(GeoContext.newBuilder().setCountry(Country.newBuilder().setId("CA")).build())
            .build();
    final Map<String, String> headers = testMicrocontext.asHeaders();
    assertNotNull(headers);
    assertEquals(4, headers.size());
    CurrentRequestContext.set(RequestContext.fromKeySupplier(headers::get));
    final Microcontext microcontext = CurrentMicrocontext.get();
    microcontext.getGeo();
    final GeoContext geoContext = microcontext.getGeo();
    assertEquals("CA", geoContext.getCountry().getId());
    assertEquals("123", microcontext.getRequestId());
    assertEquals(123L, microcontext.getAuth().getCurrentAuth().getCustomerId());
  }

  @Test
  public void testSerializedEmpty() {
    assertFalse(TestMicrocontext.builder().build().getSerializedMicrocontext().isPresent());
  }

  @Test
  public void testSerialized() {
    final TestMicrocontext testMicrocontext =
        TestMicrocontext.builder()
            .setContext(Context.newBuilder().setRequestId("123").build())
            .build();
    final Optional<String> serializedMicrocontext = testMicrocontext.getSerializedMicrocontext();
    assertTrue(serializedMicrocontext.isPresent());
    final Optional<Context> context = Serializers.fromString(serializedMicrocontext.get());
    assertTrue(context.isPresent());
    assertEquals("123", context.get().getBoxedRequestId());
  }

  @Test
  public void testBasic() {
    TestCurrentMicrocontext.set(TestMicrocontext.basic().build());
    final Microcontext microcontext = CurrentMicrocontext.get();
    assertNotNull(microcontext.getRequestId());
    final GeoContext geo = microcontext.getGeo();
    assertNotNull(geo);
    assertEquals("US", geo.getCountry().getId());
    assertEquals("CA", geo.getRegionCode().getValue());
    assertEquals(MicrocontextTestResolvers.CLIENT, microcontext.getClient());
    assertEquals(MicrocontextTestResolvers.DEVICE, microcontext.getDevice());
    assertEquals(MicrocontextTestResolvers.VISIT, microcontext.getVisit());
    assertEquals(MicrocontextTestResolvers.GEO_US.getCountry(), microcontext.getCountry());
    assertTrue(microcontext.getLocale().isPresent());
    assertEquals("en-US", microcontext.getLocale().get().getId());
  }
}
